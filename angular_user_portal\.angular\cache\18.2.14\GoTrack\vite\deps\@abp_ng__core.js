import {
  APP_INIT_ERROR_HANDLERS,
  AbpApiDefinitionService,
  AbpApplicationConfigurationService,
  AbpApplicationLocalizationService,
  AbpLocalStorageService,
  AbpTenantService,
  AbpTitleStrategy,
  AbpValidators,
  AbpWindowService,
  AbstractAuthErrorFilter,
  AbstractNavTreeService,
  AbstractNgModelComponent,
  AbstractTreeService,
  ApiInterceptor,
  AuditedEntityDto,
  AuditedEntityWithUserDto,
  AuthErrorEvent,
  AuthErrorFilterService,
  AuthEvent,
  AuthGuard,
  AuthInfoEvent,
  AuthService,
  AuthSuccessEvent,
  AutofocusDirective,
  BaseCoreModule,
  BaseTreeNode,
  CHECK_AUTHENTICATION_STATE_FN_KEY,
  CONTAINER_STRATEGY,
  CONTENT_SECURITY_STRATEGY,
  CONTENT_STRATEGY,
  CONTEXT_STRATEGY,
  COOKIE_LANGUAGE_KEY,
  CORE_OPTIONS,
  CROSS_ORIGIN_STRATEGY,
  ClearContainerStrategy,
  ComponentContextStrategy,
  ComponentProjectionStrategy,
  ConfigStateService,
  ContainerStrategy,
  ContentProjectionService,
  ContentSecurityStrategy,
  ContentStrategy,
  ContextStrategy,
  CookieLanguageProvider,
  CoreFeatureKind,
  CoreModule,
  CreationAuditedEntityDto,
  CreationAuditedEntityWithUserDto,
  CrossOriginStrategy,
  DEFAULT_DYNAMIC_LAYOUTS,
  DISABLE_PROJECT_NAME,
  DOM_STRATEGY,
  DYNAMIC_LAYOUTS_TOKEN,
  DefaultQueueManager,
  DomInsertionService,
  DomStrategy,
  DynamicLayoutComponent,
  EntityDto,
  EnvironmentService,
  ExtensibleAuditedEntityDto,
  ExtensibleAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityDto,
  ExtensibleCreationAuditedEntityWithUserDto,
  ExtensibleEntityDto,
  ExtensibleFullAuditedEntityDto,
  ExtensibleFullAuditedEntityWithUserDto,
  ExtensibleLimitedResultRequestDto,
  ExtensibleObject,
  ExtensiblePagedAndSortedResultRequestDto,
  ExtensiblePagedResultRequestDto,
  ExternalHttpClient,
  ForDirective,
  FormSubmitDirective,
  FullAuditedEntityDto,
  FullAuditedEntityWithUserDto,
  HttpErrorReporterService,
  HttpWaitService,
  INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  INJECTOR_PIPE_DATA_TOKEN,
  IS_EXTERNAL_REQUEST,
  IncludeLocalizationResourcesProvider,
  InitDirective,
  InputEventDebounceDirective,
  InsertIntoContainerStrategy,
  InternalStore,
  InternetConnectionService,
  LIST_QUERY_DEBOUNCE_TIME,
  LOADER_DELAY,
  LOADING_STRATEGY,
  LOCALIZATIONS,
  LazyLoadService,
  LazyModuleFactory,
  LimitedResultRequestDto,
  ListResultDto,
  ListService,
  LoadingStrategy,
  LocalStorageListenerService,
  LocaleId,
  LocaleProvider,
  LocalizationModule,
  LocalizationPipe,
  LocalizationService,
  LooseContentSecurityStrategy,
  MultiTenancyService,
  NAVIGATE_TO_MANAGE_PROFILE,
  NavigationEvent,
  NoContentSecurityStrategy,
  NoContextStrategy,
  NoCrossOriginStrategy,
  OTHERS_GROUP,
  PIPE_TO_LOGIN_FN_KEY,
  PROJECTION_STRATEGY,
  PagedAndSortedResultRequestDto,
  PagedResultDto,
  PagedResultRequestDto,
  PermissionDirective,
  PermissionGuard,
  PermissionService,
  ProjectionStrategy,
  QUEUE_MANAGER,
  ReplaceableComponentsService,
  ReplaceableRouteContainerComponent,
  ReplaceableTemplateDirective,
  ResourceWaitService,
  RestService,
  RootComponentProjectionStrategy,
  RootCoreModule,
  RouterEvents,
  RouterOutletComponent,
  RouterWaitService,
  RoutesService,
  SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY,
  SORT_COMPARE_FUNC,
  SafeHtmlPipe,
  ScriptContentStrategy,
  ScriptLoadingStrategy,
  SessionStateService,
  ShortDatePipe,
  ShortDateTimePipe,
  ShortTimePipe,
  ShowPasswordDirective,
  SortPipe,
  StopPropagationDirective,
  StyleContentStrategy,
  StyleLoadingStrategy,
  SubscriptionService,
  TENANT_KEY,
  TENANT_NOT_FOUND_BY_NAME,
  TemplateContextStrategy,
  TemplateProjectionStrategy,
  ToInjectorPipe,
  TrackByService,
  TrackCapsLockDirective,
  WebHttpUrlEncodingCodec,
  authGuard,
  checkHasProp,
  compareFuncFactory,
  coreOptionsFactory,
  createGroupMap,
  createLocalizationPipeKeyGenerator,
  createLocalizer,
  createLocalizerWithFallback,
  createMapFromList,
  createTokenParser,
  createTreeFromList,
  createTreeNodeFilterCreator,
  deepMerge,
  differentLocales,
  downloadBlob,
  escapeHtmlChars,
  exists,
  featuresFactory,
  findRoute,
  fromLazyLoad,
  generateHash,
  generatePassword,
  getInitialData,
  getLocaleDirection,
  getPathName,
  getRemoteEnv,
  getRoutePath,
  getShortDateFormat,
  getShortDateShortTimeFormat,
  getShortTimeFormat,
  index,
  interpolate,
  isArray,
  isNode,
  isNullOrEmpty,
  isNullOrUndefined,
  isNumber,
  isObject,
  isObjectAndNotArray,
  isObjectAndNotArrayNotNode,
  isUndefinedOrEmptyString,
  localeInitializer,
  localizationContributor,
  localizations$,
  mapEnumToOptions,
  noop,
  parseTenantFromUrl,
  permissionGuard,
  provideAbpCore,
  provideAbpCoreChild,
  pushValueTo,
  reloadRoute,
  setLanguageToCookie,
  trackBy,
  trackByDeep,
  uuid,
  validateCreditCard,
  validateMinAge,
  validateRange,
  validateRequired,
  validateStringLength,
  validateUniqueCharacter,
  validateUrl,
  withCompareFuncFactory,
  withOptions,
  withTitleStrategy
} from "./chunk-3NU57XZL.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-6D52GKB4.js";
import "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";
export {
  APP_INIT_ERROR_HANDLERS,
  AbpApiDefinitionService,
  AbpApplicationConfigurationService,
  AbpApplicationLocalizationService,
  AbpLocalStorageService,
  AbpTenantService,
  AbpTitleStrategy,
  AbpValidators,
  AbpWindowService,
  AbstractAuthErrorFilter,
  AbstractNavTreeService,
  AbstractNgModelComponent,
  AbstractTreeService,
  ApiInterceptor,
  AuditedEntityDto,
  AuditedEntityWithUserDto,
  AuthErrorEvent,
  AuthErrorFilterService,
  AuthEvent,
  AuthGuard,
  AuthInfoEvent,
  AuthService,
  AuthSuccessEvent,
  AutofocusDirective,
  BaseCoreModule,
  BaseTreeNode,
  CHECK_AUTHENTICATION_STATE_FN_KEY,
  CONTAINER_STRATEGY,
  CONTENT_SECURITY_STRATEGY,
  CONTENT_STRATEGY,
  CONTEXT_STRATEGY,
  COOKIE_LANGUAGE_KEY,
  CORE_OPTIONS,
  CROSS_ORIGIN_STRATEGY,
  ClearContainerStrategy,
  ComponentContextStrategy,
  ComponentProjectionStrategy,
  ConfigStateService,
  ContainerStrategy,
  ContentProjectionService,
  ContentSecurityStrategy,
  ContentStrategy,
  ContextStrategy,
  CookieLanguageProvider,
  CoreFeatureKind,
  CoreModule,
  CreationAuditedEntityDto,
  CreationAuditedEntityWithUserDto,
  CrossOriginStrategy,
  DEFAULT_DYNAMIC_LAYOUTS,
  DISABLE_PROJECT_NAME,
  DOM_STRATEGY,
  DYNAMIC_LAYOUTS_TOKEN,
  DefaultQueueManager,
  DomInsertionService,
  DomStrategy,
  DynamicLayoutComponent,
  EntityDto,
  EnvironmentService,
  ExtensibleAuditedEntityDto,
  ExtensibleAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityDto,
  ExtensibleCreationAuditedEntityWithUserDto,
  ExtensibleEntityDto,
  ExtensibleFullAuditedEntityDto,
  ExtensibleFullAuditedEntityWithUserDto,
  ExtensibleLimitedResultRequestDto,
  ExtensibleObject,
  ExtensiblePagedAndSortedResultRequestDto,
  ExtensiblePagedResultRequestDto,
  ExternalHttpClient,
  ForDirective,
  FormSubmitDirective,
  FullAuditedEntityDto,
  FullAuditedEntityWithUserDto,
  HttpErrorReporterService,
  HttpWaitService,
  INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  INJECTOR_PIPE_DATA_TOKEN,
  IS_EXTERNAL_REQUEST,
  IncludeLocalizationResourcesProvider,
  InitDirective,
  InputEventDebounceDirective,
  InsertIntoContainerStrategy,
  InternalStore,
  InternetConnectionService,
  LIST_QUERY_DEBOUNCE_TIME,
  LOADER_DELAY,
  LOADING_STRATEGY,
  LOCALIZATIONS,
  LazyLoadService,
  LazyModuleFactory,
  LimitedResultRequestDto,
  ListResultDto,
  ListService,
  LoadingStrategy,
  LocalStorageListenerService,
  LocaleId,
  LocaleProvider,
  LocalizationModule,
  LocalizationPipe,
  LocalizationService,
  LooseContentSecurityStrategy,
  MultiTenancyService,
  NAVIGATE_TO_MANAGE_PROFILE,
  NavigationEvent,
  NoContentSecurityStrategy,
  NoContextStrategy,
  NoCrossOriginStrategy,
  OTHERS_GROUP,
  index as ObjectExtending,
  PIPE_TO_LOGIN_FN_KEY,
  PROJECTION_STRATEGY,
  PagedAndSortedResultRequestDto,
  PagedResultDto,
  PagedResultRequestDto,
  PermissionDirective,
  PermissionGuard,
  PermissionService,
  ProjectionStrategy,
  QUEUE_MANAGER,
  ReplaceableComponentsService,
  ReplaceableRouteContainerComponent,
  ReplaceableTemplateDirective,
  ResourceWaitService,
  RestService,
  RootComponentProjectionStrategy,
  RootCoreModule,
  RouterEvents,
  RouterOutletComponent,
  RouterWaitService,
  RoutesService,
  SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY,
  SORT_COMPARE_FUNC,
  SafeHtmlPipe,
  ScriptContentStrategy,
  ScriptLoadingStrategy,
  SessionStateService,
  ShortDatePipe,
  ShortDateTimePipe,
  ShortTimePipe,
  ShowPasswordDirective,
  SortPipe,
  StopPropagationDirective,
  StyleContentStrategy,
  StyleLoadingStrategy,
  SubscriptionService,
  TENANT_KEY,
  TENANT_NOT_FOUND_BY_NAME,
  TemplateContextStrategy,
  TemplateProjectionStrategy,
  ToInjectorPipe,
  TrackByService,
  TrackCapsLockDirective,
  WebHttpUrlEncodingCodec,
  authGuard,
  checkHasProp,
  compareFuncFactory,
  coreOptionsFactory,
  createGroupMap,
  createLocalizationPipeKeyGenerator,
  createLocalizer,
  createLocalizerWithFallback,
  createMapFromList,
  createTokenParser,
  createTreeFromList,
  createTreeNodeFilterCreator,
  deepMerge,
  differentLocales,
  downloadBlob,
  escapeHtmlChars,
  exists,
  featuresFactory,
  findRoute,
  fromLazyLoad,
  generateHash,
  generatePassword,
  getInitialData,
  getLocaleDirection,
  getPathName,
  getRemoteEnv,
  getRoutePath,
  getShortDateFormat,
  getShortDateShortTimeFormat,
  getShortTimeFormat,
  interpolate,
  isArray,
  isNode,
  isNullOrEmpty,
  isNullOrUndefined,
  isNumber,
  isObject,
  isObjectAndNotArray,
  isObjectAndNotArrayNotNode,
  isUndefinedOrEmptyString,
  localeInitializer,
  localizationContributor,
  localizations$,
  mapEnumToOptions,
  noop,
  parseTenantFromUrl,
  permissionGuard,
  provideAbpCore,
  provideAbpCoreChild,
  pushValueTo,
  reloadRoute,
  setLanguageToCookie,
  trackBy,
  trackByDeep,
  uuid,
  validateCreditCard,
  validateMinAge,
  validateRange,
  validateRequired,
  validateStringLength,
  validateUniqueCharacter,
  validateUrl,
  withCompareFuncFactory,
  withOptions,
  withTitleStrategy
};
//# sourceMappingURL=@abp_ng__core.js.map
