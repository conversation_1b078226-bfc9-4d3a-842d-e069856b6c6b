import {
  differentLocales
} from "./chunk-3NU57XZL.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-6D52GKB4.js";
import {
  isDevMode
} from "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import {
  __async,
  __spreadValues
} from "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.core/fesm2022/abp-ng.core-locale.mjs
var localeMap = {};
function loadLocale(locale) {
  const list = {
    "ar": () => import("./@angular_common_locales_ar.js"),
    "cs": () => import("./cs-RQCHN4KV.js"),
    "en": () => import("./en-BOH34UFO.js"),
    "en-GB": () => import("./en-GB-37BB7OXI.js"),
    "es": () => import("./es-K3SETSJP.js"),
    "de": () => import("./de-T3JQHPWF.js"),
    "fi": () => import("./fi-LELSLNNH.js"),
    "fr": () => import("./fr-EZG4J5GB.js"),
    "hi": () => import("./hi-7A3J6NA7.js"),
    "hu": () => import("./hu-XMEFG6ZX.js"),
    "is": () => import("./is-BFMR7C6V.js"),
    "it": () => import("./it-5N3SEMR5.js"),
    "pt": () => import("./pt-TVYEKV3L.js"),
    "tr": () => import("./tr-U4CQM4BW.js"),
    "ru": () => import("./ru-7UB4AM2Y.js"),
    "ro": () => import("./ro-JVZPQ4FW.js"),
    "sk": () => import("./sk-K4BVR6SK.js"),
    "sl": () => import("./sl-UV4KWF3Q.js"),
    "zh-Hans": () => import("./zh-Hans-OEQB3H6E.js"),
    "zh-Hant": () => import("./zh-Hant-GKNFFOCH.js")
  };
  return list[locale]();
}
function registerLocaleForEsBuild({
  cultureNameLocaleFileMap = {},
  errorHandlerFn = defaultLocalErrorHandlerFn
} = {}) {
  return (locale) => {
    localeMap = __spreadValues(__spreadValues({}, differentLocales), cultureNameLocaleFileMap);
    const l = localeMap[locale] || locale;
    const localeSupportList = "ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant".split("|");
    if (localeSupportList.indexOf(locale) == -1) {
      return;
    }
    return new Promise((resolve, reject) => {
      return loadLocale(l).then((val) => {
        let module = val;
        while (module.default) {
          module = module.default;
        }
        resolve({
          default: module
        });
      }).catch((error) => {
        errorHandlerFn({
          resolve,
          reject,
          error,
          locale
        });
      });
    });
  };
}
function registerLocale({
  cultureNameLocaleFileMap = {},
  errorHandlerFn = defaultLocalErrorHandlerFn
} = {}) {
  return (locale) => {
    localeMap = __spreadValues(__spreadValues({}, differentLocales), cultureNameLocaleFileMap);
    const localePath = `/locales/${localeMap[locale] || locale}`;
    return new Promise((resolve, reject) => {
      return import(
        /* webpackMode: "lazy-once" */
        /* webpackChunkName: "locales"*/
        /* webpackInclude: /[/\\](ar|cs|en|en-GB|es|de|fi|fr|hi|hu|is|it|pt|tr|ru|ro|sk|sl|zh-Hans|zh-Hant)\.(mjs|js)$/ */
        /* webpackExclude: /[/\\]global|extra/ */
        `@angular/common${localePath}`
      ).then((val) => {
        let module = val;
        while (module.default) {
          module = module.default;
        }
        resolve({
          default: module
        });
      }).catch((error) => {
        errorHandlerFn({
          resolve,
          reject,
          error,
          locale
        });
      });
    });
  };
}
var extraLocales = {};
function storeLocaleData(data, localeId) {
  extraLocales[localeId] = data;
}
function defaultLocalErrorHandlerFn(_0) {
  return __async(this, arguments, function* ({
    locale,
    resolve
  }) {
    if (extraLocales[locale]) {
      resolve({
        default: extraLocales[localeMap[locale] || locale]
      });
      return;
    }
    if (isDevMode()) {
      console.error(`Cannot find the ${locale} locale file. You can check how can add new culture at https://abp.io/docs/latest/framework/ui/angular/localization#adding-a-new-culture`);
    }
    resolve();
  });
}
export {
  defaultLocalErrorHandlerFn,
  registerLocale,
  registerLocaleForEsBuild,
  storeLocaleData
};
//# sourceMappingURL=@abp_ng__core_locale.js.map
