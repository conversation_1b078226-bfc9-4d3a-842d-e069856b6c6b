import {
  RestService
} from "./chunk-3NU57XZL.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-6D52GKB4.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.identity/fesm2022/abp-ng.identity-proxy.mjs
var IdentityRoleService = class _IdentityRoleService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpIdentity";
    this.create = (input) => this.restService.request({
      method: "POST",
      url: "/api/identity/roles",
      body: input
    }, {
      apiName: this.apiName
    });
    this.delete = (id) => this.restService.request({
      method: "DELETE",
      url: `/api/identity/roles/${id}`
    }, {
      apiName: this.apiName
    });
    this.get = (id) => this.restService.request({
      method: "GET",
      url: `/api/identity/roles/${id}`
    }, {
      apiName: this.apiName
    });
    this.getAllList = () => this.restService.request({
      method: "GET",
      url: "/api/identity/roles/all"
    }, {
      apiName: this.apiName
    });
    this.getList = (input) => this.restService.request({
      method: "GET",
      url: "/api/identity/roles",
      params: {
        filter: input.filter,
        sorting: input.sorting,
        skipCount: input.skipCount,
        maxResultCount: input.maxResultCount
      }
    }, {
      apiName: this.apiName
    });
    this.update = (id, input) => this.restService.request({
      method: "PUT",
      url: `/api/identity/roles/${id}`,
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function IdentityRoleService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IdentityRoleService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _IdentityRoleService,
      factory: _IdentityRoleService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IdentityRoleService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var IdentityUserLookupService = class _IdentityUserLookupService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpIdentity";
    this.findById = (id) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/lookup/${id}`
    }, {
      apiName: this.apiName
    });
    this.findByUserName = (userName) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/lookup/by-username/${userName}`
    }, {
      apiName: this.apiName
    });
    this.getCount = (input) => this.restService.request({
      method: "GET",
      url: "/api/identity/users/lookup/count",
      params: {
        filter: input.filter
      }
    }, {
      apiName: this.apiName
    });
    this.search = (input) => this.restService.request({
      method: "GET",
      url: "/api/identity/users/lookup/search",
      params: {
        filter: input.filter,
        sorting: input.sorting,
        skipCount: input.skipCount,
        maxResultCount: input.maxResultCount
      }
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function IdentityUserLookupService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IdentityUserLookupService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _IdentityUserLookupService,
      factory: _IdentityUserLookupService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IdentityUserLookupService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var IdentityUserService = class _IdentityUserService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpIdentity";
    this.create = (input) => this.restService.request({
      method: "POST",
      url: "/api/identity/users",
      body: input
    }, {
      apiName: this.apiName
    });
    this.delete = (id) => this.restService.request({
      method: "DELETE",
      url: `/api/identity/users/${id}`
    }, {
      apiName: this.apiName
    });
    this.findByEmail = (email) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/by-email/${email}`
    }, {
      apiName: this.apiName
    });
    this.findByUsername = (userName) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/by-username/${userName}`
    }, {
      apiName: this.apiName
    });
    this.get = (id) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/${id}`
    }, {
      apiName: this.apiName
    });
    this.getAssignableRoles = () => this.restService.request({
      method: "GET",
      url: "/api/identity/users/assignable-roles"
    }, {
      apiName: this.apiName
    });
    this.getList = (input) => this.restService.request({
      method: "GET",
      url: "/api/identity/users",
      params: {
        filter: input.filter,
        sorting: input.sorting,
        skipCount: input.skipCount,
        maxResultCount: input.maxResultCount
      }
    }, {
      apiName: this.apiName
    });
    this.getRoles = (id) => this.restService.request({
      method: "GET",
      url: `/api/identity/users/${id}/roles`
    }, {
      apiName: this.apiName
    });
    this.update = (id, input) => this.restService.request({
      method: "PUT",
      url: `/api/identity/users/${id}`,
      body: input
    }, {
      apiName: this.apiName
    });
    this.updateRoles = (id, input) => this.restService.request({
      method: "PUT",
      url: `/api/identity/users/${id}/roles`,
      body: input
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function IdentityUserService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IdentityUserService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _IdentityUserService,
      factory: _IdentityUserService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IdentityUserService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
export {
  IdentityRoleService,
  IdentityUserLookupService,
  IdentityUserService
};
//# sourceMappingURL=@abp_ng__identity_proxy.js.map
