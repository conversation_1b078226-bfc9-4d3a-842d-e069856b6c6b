import {
  EmailSettingGroupComponent,
  EmailSettingsService,
  SETTING_MANAGEMENT_FEATURES,
  SETTING_MANAGEMENT_FEATURES_PROVIDERS,
  SETTING_MANAGEMENT_HAS_SETTING,
  SETTING_MANAGEMENT_ROUTE_PROVIDERS,
  SETTING_MANAGEMENT_ROUTE_VISIBILITY,
  SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS,
  SETTING_MANAGEMENT_VISIBLE_PROVIDERS,
  SettingManagementConfigModule,
  SettingTabsService,
  configureRoutes,
  configureSettingTabs,
  provideSettingManagementConfig,
  setSettingManagementVisibility
} from "./chunk-66VPTTH6.js";
import "./chunk-K3MSRIRI.js";
import "./chunk-XJCLBUIC.js";
import "./chunk-WXRVJEAW.js";
import "./chunk-3NU57XZL.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-6D52GKB4.js";
import "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";
export {
  EmailSettingGroupComponent,
  EmailSettingsService,
  SETTING_MANAGEMENT_FEATURES,
  SETTING_MANAGEMENT_FEATURES_PROVIDERS,
  SETTING_MANAGEMENT_HAS_SETTING,
  SETTING_MANAGEMENT_ROUTE_PROVIDERS,
  SETTING_MANAGEMENT_ROUTE_VISIBILITY,
  SETTING_MANAGEMENT_SETTING_TAB_PROVIDERS,
  SETTING_MANAGEMENT_VISIBLE_PROVIDERS,
  SettingManagementConfigModule,
  SettingTabsService,
  configureRoutes,
  configureSettingTabs,
  provideSettingManagementConfig,
  setSettingManagementVisibility
};
//# sourceMappingURL=@abp_ng__setting-management_config.js.map
