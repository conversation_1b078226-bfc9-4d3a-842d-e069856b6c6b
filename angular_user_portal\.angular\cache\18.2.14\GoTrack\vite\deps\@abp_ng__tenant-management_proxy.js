import {
  RestService
} from "./chunk-3NU57XZL.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-6D52GKB4.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.tenant-management/fesm2022/abp-ng.tenant-management-proxy.mjs
var TenantService = class _TenantService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "AbpTenantManagement";
    this.create = (input) => this.restService.request({
      method: "POST",
      url: "/api/multi-tenancy/tenants",
      body: input
    }, {
      apiName: this.apiName
    });
    this.delete = (id) => this.restService.request({
      method: "DELETE",
      url: `/api/multi-tenancy/tenants/${id}`
    }, {
      apiName: this.apiName
    });
    this.deleteDefaultConnectionString = (id) => this.restService.request({
      method: "DELETE",
      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`
    }, {
      apiName: this.apiName
    });
    this.get = (id) => this.restService.request({
      method: "GET",
      url: `/api/multi-tenancy/tenants/${id}`
    }, {
      apiName: this.apiName
    });
    this.getDefaultConnectionString = (id) => this.restService.request({
      method: "GET",
      responseType: "text",
      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`
    }, {
      apiName: this.apiName
    });
    this.getList = (input) => this.restService.request({
      method: "GET",
      url: "/api/multi-tenancy/tenants",
      params: {
        filter: input.filter,
        sorting: input.sorting,
        skipCount: input.skipCount,
        maxResultCount: input.maxResultCount
      }
    }, {
      apiName: this.apiName
    });
    this.update = (id, input) => this.restService.request({
      method: "PUT",
      url: `/api/multi-tenancy/tenants/${id}`,
      body: input
    }, {
      apiName: this.apiName
    });
    this.updateDefaultConnectionString = (id, defaultConnectionString) => this.restService.request({
      method: "PUT",
      url: `/api/multi-tenancy/tenants/${id}/default-connection-string`,
      params: {
        defaultConnectionString
      }
    }, {
      apiName: this.apiName
    });
  }
  static {
    this.ɵfac = function TenantService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TenantService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TenantService,
      factory: _TenantService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TenantService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
export {
  TenantService
};
//# sourceMappingURL=@abp_ng__tenant-management_proxy.js.map
