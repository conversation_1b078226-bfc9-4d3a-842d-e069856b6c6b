import {
  outputFromObservable,
  outputToObservable,
  takeUntilDestroyed,
  toObservable,
  toSignal
} from "./chunk-SQ2XSFGA.js";
import "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";
export {
  outputFromObservable,
  outputToObservable,
  takeUntilDestroyed,
  toObservable,
  toSignal
};
//# sourceMappingURL=@angular_core_rxjs-interop.js.map
