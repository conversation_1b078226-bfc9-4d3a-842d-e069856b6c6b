import {
  DEFAULT_ENTRY_NAME,
  FirebaseApp,
  FirebaseAppModule,
  FirebaseApps,
  FirebaseError,
  SDK_VERSION,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp2 as getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion2 as registerVersion,
  setLogLevel
} from "./chunk-7HXM6DK5.js";
import "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";
export {
  FirebaseApp,
  FirebaseAppModule,
  FirebaseApps,
  FirebaseError,
  SDK_VERSION,
  DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion,
  setLogLevel
};
//# sourceMappingURL=@angular_fire_app.js.map
