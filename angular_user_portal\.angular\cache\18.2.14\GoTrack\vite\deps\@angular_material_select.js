import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-OQ3JYWFY.js";
import "./chunk-Y5AGGM2N.js";
import "./chunk-SISCE4G2.js";
import "./chunk-ASZRR2AS.js";
import "./chunk-P7EARM5A.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>orm<PERSON>ield,
  <PERSON><PERSON><PERSON>,
  MatLabel,
  MatPrefix,
  MatSuffix
} from "./chunk-OZQQ7DCO.js";
import "./chunk-K2577LFK.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-L3STFTJR.js";
import "./chunk-JQS6LOEL.js";
import "./chunk-WXRVJEAW.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-6D52GKB4.js";
import "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
