import {
  MAT_INPUT_VALUE_ACCESSOR,
  MatInput,
  MatInputModule
} from "./chunk-YLNCLJDS.js";
import {
  MAT_FAB_DEFAULT_OPTIONS,
  MatButton,
  MatButtonModule,
  MatIconButton,
  MatMiniFabButton
} from "./chunk-KQFENTBY.js";
import {
  FlexibleConnectedPositionStrategy,
  Overlay,
  OverlayConfig,
  OverlayModule
} from "./chunk-Y5AGGM2N.js";
import {
  CdkScrollableModule
} from "./chunk-SISCE4G2.js";
import {
  CdkPortalOutlet,
  ComponentPortal,
  PortalModule,
  TemplatePortal
} from "./chunk-ASZRR2AS.js";
import "./chunk-P7EARM5A.js";
import {
  MatDivider,
  MatDividerModule
} from "./chunk-HY5YGOKM.js";
import {
  MAT_FORM_FIELD,
  Mat<PERSON><PERSON><PERSON>ield,
  MatFormFieldModule,
  MatHint
} from "./chunk-OZQQ7DCO.js";
import "./chunk-K2577LFK.js";
import {
  MatR<PERSON>ple,
  MatRippleModule
} from "./chunk-L3STFTJR.js";
import {
  A11yModule,
  CdkTrapFocus,
  DOWN_ARROW,
  ESCAPE,
  PAGE_DOWN,
  PAGE_UP,
  PlatformModule,
  UP_ARROW,
  _getFocusedElementPierceShadowDom,
  coerceBooleanProperty,
  coerceNumberProperty,
  hasModifierKey
} from "./chunk-JQS6LOEL.js";
import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger
} from "./chunk-WXRVJEAW.js";
import {
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  Validators
} from "./chunk-JP2LMHJE.js";
import {
  CommonModule,
  DOCUMENT,
  NgIf,
  NgStyle
} from "./chunk-6D52GKB4.js";
import {
  Attribute,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Injectable,
  InjectionToken,
  Input,
  LOCALE_ID,
  NgModule,
  NgZone,
  Optional,
  Output,
  SkipSelf,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation$1,
  booleanAttribute,
  forwardRef,
  inject,
  setClassMetadata,
  signal,
  ɵɵInheritDefinitionFeature,
  ɵɵInputTransformsFeature,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵgetInheritedFactory,
  ɵɵhostProperty,
  ɵɵinject,
  ɵɵinjectAttribute,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵsyntheticHostListener,
  ɵɵsyntheticHostProperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵviewQuery
} from "./chunk-QGPYGS5J.js";
import "./chunk-BTHIXAM7.js";
import {
  fromEvent,
  merge
} from "./chunk-GJSJXBTC.js";
import {
  BehaviorSubject,
  Subject,
  Subscription,
  debounceTime,
  filter,
  first,
  of,
  take
} from "./chunk-DJECZSZD.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-ZTELYOIP.js";

// node_modules/@dhutaryan/ngx-mat-timepicker/fesm2022/dhutaryan-ngx-mat-timepicker.mjs
var _c0 = [[["", "matTimepickerToggleIcon", ""]]];
var _c1 = ["[matTimepickerToggleIcon]"];
function MatTimepickerToggle_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵnamespaceSVG();
    ɵɵelementStart(0, "svg", 2);
    ɵɵelement(1, "path", 3);
    ɵɵelementEnd();
  }
}
var _forTrack0 = ($index, $item) => $item.value;
function MatMinutesClockDial_For_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "button", 3);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const minute_r1 = ctx.$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵstyleProp("left", minute_r1.left, "px")("top", minute_r1.top, "px");
    ɵɵclassProp("mat-clock-dial-cell-active", ctx_r1._isActiveCell(minute_r1.value))("mat-clock-dial-cell-disabled", minute_r1.disabled);
    ɵɵproperty("tabIndex", ctx_r1._isActiveCell(minute_r1.value) ? 0 : -1)("color", ctx_r1._isActiveCell(minute_r1.value) ? ctx_r1.color : void 0);
    ɵɵattribute("aria-disabled", minute_r1.disabled || null);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", minute_r1.displayValue, " ");
  }
}
function MatHoursClockDial_For_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "button", 2);
    ɵɵtext(1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const hour_r1 = ctx.$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵstyleProp("left", hour_r1.left, "px")("top", hour_r1.top, "px");
    ɵɵclassProp("mat-clock-dial-cell-active", ctx_r1._isActiveCell(hour_r1.value))("mat-clock-dial-cell-disabled", hour_r1.disabled);
    ɵɵproperty("tabIndex", ctx_r1._isActiveCell(hour_r1.value) ? 0 : -1)("color", ctx_r1._isActiveCell(hour_r1.value) ? ctx_r1.color : void 0);
    ɵɵattribute("aria-disabled", hour_r1.disabled || null);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", hour_r1.displayValue, " ");
  }
}
var _c2 = '.mat-clock-dial{position:relative;display:block;width:16rem;height:16rem;margin:0 auto;border-radius:50%}.mat-clock-dial:before{position:absolute;top:50%;left:50%;width:.4375rem;height:.4375rem;border-radius:50%;transform:translate(-50%,-50%);content:""}[mat-mini-fab].mat-clock-dial-cell{position:absolute;display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;border-radius:50%;box-shadow:none}[mat-mini-fab].mat-clock-dial-cell:disabled{pointer-events:none}[mat-mini-fab].mat-clock-dial-cell:focus,[mat-mini-fab].mat-clock-dial-cell:hover,[mat-mini-fab].mat-clock-dial-cell:active,[mat-mini-fab].mat-clock-dial-cell:focus:active{box-shadow:none}.mat-timepicker-content-touch .mat-clock-dial{width:20rem;height:20rem}.mat-timepicker-content-touch [mat-mini-fab].mat-clock-dial-cell{width:3rem;height:3rem;font-size:1.125rem}.mat-clock-dial-hand{position:absolute;inset:0;width:1px;margin:0 auto;transform-origin:bottom}.mat-clock-dial-hand:before{position:absolute;top:-.25rem;left:-.25rem;width:calc(.5rem + 1px);height:calc(.5rem + 1px);border-radius:50%;content:""}.mat-clock-dial-hand.mat-clock-dial-hand-pointless:before{content:none}\n';
var _c3 = [[["", "hours", ""]], [["", "minutes", ""]], [["", "mat-time-period", ""]], "*"];
var _c4 = ["[hours]", "[minutes]", "[mat-time-period]", "*"];
function MatClockDials_ng_template_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-time-period", 7);
    ɵɵlistener("periodChanged", function MatClockDials_ng_template_5_Template_mat_time_period_periodChanged_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onPeriodChanged($event));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("period", ctx_r1.period)("disabledPeriod", ctx_r1.disabledPeriod)("vertical", ctx_r1.orientation === "vertical");
  }
}
function MatClockDials_Conditional_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-hours-clock-dial", 8);
    ɵɵlistener("selectedChange", function MatClockDials_Conditional_7_Template_mat_hours_clock_dial_selectedChange_0_listener($event) {
      ɵɵrestoreView(_r3);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onHourChanged($event));
    })("keydown", function MatClockDials_Conditional_7_Template_mat_hours_clock_dial_keydown_0_listener($event) {
      ɵɵrestoreView(_r3);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onKeydown($event, "hour"));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("@enterLeaveAnimation", void 0)("color", ctx_r1.color)("selectedHour", ctx_r1.selectedHour)("isMeridiem", ctx_r1.isMeridiem)("availableHours", ctx_r1._getAvailableHours())("touchUi", ctx_r1.touchUi);
  }
}
function MatClockDials_Conditional_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-minutes-clock-dial", 9);
    ɵɵlistener("selectedChange", function MatClockDials_Conditional_8_Template_mat_minutes_clock_dial_selectedChange_0_listener($event) {
      ɵɵrestoreView(_r4);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onMinuteSelected($event));
    })("keydown", function MatClockDials_Conditional_8_Template_mat_minutes_clock_dial_keydown_0_listener($event) {
      ɵɵrestoreView(_r4);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onKeydown($event, "minute"));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("@enterLeaveAnimation", void 0)("color", ctx_r1.color)("selectedMinute", ctx_r1.selectedMinute)("interval", ctx_r1.minuteInterval)("availableMinutes", ctx_r1.availableMinutes)("touchUi", ctx_r1.touchUi);
  }
}
function MatTimeInputs_ng_template_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-time-period", 6);
    ɵɵlistener("periodChanged", function MatTimeInputs_ng_template_9_Template_mat_time_period_periodChanged_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._onPeriodChanged($event));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("period", ctx_r1.period)("disabledPeriod", ctx_r1.disabledPeriod);
  }
}
function MatTimepickerContent_Case_1_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 6);
    ɵɵlistener("click", function MatTimepickerContent_Case_1_Conditional_2_Template_button_click_0_listener() {
      ɵɵrestoreView(_r3);
      const ctx_r1 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r1.onToggleMode("dial"));
    });
    ɵɵnamespaceSVG();
    ɵɵelementStart(1, "svg", 7);
    ɵɵelement(2, "path", 8);
    ɵɵelementEnd()();
  }
}
function MatTimepickerContent_Case_1_ng_template_3_Template(rf, ctx) {
}
function MatTimepickerContent_Case_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-time-inputs", 2);
    ɵɵlistener("_userSelection", function MatTimepickerContent_Case_1_Template_mat_time_inputs__userSelection_0_listener($event) {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._handleUserSelection($event));
    });
    ɵɵelementEnd();
    ɵɵelementStart(1, "div", 3);
    ɵɵtemplate(2, MatTimepickerContent_Case_1_Conditional_2_Template, 3, 0, "button", 4)(3, MatTimepickerContent_Case_1_ng_template_3_Template, 0, 0, "ng-template", 5);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("id", ctx_r1.timepicker.id)("color", ctx_r1.color)("isMeridiem", ctx_r1.isMeridiem)("selected", ctx_r1._getSelected())("minTime", ctx_r1.timepicker._getMinTime())("maxTime", ctx_r1.timepicker._getMaxTime())("minuteInterval", ctx_r1.minuteInterval);
    ɵɵadvance(2);
    ɵɵconditional(ctx_r1.showToggleModeButton ? 2 : -1);
    ɵɵadvance();
    ɵɵproperty("cdkPortalOutlet", ctx_r1._actionsPortal);
  }
}
function MatTimepickerContent_Case_2_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 6);
    ɵɵlistener("click", function MatTimepickerContent_Case_2_Conditional_2_Template_button_click_0_listener() {
      ɵɵrestoreView(_r5);
      const ctx_r1 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r1.onToggleMode("input"));
    });
    ɵɵnamespaceSVG();
    ɵɵelementStart(1, "svg", 7);
    ɵɵelement(2, "path", 10);
    ɵɵelementEnd()();
  }
}
function MatTimepickerContent_Case_2_ng_template_3_Template(rf, ctx) {
}
function MatTimepickerContent_Case_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "mat-clock-dials", 9);
    ɵɵlistener("_userSelection", function MatTimepickerContent_Case_2_Template_mat_clock_dials__userSelection_0_listener($event) {
      ɵɵrestoreView(_r4);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1._handleUserSelection($event));
    });
    ɵɵelementEnd();
    ɵɵelementStart(1, "div", 3);
    ɵɵtemplate(2, MatTimepickerContent_Case_2_Conditional_2_Template, 3, 0, "button", 4)(3, MatTimepickerContent_Case_2_ng_template_3_Template, 0, 0, "ng-template", 5);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("id", ctx_r1.timepicker.id)("color", ctx_r1.color)("isMeridiem", ctx_r1.isMeridiem)("selected", ctx_r1._getSelected())("minTime", ctx_r1.timepicker._getMinTime())("maxTime", ctx_r1.timepicker._getMaxTime())("minuteInterval", ctx_r1.minuteInterval)("orientation", ctx_r1.orientation)("touchUi", ctx_r1.timepicker.touchUi);
    ɵɵadvance(2);
    ɵɵconditional(ctx_r1.showToggleModeButton ? 2 : -1);
    ɵɵadvance();
    ɵɵproperty("cdkPortalOutlet", ctx_r1._actionsPortal);
  }
}
var _c5 = ["*"];
function MatTimepickerActions_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 0);
    ɵɵprojection(1);
    ɵɵelementEnd();
  }
}
var _c6 = ".mat-timepicker-actions-container{margin-left:auto}.mat-timepicker-actions{display:flex;gap:.5rem;align-items:center;margin-top:.5rem}\n";
var MAT_TIMEPICKER_SCROLL_STRATEGY = new InjectionToken("mat-timepicker-scroll-strategy");
function MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY(overlay) {
  return () => overlay.scrollStrategies.reposition();
}
var MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER = {
  provide: MAT_TIMEPICKER_SCROLL_STRATEGY,
  deps: [Overlay],
  useFactory: MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY
};
var MatTimepickerIntl = class _MatTimepickerIntl {
  constructor() {
    this.changes = new Subject();
    this.inputsTitle = "Enter time";
    this.dialsTitle = "Select time";
    this.hourInputHint = "Hour";
    this.minuteInputHint = "Minute";
    this.openTimepickerLabel = "Open timepicker";
    this.closeTimepickerLabel = "Close timepicker";
    this.okButton = "OK";
    this.cancelButton = "Cancel";
    this.am = "AM";
    this.pm = "PM";
  }
  static {
    this.ɵfac = function MatTimepickerIntl_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerIntl)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _MatTimepickerIntl,
      factory: _MatTimepickerIntl.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerIntl, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var MatTimepickerToggleIcon = class _MatTimepickerToggleIcon {
  static {
    this.ɵfac = function MatTimepickerToggleIcon_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerToggleIcon)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerToggleIcon,
      selectors: [["", "matTimepickerToggleIcon", ""]],
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerToggleIcon, [{
    type: Directive,
    args: [{
      selector: "[matTimepickerToggleIcon]",
      standalone: true
    }]
  }], null, null);
})();
var MatTimepickerToggle = class _MatTimepickerToggle {
  /** Whether the toggle button is disabled. */
  get disabled() {
    if (this._disabled === void 0 && this.timepicker) {
      return this.timepicker.disabled;
    }
    return !!this._disabled;
  }
  set disabled(value) {
    this._disabled = coerceBooleanProperty(value);
  }
  constructor(defaultTabIndex, _intl, _cdr) {
    this._intl = _intl;
    this._cdr = _cdr;
    this._stateChanges = Subscription.EMPTY;
    const parsedTabIndex = Number(defaultTabIndex);
    this.tabIndex = parsedTabIndex || parsedTabIndex === 0 ? parsedTabIndex : null;
  }
  ngOnChanges(changes) {
    if (changes["timepicker"]) {
      this._watchStateChanges();
    }
  }
  ngOnDestroy() {
    this._stateChanges.unsubscribe();
  }
  /** Opens timepicker. */
  open(event) {
    if (this.timepicker && !this.disabled) {
      this.timepicker.open();
      event.stopPropagation();
    }
  }
  _watchStateChanges() {
    const timepickerStateChanged = this.timepicker ? this.timepicker.stateChanges : of();
    const inputStateChanged = this.timepicker && this.timepicker.timepickerInput ? this.timepicker.timepickerInput.stateChanges : of();
    const timepickerToggled = this.timepicker ? merge(this.timepicker.openedStream, this.timepicker.closedStream) : of();
    this._stateChanges.unsubscribe();
    this._stateChanges = merge(this._intl.changes, timepickerStateChanged, inputStateChanged, timepickerToggled).subscribe(() => this._cdr.markForCheck());
  }
  static {
    this.ɵfac = function MatTimepickerToggle_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerToggle)(ɵɵinjectAttribute("tabindex"), ɵɵdirectiveInject(MatTimepickerIntl), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepickerToggle,
      selectors: [["mat-timepicker-toggle"]],
      contentQueries: function MatTimepickerToggle_ContentQueries(rf, ctx, dirIndex) {
        if (rf & 1) {
          ɵɵcontentQuery(dirIndex, MatTimepickerToggleIcon, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.customIcon = _t.first);
        }
      },
      hostAttrs: [1, "mat-timepicker-toggle"],
      hostVars: 7,
      hostBindings: function MatTimepickerToggle_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function MatTimepickerToggle_click_HostBindingHandler($event) {
            return ctx.open($event);
          });
        }
        if (rf & 2) {
          ɵɵattribute("tabindex", null);
          ɵɵclassProp("mat-timepicker-toggle-active", ctx.timepicker && ctx.timepicker.opened)("mat-accent", ctx.timepicker && ctx.timepicker.color === "accent")("mat-warn", ctx.timepicker && ctx.timepicker.color === "warn");
        }
      },
      inputs: {
        timepicker: [0, "for", "timepicker"],
        disabled: "disabled",
        disableRipple: "disableRipple",
        tabIndex: "tabIndex",
        ariaLabel: [0, "aria-label", "ariaLabel"]
      },
      exportAs: ["matTimepickerToggle"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      ngContentSelectors: _c1,
      decls: 4,
      vars: 6,
      consts: [["button", ""], ["type", "button", "mat-icon-button", "", 3, "disabled", "disableRipple"], ["viewBox", "0 0 24 24", "width", "24", "height", "24", "fill", "currentColor", "focusable", "false", 1, "mat-timepicker-toggle-default-icon"], ["d", "M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10S17.5,2,12,2z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8 S16.41,20,12,20z M12.5,7H11v6l5.2,3.2l0.8-1.3l-4.5-2.7V7z"]],
      template: function MatTimepickerToggle_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef(_c0);
          ɵɵelementStart(0, "button", 1, 0);
          ɵɵtemplate(2, MatTimepickerToggle_Conditional_2_Template, 2, 0, ":svg:svg", 2);
          ɵɵprojection(3);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("disabled", ctx.disabled)("disableRipple", ctx.disableRipple);
          ɵɵattribute("aria-haspopup", ctx.timepicker ? "dialog" : null)("aria-label", ctx.ariaLabel || ctx._intl.openTimepickerLabel)("tabindex", ctx.disabled ? -1 : ctx.tabIndex);
          ɵɵadvance(2);
          ɵɵconditional(!ctx.customIcon ? 2 : -1);
        }
      },
      dependencies: [CommonModule, MatButtonModule, MatIconButton],
      styles: [".mat-form-field .mat-form-field-prefix .mat-timepicker-toggle-default-icon,.mat-form-field .mat-form-field-suffix .mat-timepicker-toggle-default-icon{display:block;width:1.5em;height:1.5em}.mat-form-field .mat-form-field-prefix .mat-icon-button .mat-timepicker-toggle-default-icon,.mat-form-field .mat-form-field-suffix .mat-icon-button .mat-timepicker-toggle-default-icon{margin:auto}\n"],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerToggle, [{
    type: Component,
    args: [{
      selector: "mat-timepicker-toggle",
      standalone: true,
      imports: [CommonModule, MatButtonModule],
      exportAs: "matTimepickerToggle",
      host: {
        class: "mat-timepicker-toggle",
        "[attr.tabindex]": "null",
        "[class.mat-timepicker-toggle-active]": "timepicker && timepicker.opened",
        "[class.mat-accent]": 'timepicker && timepicker.color === "accent"',
        "[class.mat-warn]": 'timepicker && timepicker.color === "warn"',
        "(click)": "open($event)"
      },
      encapsulation: ViewEncapsulation$1.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `<button
  type="button"
  #button
  mat-icon-button
  [attr.aria-haspopup]="timepicker ? 'dialog' : null"
  [attr.aria-label]="ariaLabel || _intl.openTimepickerLabel"
  [attr.tabindex]="disabled ? -1 : tabIndex"
  [disabled]="disabled"
  [disableRipple]="disableRipple"
>
  @if (!customIcon) {
    <svg
      class="mat-timepicker-toggle-default-icon"
      viewBox="0 0 24 24"
      width="24"
      height="24"
      fill="currentColor"
      focusable="false"
    >
      <path
        d="M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10s10-4.5,10-10S17.5,2,12,2z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8 S16.41,20,12,20z M12.5,7H11v6l5.2,3.2l0.8-1.3l-4.5-2.7V7z"
      />
    </svg>
  }

  <ng-content select="[matTimepickerToggleIcon]"></ng-content>
</button>
`,
      styles: [".mat-form-field .mat-form-field-prefix .mat-timepicker-toggle-default-icon,.mat-form-field .mat-form-field-suffix .mat-timepicker-toggle-default-icon{display:block;width:1.5em;height:1.5em}.mat-form-field .mat-form-field-prefix .mat-icon-button .mat-timepicker-toggle-default-icon,.mat-form-field .mat-form-field-suffix .mat-icon-button .mat-timepicker-toggle-default-icon{margin:auto}\n"]
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Attribute,
      args: ["tabindex"]
    }]
  }, {
    type: MatTimepickerIntl
  }, {
    type: ChangeDetectorRef
  }], {
    timepicker: [{
      type: Input,
      args: ["for"]
    }],
    disabled: [{
      type: Input
    }],
    disableRipple: [{
      type: Input
    }],
    tabIndex: [{
      type: Input
    }],
    customIcon: [{
      type: ContentChild,
      args: [MatTimepickerToggleIcon]
    }],
    ariaLabel: [{
      type: Input,
      args: ["aria-label"]
    }]
  });
})();
var matTimepickerAnimations = {
  /** Transforms the height of the timepicker's. */
  transformPanel: trigger("transformPanel", [transition("void => enter-dropdown", animate("120ms cubic-bezier(0, 0, 0.2, 1)", keyframes([style({
    opacity: 0,
    transform: "scale(1, 0.8)"
  }), style({
    opacity: 1,
    transform: "scale(1, 1)"
  })]))), transition("void => enter-dialog", animate("150ms cubic-bezier(0, 0, 0.2, 1)", keyframes([style({
    opacity: 0,
    transform: "scale(0.7)"
  }), style({
    transform: "none",
    opacity: 1
  })]))), transition("* => void", animate("100ms linear", style({
    opacity: 0
  })))]),
  /** Fades in the content of the timepicker. */
  fadeInTimepicker: trigger("fadeInTimepicker", [state("void", style({
    opacity: 0
  })), state("enter", style({
    opacity: 1
  }))])
};
var TOUCH_UI_MULTIPLIER = 1.25;
var TOUCH_UI_TICK_MULTIPLIER = 1.5;
var CLOCK_RADIUS = 128;
var CLOCK_TICK_RADIUS = 16;
var CLOCK_OUTER_RADIUS = 100;
function getClockRadius(touchUi) {
  return touchUi ? CLOCK_RADIUS * TOUCH_UI_MULTIPLIER : CLOCK_RADIUS;
}
function getClockTickRadius(touchUi) {
  return touchUi ? CLOCK_TICK_RADIUS * TOUCH_UI_TICK_MULTIPLIER : CLOCK_TICK_RADIUS;
}
function getClockCorrectedRadius(touchUi) {
  return getClockRadius(touchUi) - getClockTickRadius(touchUi);
}
function getClockOuterRadius(touchUi) {
  return touchUi ? CLOCK_OUTER_RADIUS * TOUCH_UI_MULTIPLIER : CLOCK_OUTER_RADIUS;
}
function getClockInnerRadius(touchUi) {
  return getClockOuterRadius(touchUi) - getClockTickRadius(touchUi) * 2;
}
var ALL_MINUTES = Array(60).fill(null).map((_, i) => i);
var MatMinutesClockDial = class _MatMinutesClockDial {
  /** Selected minute. */
  get selectedMinute() {
    return this._selectedMinute;
  }
  set selectedMinute(value) {
    this._selectedMinute = value;
  }
  /** Step over minutes. */
  get interval() {
    return this._interval;
  }
  set interval(value) {
    this._interval = coerceNumberProperty(value) || 1;
  }
  get availableMinutes() {
    return this._availableMinutes;
  }
  set availableMinutes(value) {
    this._availableMinutes = value;
    this._initMinutes();
  }
  /** Whether the timepicker UI is in touch mode. */
  get touchUi() {
    return this._touchUi;
  }
  set touchUi(value) {
    this._touchUi = value;
  }
  get disabled() {
    return !this.availableMinutes.includes(this.selectedMinute);
  }
  get isMinutePoint() {
    return !!this.minutes.find((hour) => hour.value === this.selectedMinute);
  }
  constructor(_element, _cdr, _document) {
    this._element = _element;
    this._cdr = _cdr;
    this._document = _document;
    this._selectedMinute = 0;
    this._interval = 1;
    this._availableMinutes = [];
    this.selectedChange = new EventEmitter();
    this.minutes = [];
  }
  ngOnInit() {
    this._initMinutes();
  }
  /** Hand styles based on selected minute. */
  _handStyles() {
    const deg = Math.round(this._selectedMinute * (360 / 60));
    const height = getClockOuterRadius(this.touchUi);
    const marginTop = getClockRadius(this.touchUi) - getClockOuterRadius(this.touchUi);
    return {
      transform: `rotate(${deg}deg)`,
      height: `${height}px`,
      "margin-top": `${marginTop}px`
    };
  }
  /** Handles mouse and touch events on dial and document. */
  _onUserAction(event) {
    if (event.cancelable) {
      event.preventDefault();
    }
    this._setMinute(event);
    const eventsSubscription = merge(fromEvent(this._document, "mousemove"), fromEvent(this._document, "touchmove")).pipe(debounceTime(0)).subscribe({
      next: (event2) => {
        event2.preventDefault();
        this._setMinute(event2);
      }
    });
    merge(fromEvent(this._document, "mouseup"), fromEvent(this._document, "touchend")).pipe(take(1)).subscribe({
      next: () => {
        eventsSubscription.unsubscribe();
      }
    });
  }
  _isActiveCell(minute) {
    return this.selectedMinute === minute;
  }
  _setMinute(event) {
    const element = this._element.nativeElement;
    const window2 = this._getWindow();
    const elementRect = element.getBoundingClientRect();
    const width = element.offsetWidth;
    const height = element.offsetHeight;
    const pageX = event instanceof MouseEvent ? event.pageX : event.touches[0].pageX;
    const pageY = event instanceof MouseEvent ? event.pageY : event.touches[0].pageY;
    const x = width / 2 - (pageX - elementRect.left - window2.scrollX);
    const y = height / 2 - (pageY - elementRect.top - window2.scrollY);
    const unit = Math.PI / (30 / this.interval);
    const atan2 = Math.atan2(-x, y);
    const radian = atan2 < 0 ? Math.PI * 2 + atan2 : atan2;
    const initialValue = Math.round(radian / unit) * this.interval;
    const value = initialValue === 60 ? 0 : initialValue;
    if (this.availableMinutes.includes(value) && this.availableMinutes.includes(value)) {
      this.selectedMinute = value;
      this.selectedChange.emit(this.selectedMinute);
    }
    this._cdr.detectChanges();
  }
  /** Creates list of minutes. */
  _initMinutes() {
    this.minutes = ALL_MINUTES.filter((minute) => minute % 5 === 0).map((minute) => {
      const radian = minute / 30 * Math.PI;
      const displayValue = minute === 0 ? "00" : String(minute);
      return {
        value: minute,
        displayValue,
        left: getClockCorrectedRadius(this.touchUi) + Math.sin(radian) * getClockOuterRadius(this.touchUi),
        top: getClockCorrectedRadius(this.touchUi) - Math.cos(radian) * getClockOuterRadius(this.touchUi),
        disabled: !this.availableMinutes.includes(minute)
      };
    });
  }
  /** Use defaultView of injected document if available or fallback to global window reference */
  _getWindow() {
    return this._document.defaultView || window;
  }
  static {
    this.ɵfac = function MatMinutesClockDial_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatMinutesClockDial)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(DOCUMENT));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatMinutesClockDial,
      selectors: [["mat-minutes-clock-dial"]],
      hostAttrs: [1, "mat-clock-dial", "mat-clock-dial-minutes"],
      hostBindings: function MatMinutesClockDial_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("mousedown", function MatMinutesClockDial_mousedown_HostBindingHandler($event) {
            return ctx._onUserAction($event);
          })("touchstart", function MatMinutesClockDial_touchstart_HostBindingHandler($event) {
            return ctx._onUserAction($event);
          });
        }
      },
      inputs: {
        selectedMinute: "selectedMinute",
        interval: "interval",
        availableMinutes: "availableMinutes",
        color: "color",
        touchUi: "touchUi"
      },
      outputs: {
        selectedChange: "selectedChange"
      },
      exportAs: ["matMinutesClockDial"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      decls: 4,
      vars: 5,
      consts: [[1, "mat-clock-dial-hand", 3, "ngStyle"], ["tabindex", "0", 1, "mat-clock-dial-hand-point"], ["mat-mini-fab", "", "disableRipple", "", 1, "mat-clock-dial-cell", 3, "tabIndex", "left", "top", "mat-clock-dial-cell-active", "mat-clock-dial-cell-disabled", "color"], ["mat-mini-fab", "", "disableRipple", "", 1, "mat-clock-dial-cell", 3, "tabIndex", "color"]],
      template: function MatMinutesClockDial_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵelement(1, "div", 1);
          ɵɵelementEnd();
          ɵɵrepeaterCreate(2, MatMinutesClockDial_For_3_Template, 2, 12, "button", 2, _forTrack0);
        }
        if (rf & 2) {
          ɵɵclassProp("mat-clock-dial-hand-pointless", ctx.isMinutePoint)("mat-clock-dial-hand-disabled", ctx.disabled);
          ɵɵproperty("ngStyle", ctx._handStyles());
          ɵɵadvance(2);
          ɵɵrepeater(ctx.minutes);
        }
      },
      dependencies: [CommonModule, NgStyle, MatButtonModule, MatMiniFabButton],
      styles: ['.mat-clock-dial{position:relative;display:block;width:16rem;height:16rem;margin:0 auto;border-radius:50%}.mat-clock-dial:before{position:absolute;top:50%;left:50%;width:.4375rem;height:.4375rem;border-radius:50%;transform:translate(-50%,-50%);content:""}[mat-mini-fab].mat-clock-dial-cell{position:absolute;display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;border-radius:50%;box-shadow:none}[mat-mini-fab].mat-clock-dial-cell:disabled{pointer-events:none}[mat-mini-fab].mat-clock-dial-cell:focus,[mat-mini-fab].mat-clock-dial-cell:hover,[mat-mini-fab].mat-clock-dial-cell:active,[mat-mini-fab].mat-clock-dial-cell:focus:active{box-shadow:none}.mat-timepicker-content-touch .mat-clock-dial{width:20rem;height:20rem}.mat-timepicker-content-touch [mat-mini-fab].mat-clock-dial-cell{width:3rem;height:3rem;font-size:1.125rem}.mat-clock-dial-hand{position:absolute;inset:0;width:1px;margin:0 auto;transform-origin:bottom}.mat-clock-dial-hand:before{position:absolute;top:-.25rem;left:-.25rem;width:calc(.5rem + 1px);height:calc(.5rem + 1px);border-radius:50%;content:""}.mat-clock-dial-hand.mat-clock-dial-hand-pointless:before{content:none}\n'],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatMinutesClockDial, [{
    type: Component,
    args: [{
      selector: "mat-minutes-clock-dial",
      standalone: true,
      imports: [CommonModule, MatButtonModule],
      exportAs: "matMinutesClockDial",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-clock-dial mat-clock-dial-minutes",
        "(mousedown)": "_onUserAction($event)",
        "(touchstart)": "_onUserAction($event)"
      },
      template: '<div\n  class="mat-clock-dial-hand"\n  [class.mat-clock-dial-hand-pointless]="isMinutePoint"\n  [class.mat-clock-dial-hand-disabled]="disabled"\n  [ngStyle]="_handStyles()"\n>\n  <div class="mat-clock-dial-hand-point" tabindex="0"></div>\n</div>\n@for (minute of minutes; track minute.value) {\n  <button\n    class="mat-clock-dial-cell"\n    mat-mini-fab\n    disableRipple\n    [tabIndex]="_isActiveCell(minute.value) ? 0 : -1"\n    [style.left.px]="minute.left"\n    [style.top.px]="minute.top"\n    [class.mat-clock-dial-cell-active]="_isActiveCell(minute.value)"\n    [class.mat-clock-dial-cell-disabled]="minute.disabled"\n    [color]="_isActiveCell(minute.value) ? color : undefined"\n    [attr.aria-disabled]="minute.disabled || null"\n  >\n    {{ minute.displayValue }}\n  </button>\n}\n',
      styles: ['.mat-clock-dial{position:relative;display:block;width:16rem;height:16rem;margin:0 auto;border-radius:50%}.mat-clock-dial:before{position:absolute;top:50%;left:50%;width:.4375rem;height:.4375rem;border-radius:50%;transform:translate(-50%,-50%);content:""}[mat-mini-fab].mat-clock-dial-cell{position:absolute;display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;border-radius:50%;box-shadow:none}[mat-mini-fab].mat-clock-dial-cell:disabled{pointer-events:none}[mat-mini-fab].mat-clock-dial-cell:focus,[mat-mini-fab].mat-clock-dial-cell:hover,[mat-mini-fab].mat-clock-dial-cell:active,[mat-mini-fab].mat-clock-dial-cell:focus:active{box-shadow:none}.mat-timepicker-content-touch .mat-clock-dial{width:20rem;height:20rem}.mat-timepicker-content-touch [mat-mini-fab].mat-clock-dial-cell{width:3rem;height:3rem;font-size:1.125rem}.mat-clock-dial-hand{position:absolute;inset:0;width:1px;margin:0 auto;transform-origin:bottom}.mat-clock-dial-hand:before{position:absolute;top:-.25rem;left:-.25rem;width:calc(.5rem + 1px);height:calc(.5rem + 1px);border-radius:50%;content:""}.mat-clock-dial-hand.mat-clock-dial-hand-pointless:before{content:none}\n']
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: Document,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }], {
    selectedMinute: [{
      type: Input
    }],
    interval: [{
      type: Input
    }],
    availableMinutes: [{
      type: Input
    }],
    color: [{
      type: Input
    }],
    touchUi: [{
      type: Input
    }],
    selectedChange: [{
      type: Output
    }]
  });
})();
var ALL_HOURS = Array(24).fill(null).map((_, i) => i);
var MatHoursClockDial = class _MatHoursClockDial {
  /** Selected hour. */
  get selectedHour() {
    return this._selectedHour;
  }
  set selectedHour(value) {
    this._selectedHour = value;
  }
  /** Whether the clock uses 12 hour format. */
  get isMeridiem() {
    return this._isMeridiem;
  }
  set isMeridiem(value) {
    this._isMeridiem = value;
  }
  get availableHours() {
    return this._availableHours;
  }
  set availableHours(value) {
    this._availableHours = value;
    this._initHours();
  }
  /** Whether the timepicker UI is in touch mode. */
  get touchUi() {
    return this._touchUi;
  }
  set touchUi(value) {
    this._touchUi = value;
    this._initHours();
  }
  get disabledHand() {
    return !this.availableHours.includes(this.selectedHour);
  }
  get isHour() {
    return !!this.hours.find((hour) => hour.value === this.selectedHour);
  }
  constructor(_element, _cdr, _document) {
    this._element = _element;
    this._cdr = _cdr;
    this._document = _document;
    this._availableHours = [];
    this.selectedChange = new EventEmitter();
    this.hours = [];
  }
  ngOnInit() {
    this._initHours();
  }
  /** Hand styles based on selected hour. */
  _handStyles() {
    const deg = Math.round(this.selectedHour * (360 / (24 / 2)));
    const radius = this._getRadius(this.selectedHour);
    const height = radius;
    const marginTop = getClockRadius(this.touchUi) - radius;
    return {
      transform: `rotate(${deg}deg)`,
      height: `${height}px`,
      "margin-top": `${marginTop}px`
    };
  }
  /** Handles mouse and touch events on dial and document. */
  _onUserAction(event) {
    if (event.cancelable) {
      event.preventDefault();
    }
    this._setHour(event);
    const eventsSubscription = merge(fromEvent(this._document, "mousemove"), fromEvent(this._document, "touchmove")).pipe(debounceTime(0)).subscribe({
      next: (event2) => {
        event2.preventDefault();
        this._setHour(event2);
      }
    });
    merge(fromEvent(this._document, "mouseup"), fromEvent(this._document, "touchend")).pipe(take(1)).subscribe({
      next: () => {
        eventsSubscription.unsubscribe();
        this.selectedChange.emit({
          hour: this.selectedHour,
          changeView: true
        });
      }
    });
  }
  _isActiveCell(hour) {
    return this.selectedHour === hour;
  }
  /** Changes selected hour based on coordinates. */
  _setHour(event) {
    const element = this._element.nativeElement;
    const window2 = this._getWindow();
    const elementRect = element.getBoundingClientRect();
    const width = element.offsetWidth;
    const height = element.offsetHeight;
    const pageX = event instanceof MouseEvent ? event.pageX : event.touches[0].pageX;
    const pageY = event instanceof MouseEvent ? event.pageY : event.touches[0].pageY;
    const x = width / 2 - (pageX - elementRect.left - window2.scrollX);
    const y = height / 2 - (pageY - elementRect.top - window2.scrollY);
    const unit = Math.PI / 6;
    const atan2 = Math.atan2(-x, y);
    const radian = atan2 < 0 ? Math.PI * 2 + atan2 : atan2;
    const initialValue = Math.round(radian / unit);
    const z = Math.sqrt(x * x + y * y);
    const outer = z > getClockOuterRadius(this.touchUi) - getClockTickRadius(this.touchUi);
    const value = this._getHourValue(initialValue, outer);
    if (this.availableHours.includes(value)) {
      this.selectedHour = value;
      this.selectedChange.emit({
        hour: this.selectedHour
      });
    }
    this._cdr.detectChanges();
  }
  /** Return value of hour. */
  _getHourValue(value, outer) {
    const edgeValue = value === 0 || value === 12;
    if (this.isMeridiem) {
      return edgeValue ? 12 : value;
    }
    if (outer) {
      return edgeValue ? 0 : value;
    }
    return edgeValue ? 12 : value + 12;
  }
  /** Creates list of hours. */
  _initHours() {
    const initialHours = this.isMeridiem ? ALL_HOURS.slice(1, 13) : ALL_HOURS;
    this.hours = initialHours.map((hour) => {
      const radian = hour / 6 * Math.PI;
      const radius = this._getRadius(hour);
      return {
        value: hour,
        displayValue: hour === 0 ? "00" : String(hour),
        left: getClockCorrectedRadius(this.touchUi) + Math.sin(radian) * radius,
        top: getClockCorrectedRadius(this.touchUi) - Math.cos(radian) * radius,
        disabled: !this.availableHours.includes(hour)
      };
    });
  }
  /** Returns radius based on hour */
  _getRadius(hour) {
    if (this.isMeridiem) {
      return getClockOuterRadius(this.touchUi);
    }
    const outer = hour >= 0 && hour < 12;
    const radius = outer ? getClockOuterRadius(this.touchUi) : getClockInnerRadius(this.touchUi);
    return radius;
  }
  /** Use defaultView of injected document if available or fallback to global window reference */
  _getWindow() {
    return this._document.defaultView || window;
  }
  static {
    this.ɵfac = function MatHoursClockDial_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatHoursClockDial)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(DOCUMENT));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatHoursClockDial,
      selectors: [["mat-hours-clock-dial"]],
      hostAttrs: [1, "mat-clock-dial", "mat-clock-dial-hours"],
      hostBindings: function MatHoursClockDial_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("mousedown", function MatHoursClockDial_mousedown_HostBindingHandler($event) {
            return ctx._onUserAction($event);
          })("touchstart", function MatHoursClockDial_touchstart_HostBindingHandler($event) {
            return ctx._onUserAction($event);
          });
        }
      },
      inputs: {
        selectedHour: "selectedHour",
        isMeridiem: "isMeridiem",
        availableHours: "availableHours",
        color: "color",
        touchUi: "touchUi"
      },
      outputs: {
        selectedChange: "selectedChange"
      },
      exportAs: ["matHoursClockDial"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      decls: 3,
      vars: 5,
      consts: [[1, "mat-clock-dial-hand", 3, "ngStyle"], ["mat-mini-fab", "", "disableRipple", "", 1, "mat-clock-dial-cell", 3, "tabIndex", "left", "top", "mat-clock-dial-cell-active", "mat-clock-dial-cell-disabled", "color"], ["mat-mini-fab", "", "disableRipple", "", 1, "mat-clock-dial-cell", 3, "tabIndex", "color"]],
      template: function MatHoursClockDial_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelement(0, "div", 0);
          ɵɵrepeaterCreate(1, MatHoursClockDial_For_2_Template, 2, 12, "button", 1, _forTrack0);
        }
        if (rf & 2) {
          ɵɵclassProp("mat-clock-dial-hand-pointless", ctx.isHour)("mat-clock-dial-hand-disabled", ctx.disabledHand);
          ɵɵproperty("ngStyle", ctx._handStyles());
          ɵɵadvance();
          ɵɵrepeater(ctx.hours);
        }
      },
      dependencies: [CommonModule, NgStyle, MatButtonModule, MatMiniFabButton],
      styles: [_c2],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatHoursClockDial, [{
    type: Component,
    args: [{
      selector: "mat-hours-clock-dial",
      standalone: true,
      imports: [CommonModule, MatButtonModule],
      exportAs: "matHoursClockDial",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-clock-dial mat-clock-dial-hours",
        "(mousedown)": "_onUserAction($event)",
        "(touchstart)": "_onUserAction($event)"
      },
      template: '<div\n  class="mat-clock-dial-hand"\n  [class.mat-clock-dial-hand-pointless]="isHour"\n  [class.mat-clock-dial-hand-disabled]="disabledHand"\n  [ngStyle]="_handStyles()"\n></div>\n@for (hour of hours; track hour.value) {\n  <button\n    class="mat-clock-dial-cell"\n    mat-mini-fab\n    disableRipple\n    [tabIndex]="_isActiveCell(hour.value) ? 0 : -1"\n    [style.left.px]="hour.left"\n    [style.top.px]="hour.top"\n    [class.mat-clock-dial-cell-active]="_isActiveCell(hour.value)"\n    [class.mat-clock-dial-cell-disabled]="hour.disabled"\n    [color]="_isActiveCell(hour.value) ? color : undefined"\n    [attr.aria-disabled]="hour.disabled || null"\n  >\n    {{ hour.displayValue }}\n  </button>\n}\n',
      styles: ['.mat-clock-dial{position:relative;display:block;width:16rem;height:16rem;margin:0 auto;border-radius:50%}.mat-clock-dial:before{position:absolute;top:50%;left:50%;width:.4375rem;height:.4375rem;border-radius:50%;transform:translate(-50%,-50%);content:""}[mat-mini-fab].mat-clock-dial-cell{position:absolute;display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;border-radius:50%;box-shadow:none}[mat-mini-fab].mat-clock-dial-cell:disabled{pointer-events:none}[mat-mini-fab].mat-clock-dial-cell:focus,[mat-mini-fab].mat-clock-dial-cell:hover,[mat-mini-fab].mat-clock-dial-cell:active,[mat-mini-fab].mat-clock-dial-cell:focus:active{box-shadow:none}.mat-timepicker-content-touch .mat-clock-dial{width:20rem;height:20rem}.mat-timepicker-content-touch [mat-mini-fab].mat-clock-dial-cell{width:3rem;height:3rem;font-size:1.125rem}.mat-clock-dial-hand{position:absolute;inset:0;width:1px;margin:0 auto;transform-origin:bottom}.mat-clock-dial-hand:before{position:absolute;top:-.25rem;left:-.25rem;width:calc(.5rem + 1px);height:calc(.5rem + 1px);border-radius:50%;content:""}.mat-clock-dial-hand.mat-clock-dial-hand-pointless:before{content:none}\n']
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: Document,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }], {
    selectedHour: [{
      type: Input
    }],
    isMeridiem: [{
      type: Input
    }],
    availableHours: [{
      type: Input
    }],
    color: [{
      type: Input
    }],
    touchUi: [{
      type: Input
    }],
    selectedChange: [{
      type: Output
    }]
  });
})();
var MAT_TIME_LOCALE = new InjectionToken("MAT_TIME_LOCALE", {
  providedIn: "root",
  factory: MAT_DATE_TIME_LOCALE_FACTORY
});
function MAT_DATE_TIME_LOCALE_FACTORY() {
  return inject(LOCALE_ID);
}
var MAT_TIME_LOCALE_PROVIDER = {
  provide: MAT_TIME_LOCALE,
  useExisting: LOCALE_ID
};
var TimeAdapter = class {
  /**
   * Given a potential time object, returns that same time object if it is
   * a valid time, or `null` if it's not a valid time.
   * @param obj The object to check.
   * @returns A time or `null`.
   */
  getValidTimeOrNull(obj) {
    return this.isTimeInstance(obj) && this.isValid(obj) ? obj : null;
  }
  /**
   * Attempts to deserialize a value to a valid time object. The `<mat-timepicker>` will call this
   * method on all of its `@Input()` properties that accept time. It is therefore possible to
   * support passing values from your backend directly to these properties by overriding this method
   * to also deserialize the format used by your backend.
   * @param value The value to be deserialized into a time object.
   * @returns The deserialized time object, either a valid time, null if the value can be
   *     deserialized into a null time (e.g. the empty string), or an invalid date.
   */
  deserialize(value) {
    if (value == null || this.isTimeInstance(value) && this.isValid(value)) {
      return value;
    }
    return this.invalid();
  }
  /**
   * Sets the locale used for all time.
   * @param locale The new locale.
   */
  setLocale(locale) {
    this.locale = locale;
  }
  /**
   * Checks if two time are equal.
   * @param first The first time to check.
   * @param second The second time to check.
   * @returns Whether the two time are equal.
   *     Null time are considered equal to other null time.
   */
  sameTime(first2, second) {
    if (first2 && second) {
      let firstValid = this.isValid(first2);
      let secondValid = this.isValid(second);
      if (firstValid && secondValid) {
        return !this.compareTime(first2, second);
      }
      return firstValid == secondValid;
    }
    return first2 == second;
  }
  /**
   * Clamp the given time between min and max time.
   * @param time The time to clamp.
   * @param min The minimum value to allow. If null or omitted no min is enforced.
   * @param max The maximum value to allow. If null or omitted no max is enforced.
   * @returns `min` if `time` is less than `min`, `max` if time is greater than `max`,
   *     otherwise `time`.
   */
  clampTime(time, min, max) {
    if (min && this.compareTime(time, min) < 0) {
      return min;
    }
    if (max && this.compareTime(time, max) > 0) {
      return max;
    }
    return time;
  }
};
var ISO_8601_REGEX = /^\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|(?:(?:\+|-)\d{2}:\d{2}))?)?$/;
var NativeDateTimeAdapter = class _NativeDateTimeAdapter extends TimeAdapter {
  constructor(matTimeLocale) {
    super();
    this._matTimeLocale = inject(MAT_TIME_LOCALE, {
      optional: true
    });
    if (matTimeLocale !== void 0) {
      this._matTimeLocale = matTimeLocale;
    }
    super.setLocale(this._matTimeLocale);
  }
  now() {
    return /* @__PURE__ */ new Date();
  }
  parse(value, parseFormat) {
    if (typeof value == "number") {
      return new Date(value);
    }
    const {
      hour,
      minute,
      meridiem
    } = this.parseTime(value);
    const correctedHour = meridiem === "pm" && hour < 12 ? hour + 12 : hour;
    const date = /* @__PURE__ */ new Date();
    date.setHours(correctedHour);
    date.setMinutes(minute);
    return value ? new Date(date) : null;
  }
  parseTime(value) {
    const time = value.replace(/(\sam|\spm|\sAM|\sPM|am|pm|AM|PM)/g, "");
    const meridiem = value.replace(time, "").trim().toLowerCase();
    const [hour, minute] = time.split(":");
    return {
      hour: Number(hour),
      minute: Number(minute),
      meridiem
    };
  }
  getHour(date) {
    return date.getHours();
  }
  getMinute(date) {
    return date.getMinutes();
  }
  updateHour(date, hour) {
    const copy = new Date(date.getTime());
    copy.setHours(hour);
    return copy;
  }
  updateMinute(date, minute) {
    const copy = new Date(date.getTime());
    copy.setMinutes(minute);
    return copy;
  }
  getPeriod(date) {
    return date.getHours() < 12 ? "am" : "pm";
  }
  format(date, displayFormat) {
    if (!this.isValid(date)) {
      throw Error("NativeDateTimeAdapter: Cannot format invalid date.");
    }
    const dtf = new Intl.DateTimeFormat(this.locale, __spreadProps(__spreadValues({}, displayFormat), {
      timeZone: "utc"
    }));
    return this._format(dtf, date);
  }
  /**
   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings
   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an
   * invalid date for all other values.
   */
  deserialize(value) {
    if (typeof value === "string") {
      if (!value) {
        return null;
      }
      if (ISO_8601_REGEX.test(value)) {
        const date = new Date(value);
        if (this.isValid(date)) {
          return date;
        }
      }
    }
    return super.deserialize(value);
  }
  isTimeInstance(obj) {
    return obj instanceof Date;
  }
  isValid(date) {
    return !isNaN(date.getTime());
  }
  invalid() {
    return /* @__PURE__ */ new Date(NaN);
  }
  compareTime(first2, second) {
    return first2.getHours() - second.getHours() || first2.getMinutes() - second.getMinutes();
  }
  /**
   * When converting Date object to string, javascript built-in functions may return wrong
   * results because it applies its internal DST rules. The DST rules around the world change
   * very frequently, and the current valid rule is not always valid in previous years though.
   * We work around this problem building a new Date object which has its internal UTC
   * representation with the local date and time.
   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have
   *    timeZone set to 'utc' to work fine.
   * @param date Date from which we want to get the string representation according to dtf
   * @returns A Date object with its UTC representation based on the passed in date info
   */
  _format(dtf, date) {
    const d = /* @__PURE__ */ new Date();
    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());
    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    return dtf.format(d);
  }
  static {
    this.ɵfac = function NativeDateTimeAdapter_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NativeDateTimeAdapter)(ɵɵinject(MAT_TIME_LOCALE, 8));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _NativeDateTimeAdapter,
      factory: _NativeDateTimeAdapter.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NativeDateTimeAdapter, [{
    type: Injectable
  }], () => [{
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [MAT_TIME_LOCALE]
    }]
  }], null);
})();
var NativeDateTimeModule = class _NativeDateTimeModule {
  static {
    this.ɵfac = function NativeDateTimeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _NativeDateTimeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _NativeDateTimeModule,
      imports: [PlatformModule]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [{
        provide: TimeAdapter,
        useClass: NativeDateTimeAdapter
      }],
      imports: [PlatformModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NativeDateTimeModule, [{
    type: NgModule,
    args: [{
      imports: [PlatformModule],
      providers: [{
        provide: TimeAdapter,
        useClass: NativeDateTimeAdapter
      }]
    }]
  }], null, null);
})();
var MatNativeDateTimeModule = class _MatNativeDateTimeModule {
  static {
    this.ɵfac = function MatNativeDateTimeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatNativeDateTimeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _MatNativeDateTimeModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [provideNativeDateTimeAdapter()]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatNativeDateTimeModule, [{
    type: NgModule,
    args: [{
      providers: [provideNativeDateTimeAdapter()]
    }]
  }], null, null);
})();
function provideNativeDateTimeAdapter() {
  return {
    provide: TimeAdapter,
    useClass: NativeDateTimeAdapter
  };
}
var MatTimeFaceBase = class _MatTimeFaceBase {
  /** The currently selected time. */
  get selected() {
    return this._selected;
  }
  set selected(value) {
    this._selected = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(value));
    if (!this._selected) {
      return;
    }
    const hour = this._timeAdapter.getHour(this._selected);
    this.selectedHour = hour > 12 && this.isMeridiem ? hour - 12 : hour;
    if (hour === 0 && this.isMeridiem) {
      this.selectedHour = 12;
    }
    this.selectedMinute = this._timeAdapter.getMinute(this._selected);
    this.availableHours = ALL_HOURS;
    if (this.isMeridiem) {
      this.period = this._timeAdapter.getPeriod(this._selected);
    }
    this.availableMinutes = ALL_MINUTES;
    this._setMinHour();
    this._setMaxHour();
    this._setMinMinute();
    this._setMaxMinute();
    this._moveFocusOnNextTick = this.isMeridiem;
  }
  /** The minimum selectable time. */
  get minTime() {
    return this._minTime;
  }
  set minTime(value) {
    this._minTime = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(value));
    if (value) {
      this._setMinHour();
      this._setMinMinute();
      this._setDisabledPeriod();
    }
  }
  /** The maximum selectable time. */
  get maxTime() {
    return this._maxTime;
  }
  set maxTime(value) {
    this._maxTime = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(value));
    if (value) {
      this._setMaxHour();
      this._setMaxMinute();
      this._setDisabledPeriod();
    }
  }
  /** Step over minutes. */
  get minuteInterval() {
    return this._minuteInterval;
  }
  set minuteInterval(value) {
    this._minuteInterval = coerceNumberProperty(value) || 1;
  }
  constructor(_timeAdapter) {
    this._timeAdapter = _timeAdapter;
    this._minuteInterval = 1;
    this.color = "primary";
    this._userSelection = new EventEmitter();
    this.selectedChange = new EventEmitter();
    this.selectedHour = 0;
    this.selectedMinute = 0;
    this.disabledPeriod = null;
    this.availableMinutes = ALL_MINUTES;
    this.availableHours = ALL_HOURS;
    this._moveFocusOnNextTick = false;
  }
  ngAfterContentInit() {
    if (!this.selected) {
      this.selected = this._timeAdapter.clampTime(this._timeAdapter.now(), this.minTime, this.maxTime);
      this._userSelection.emit(this.selected);
    }
  }
  ngAfterViewChecked() {
    if (this._moveFocusOnNextTick) {
      this._moveFocusOnNextTick = false;
      this.focusActiveCell();
    }
  }
  /** Handles hour selection. */
  _onHourSelected(hour) {
    this.selectedHour = hour;
    const selected = this._timeAdapter.updateHour(this.selected, this._getHourBasedOnPeriod(hour));
    this._timeSelected(selected);
  }
  /** Handles minute selection. */
  _onMinuteSelected(minute) {
    this.selectedMinute = minute;
    const selected = this._timeAdapter.updateMinute(this.selected, minute);
    this._timeSelected(selected);
  }
  /** Handles period changing. */
  _onPeriodChanged(period) {
    this.period = period;
    const selected = this._timeAdapter.updateHour(this.selected, this._getHourBasedOnPeriod(this.selectedHour));
    this._timeSelected(selected);
  }
  _getAvailableHours() {
    if (this.isMeridiem) {
      return this.availableHours.filter((h) => {
        if (this.period === "am") {
          return h < 12;
        }
        if (this.period === "pm") {
          return h >= 12;
        }
        return h;
      }).map((h) => {
        if (h > 12) {
          return h - 12;
        }
        if (h === 0) {
          return 12;
        }
        return h;
      });
    }
    return this.availableHours;
  }
  _onKeydown(event, view) {
    switch (view) {
      case "hour":
        this._handleHourKeydown(event);
        break;
      case "minute":
        this._handleMinuteKeydown(event);
        break;
    }
  }
  _handleHourKeydown(event) {
    const hours = this._getAvailableHours();
    const selectedHourIndex = hours.findIndex((hour) => hour === this.selectedHour);
    if (!hours.length) {
      return;
    }
    switch (event.keyCode) {
      case UP_ARROW:
        if (selectedHourIndex + 1 >= hours.length || selectedHourIndex < 0) {
          this._onHourSelected(hours[0]);
        } else {
          this._onHourSelected(hours[selectedHourIndex + 1]);
        }
        break;
      case DOWN_ARROW:
        if (selectedHourIndex - 1 < 0 || selectedHourIndex < 0) {
          this._onHourSelected(hours[hours.length - 1]);
        } else {
          this._onHourSelected(hours[selectedHourIndex - 1]);
        }
        break;
      default:
        break;
    }
  }
  _handleMinuteKeydown(event) {
    const minutes = this.availableMinutes;
    const selectedMinuteIndex = minutes.findIndex((minute) => minute === this.selectedMinute);
    if (!minutes.length) {
      return;
    }
    switch (event.keyCode) {
      case UP_ARROW:
        if (selectedMinuteIndex + this.minuteInterval >= minutes.length || selectedMinuteIndex < 0) {
          const difference = 60 - this.selectedMinute + Math.min(...this.availableMinutes);
          const count = Math.ceil(difference / this.minuteInterval);
          const differenceForValid = count * this.minuteInterval;
          const nextValidValue = this.selectedMinute + differenceForValid;
          const correctIndex = minutes.findIndex(
            (minute) => minute === nextValidValue - 60
            // amount of mins
          );
          this._onMinuteSelected(minutes[correctIndex]);
        } else {
          this._onMinuteSelected(minutes[selectedMinuteIndex + this.minuteInterval]);
        }
        break;
      case DOWN_ARROW:
        if (selectedMinuteIndex - this.minuteInterval < 0 || selectedMinuteIndex < 0) {
          const difference = 60 + this.selectedMinute - Math.max(...this.availableMinutes);
          const count = Math.ceil(difference / this.minuteInterval);
          const differenceForValid = count * this.minuteInterval;
          const nextValidValue = this.selectedMinute - differenceForValid;
          const correctIndex = minutes.findIndex(
            (minute) => minute === nextValidValue + 60
            // amount of mins
          );
          this._onMinuteSelected(minutes[correctIndex]);
        } else {
          this._onMinuteSelected(minutes[selectedMinuteIndex - this.minuteInterval]);
        }
        break;
      default:
        break;
    }
  }
  /** Gets a correct hours based on meridiem and period. */
  _getHourBasedOnPeriod(hour) {
    const afterNoon = this.isMeridiem && this.period === "pm";
    const beforeNoon = this.isMeridiem && this.period === "am";
    if (afterNoon) {
      return hour === 12 ? hour : hour + 12;
    }
    if (beforeNoon) {
      return hour === 12 ? 0 : hour;
    }
    return hour;
  }
  _timeSelected(value) {
    if (value && !this._timeAdapter.sameTime(value, this.selected)) {
      this.selectedChange.emit(value);
    }
    this._userSelection.emit(value);
  }
  /** Sets min hour. */
  _setMinHour() {
    if (!this.minTime) {
      return;
    }
    const minHour = this._timeAdapter.getHour(this.minTime);
    this.availableHours = this.availableHours.filter((h) => h >= minHour);
  }
  /** Sets max hour. */
  _setMaxHour() {
    if (!this.maxTime) {
      return;
    }
    const maxHour = this._timeAdapter.getHour(this.maxTime);
    this.availableHours = this.availableHours.filter((h) => h <= maxHour);
  }
  /** Sets min minute. */
  _setMinMinute() {
    if (!this.selected || !this.minTime) {
      return;
    }
    const selectedHour = this._timeAdapter.getHour(this.selected);
    const minHour = this._timeAdapter.getHour(this.minTime);
    const minMinute = selectedHour > minHour ? 0 : this._timeAdapter.getMinute(this.minTime);
    this.availableMinutes = this.availableMinutes.filter((minute) => minute >= minMinute);
    if (selectedHour < minHour) {
      this.availableMinutes = [];
    }
  }
  /** Sets max minute. */
  _setMaxMinute() {
    if (!this.selected || !this.maxTime) {
      return;
    }
    const selectedHour = this._timeAdapter.getHour(this.selected);
    const maxHour = this._timeAdapter.getHour(this.maxTime);
    const maxMinute = selectedHour < maxHour ? 59 : this._timeAdapter.getMinute(this.maxTime);
    this.availableMinutes = this.availableMinutes.filter((minute) => minute <= maxMinute);
    if (selectedHour > maxHour) {
      this.availableMinutes = [];
    }
  }
  /** Sets disabled period. */
  _setDisabledPeriod() {
    if (this.minTime) {
      const minHour = this._timeAdapter.getHour(this.minTime);
      if (minHour >= 12) {
        this.disabledPeriod = "am";
      }
    }
    if (this.maxTime) {
      const maxHour = this._timeAdapter.getHour(this.maxTime);
      const maxMinute = this._timeAdapter.getHour(this.maxTime);
      if (maxHour < 12 || maxHour === 12 && maxMinute === 0) {
        this.disabledPeriod = "pm";
      }
    }
  }
  static {
    this.ɵfac = function MatTimeFaceBase_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimeFaceBase)(ɵɵdirectiveInject(TimeAdapter, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimeFaceBase,
      inputs: {
        selected: "selected",
        minTime: "minTime",
        maxTime: "maxTime",
        minuteInterval: "minuteInterval",
        isMeridiem: "isMeridiem",
        color: "color"
      },
      outputs: {
        _userSelection: "_userSelection",
        selectedChange: "selectedChange"
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimeFaceBase, [{
    type: Directive
  }], () => [{
    type: TimeAdapter,
    decorators: [{
      type: Optional
    }]
  }], {
    selected: [{
      type: Input
    }],
    minTime: [{
      type: Input
    }],
    maxTime: [{
      type: Input
    }],
    minuteInterval: [{
      type: Input
    }],
    isMeridiem: [{
      type: Input
    }],
    color: [{
      type: Input
    }],
    _userSelection: [{
      type: Output
    }],
    selectedChange: [{
      type: Output
    }]
  });
})();
function withZeroPrefix(value) {
  return value < 10 ? `0${value}` : `${value}`;
}
function withZeroPrefixMeridiem(value, isMeridiem) {
  const newValue = isMeridiem && value === 0 ? 12 : value;
  return withZeroPrefix(newValue);
}
var DIGIT_KEYS = Array.from({
  length: 10
}, (_, i) => `${i}`);
var SPECIAL_KEYS = ["Backspace", "Delete", "ArrowLeft", "ArrowRight", "Tab"];
var MatTimeInputBase = class _MatTimeInputBase {
  get value() {
    return this._value;
  }
  set value(value) {
    this._value = value;
    if (!this.hasFocus) {
      this.setInputValue(this._value);
    }
    setTimeout(() => {
      this.setInputPlaceholder(this._value);
    }, 0);
  }
  _keydown(event) {
    const isAllow = DIGIT_KEYS.includes(event.key) && !event.shiftKey || SPECIAL_KEYS.includes(event.code);
    if (!isAllow) {
      event.preventDefault();
    }
  }
  get inputElement() {
    return this.element.nativeElement;
  }
  get hasFocus() {
    return this.element?.nativeElement === this._document.activeElement;
  }
  constructor(element, _cdr, _document) {
    this.element = element;
    this._cdr = _cdr;
    this._document = _document;
    this.timeChanged = new EventEmitter();
  }
  focus() {
    this.setInputValue(null);
  }
  blur() {
    const isNumber = !isNaN(Number(this.inputElement.value));
    const value = this._formatValue(isNumber ? Number(this.inputElement.value || this._value) : this.value);
    this.setInputValue(value);
    this.setInputPlaceholder(value);
    this.timeChanged.emit(value);
  }
  setInputValue(value) {
    if (value !== null) {
      this.inputElement.value = this._withZeroPrefix(value);
    } else {
      this.inputElement.value = "";
    }
    this._cdr.markForCheck();
  }
  setInputPlaceholder(value) {
    this.inputElement.placeholder = this._withZeroPrefix(value);
    this._cdr.markForCheck();
  }
  static {
    this.ɵfac = function MatTimeInputBase_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimeInputBase)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(DOCUMENT));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimeInputBase,
      hostBindings: function MatTimeInputBase_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("keydown", function MatTimeInputBase_keydown_HostBindingHandler($event) {
            return ctx._keydown($event);
          });
        }
      },
      inputs: {
        value: "value"
      },
      outputs: {
        timeChanged: "timeChanged"
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimeInputBase, [{
    type: Directive
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: Document,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }], {
    value: [{
      type: Input
    }],
    timeChanged: [{
      type: Output
    }],
    _keydown: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }]
  });
})();
var visible = {
  transform: "scale(1)",
  opacity: 1,
  visibility: "visible"
};
var hidden = {
  transform: "scale(1.05)",
  opacity: 0,
  visibility: "hidden"
};
var enterLeaveAnimation = trigger("enterLeaveAnimation", [transition(":enter", [style(hidden), animate("0.1s ease-out", style(visible))]), transition(":leave", [style(visible), animate("0s ease-in", style(hidden))])]);
var MatTimepickerContentLayout = class _MatTimepickerContentLayout {
  constructor() {
    this.orientation = "vertical";
  }
  static {
    this.ɵfac = function MatTimepickerContentLayout_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerContentLayout)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepickerContentLayout,
      selectors: [["mat-timepicker-content-layout"]],
      hostAttrs: [1, "mat-timepicker-content-layout"],
      hostVars: 4,
      hostBindings: function MatTimepickerContentLayout_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("mat-timepicker-content-layout-horizontal", ctx.orientation === "horizontal")("mat-timepicker-content-layout-vertical", ctx.orientation === "vertical");
        }
      },
      inputs: {
        title: "title",
        orientation: "orientation"
      },
      exportAs: ["matTimepickerContent"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c4,
      decls: 12,
      vars: 1,
      consts: [[1, "mat-timepicker-content-layout-title"], [1, "mat-timepicker-content-layout-container"], [1, "mat-timepicker-content-layout-values"], [1, "mat-timepicker-content-layout-hours"], [1, "mat-timepicker-content-layout-separator"], [1, "mat-timepicker-content-layout-minutes"]],
      template: function MatTimepickerContentLayout_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef(_c3);
          ɵɵelementStart(0, "h6", 0);
          ɵɵtext(1);
          ɵɵelementEnd();
          ɵɵelementStart(2, "div", 1)(3, "div", 2)(4, "div", 3);
          ɵɵprojection(5);
          ɵɵelementEnd();
          ɵɵelementStart(6, "span", 4);
          ɵɵtext(7, ":");
          ɵɵelementEnd();
          ɵɵelementStart(8, "div", 5);
          ɵɵprojection(9, 1);
          ɵɵelementEnd();
          ɵɵprojection(10, 2);
          ɵɵelementEnd();
          ɵɵprojection(11, 3);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵtextInterpolate(ctx.title);
        }
      },
      styles: ["h6.mat-timepicker-content-layout-title{margin-top:0rem;margin-bottom:1.25rem;letter-spacing:.05rem}.mat-timepicker-content-layout-values{display:flex;justify-content:center}.mat-time-period{margin-left:.75rem}.mat-timepicker-content-layout-horizontal h6.mat-timepicker-content-layout-title{margin-bottom:0}.mat-timepicker-content-layout-horizontal .mat-timepicker-content-layout-container{display:flex;gap:4rem;align-items:center}.mat-timepicker-content-layout-horizontal .mat-timepicker-content-layout-values{flex-wrap:wrap}.mat-timepicker-content-layout-horizontal .mat-time-period{margin-top:.75rem;margin-left:0}.mat-timepicker-content-layout-hours,.mat-timepicker-content-layout-minutes{width:6rem}.mat-timepicker-content-layout-separator{display:flex;justify-content:center;align-self:center;width:1.5rem;height:1.75rem;font-weight:500}\n"],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerContentLayout, [{
    type: Component,
    args: [{
      selector: "mat-timepicker-content-layout",
      standalone: true,
      exportAs: "matTimepickerContent",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-timepicker-content-layout",
        "[class.mat-timepicker-content-layout-horizontal]": 'orientation === "horizontal"',
        "[class.mat-timepicker-content-layout-vertical]": 'orientation === "vertical"'
      },
      template: '<h6 class="mat-timepicker-content-layout-title">{{ title }}</h6>\n\n<div class="mat-timepicker-content-layout-container">\n  <div class="mat-timepicker-content-layout-values">\n    <div class="mat-timepicker-content-layout-hours">\n      <ng-content select="[hours]"></ng-content>\n    </div>\n    <span class="mat-timepicker-content-layout-separator">&#58;</span>\n    <div class="mat-timepicker-content-layout-minutes">\n      <ng-content select="[minutes]"></ng-content>\n    </div>\n\n    <ng-content select="[mat-time-period]"></ng-content>\n  </div>\n\n  <ng-content></ng-content>\n</div>\n',
      styles: ["h6.mat-timepicker-content-layout-title{margin-top:0rem;margin-bottom:1.25rem;letter-spacing:.05rem}.mat-timepicker-content-layout-values{display:flex;justify-content:center}.mat-time-period{margin-left:.75rem}.mat-timepicker-content-layout-horizontal h6.mat-timepicker-content-layout-title{margin-bottom:0}.mat-timepicker-content-layout-horizontal .mat-timepicker-content-layout-container{display:flex;gap:4rem;align-items:center}.mat-timepicker-content-layout-horizontal .mat-timepicker-content-layout-values{flex-wrap:wrap}.mat-timepicker-content-layout-horizontal .mat-time-period{margin-top:.75rem;margin-left:0}.mat-timepicker-content-layout-hours,.mat-timepicker-content-layout-minutes{width:6rem}.mat-timepicker-content-layout-separator{display:flex;justify-content:center;align-self:center;width:1.5rem;height:1.75rem;font-weight:500}\n"]
    }]
  }], null, {
    title: [{
      type: Input
    }],
    orientation: [{
      type: Input
    }]
  });
})();
var MatTimePeriod = class _MatTimePeriod {
  /** Whether the time period is vertically aligned. */
  get vertical() {
    return this._vertical;
  }
  set vertical(value) {
    this._vertical = coerceBooleanProperty(value);
  }
  get period() {
    return this._period;
  }
  set period(value) {
    this._period = value || "am";
  }
  get disabledPeriod() {
    return this._disabledPeriod;
  }
  set disabledPeriod(value) {
    this._disabledPeriod = value;
  }
  constructor(_intl) {
    this._intl = _intl;
    this._vertical = true;
    this._period = "am";
    this._disabledPeriod = null;
    this.periodChanged = new EventEmitter();
  }
  setPeriod(event, period) {
    event.preventDefault();
    this.period = period;
    this.periodChanged.emit(period);
  }
  _isPeriodDisabled(period) {
    if (!this.disabledPeriod) {
      return false;
    }
    return this.disabledPeriod === period;
  }
  static {
    this.ɵfac = function MatTimePeriod_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimePeriod)(ɵɵdirectiveInject(MatTimepickerIntl));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimePeriod,
      selectors: [["mat-time-period"]],
      hostAttrs: [1, "mat-time-period"],
      hostVars: 5,
      hostBindings: function MatTimePeriod_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵattribute("aria-orientation", ctx.vertical ? "vertical" : "horizontal");
          ɵɵclassProp("mat-time-period-vertical", ctx.vertical)("mat-time-period-horizontal", !ctx.vertical);
        }
      },
      inputs: {
        vertical: "vertical",
        period: "period",
        disabledPeriod: "disabledPeriod"
      },
      outputs: {
        periodChanged: "periodChanged"
      },
      standalone: true,
      features: [ɵɵStandaloneFeature],
      decls: 5,
      vars: 11,
      consts: [["tabindex", "0", "matRipple", "", 1, "mat-time-period-item", 3, "click", "keydown.space"], [3, "vertical"]],
      template: function MatTimePeriod_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵlistener("click", function MatTimePeriod_Template_div_click_0_listener($event) {
            return ctx.setPeriod($event, "am");
          })("keydown.space", function MatTimePeriod_Template_div_keydown_space_0_listener($event) {
            return ctx.setPeriod($event, "am");
          });
          ɵɵtext(1);
          ɵɵelementEnd();
          ɵɵelement(2, "mat-divider", 1);
          ɵɵelementStart(3, "div", 0);
          ɵɵlistener("click", function MatTimePeriod_Template_div_click_3_listener($event) {
            return ctx.setPeriod($event, "pm");
          })("keydown.space", function MatTimePeriod_Template_div_keydown_space_3_listener($event) {
            return ctx.setPeriod($event, "pm");
          });
          ɵɵtext(4);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵclassProp("mat-time-period-item-active", ctx.period === "am")("mat-time-period-item-disabled", ctx._isPeriodDisabled("am"));
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._intl.am, "\n");
          ɵɵadvance();
          ɵɵproperty("vertical", !ctx.vertical);
          ɵɵadvance();
          ɵɵclassProp("mat-time-period-item-active", ctx.period === "pm")("mat-time-period-item-disabled", ctx._isPeriodDisabled("pm"));
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._intl.pm, "\n");
        }
      },
      dependencies: [MatDividerModule, MatDivider, MatRippleModule, MatRipple],
      styles: [".mat-time-period{display:flex;text-align:center;border-width:1px;border-style:solid;border-radius:.25rem;box-sizing:border-box}.mat-time-period-vertical{flex-direction:column;width:3.25rem;height:4.5rem}.mat-time-period-vertical .mat-time-period-item:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-time-period-vertical .mat-time-period-item:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-time-period-horizontal{flex-direction:row;max-width:13.5rem;width:100%;height:2.5rem}.mat-time-period-horizontal .mat-time-period-item:first-child{border-top-left-radius:inherit;border-bottom-left-radius:inherit}.mat-time-period-horizontal .mat-time-period-item:last-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}.mat-time-period-item{display:flex;flex-direction:column;justify-content:center;flex-grow:1;height:100%;font-size:.875rem;font-weight:500;cursor:pointer}.mat-time-period-item-disabled{pointer-events:none}\n"],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimePeriod, [{
    type: Component,
    args: [{
      selector: "mat-time-period",
      standalone: true,
      imports: [MatDividerModule, MatRippleModule],
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-time-period",
        "[class.mat-time-period-vertical]": "vertical",
        "[class.mat-time-period-horizontal]": "!vertical",
        "[attr.aria-orientation]": 'vertical ? "vertical" : "horizontal"'
      },
      template: `<div
  tabindex="0"
  class="mat-time-period-item"
  matRipple
  [class.mat-time-period-item-active]="period === 'am'"
  [class.mat-time-period-item-disabled]="_isPeriodDisabled('am')"
  (click)="setPeriod($event, 'am')"
  (keydown.space)="setPeriod($event, 'am')"
>
  {{ _intl.am }}
</div>
<mat-divider [vertical]="!vertical"></mat-divider>
<div
  tabindex="0"
  class="mat-time-period-item"
  matRipple
  [class.mat-time-period-item-active]="period === 'pm'"
  [class.mat-time-period-item-disabled]="_isPeriodDisabled('pm')"
  (click)="setPeriod($event, 'pm')"
  (keydown.space)="setPeriod($event, 'pm')"
>
  {{ _intl.pm }}
</div>
`,
      styles: [".mat-time-period{display:flex;text-align:center;border-width:1px;border-style:solid;border-radius:.25rem;box-sizing:border-box}.mat-time-period-vertical{flex-direction:column;width:3.25rem;height:4.5rem}.mat-time-period-vertical .mat-time-period-item:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mat-time-period-vertical .mat-time-period-item:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-time-period-horizontal{flex-direction:row;max-width:13.5rem;width:100%;height:2.5rem}.mat-time-period-horizontal .mat-time-period-item:first-child{border-top-left-radius:inherit;border-bottom-left-radius:inherit}.mat-time-period-horizontal .mat-time-period-item:last-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}.mat-time-period-item{display:flex;flex-direction:column;justify-content:center;flex-grow:1;height:100%;font-size:.875rem;font-weight:500;cursor:pointer}.mat-time-period-item-disabled{pointer-events:none}\n"]
    }]
  }], () => [{
    type: MatTimepickerIntl
  }], {
    vertical: [{
      type: Input
    }],
    period: [{
      type: Input
    }],
    disabledPeriod: [{
      type: Input
    }],
    periodChanged: [{
      type: Output
    }]
  });
})();
var MatClockDials = class _MatClockDials extends MatTimeFaceBase {
  constructor(_intl, _timeAdapter, _ngZone, _elementRef, _cdr) {
    super(_timeAdapter);
    this._intl = _intl;
    this._ngZone = _ngZone;
    this._elementRef = _elementRef;
    this._cdr = _cdr;
    this.isHoursView = true;
    this._view = new BehaviorSubject("hours");
    this._viewSubscription = Subscription.EMPTY;
  }
  ngOnInit() {
    this._viewSubscription = this._view.subscribe((view) => this.isHoursView = view === "hours");
  }
  ngOnDestroy() {
    this._viewSubscription?.unsubscribe();
    this._viewSubscription = null;
  }
  /** Changes clock dial view. */
  onViewChange(event, view) {
    event.preventDefault();
    this._view.next(view);
  }
  focusActiveCell() {
    this._ngZone.runOutsideAngular(() => {
      this._ngZone.onStable.pipe(take(1)).subscribe(() => {
        const activeCell = this._elementRef.nativeElement.querySelector(".mat-timepicker-content .mat-clock-dial-cell-active");
        if (activeCell) {
          activeCell.focus();
          return;
        }
        const activePoint = this._elementRef.nativeElement.querySelector(".mat-timepicker-content .mat-clock-dial-hand-point");
        if (activePoint) {
          activePoint.focus();
        }
      });
    });
  }
  _withZeroPrefix(value) {
    if (value === 0) {
      return "00";
    }
    return withZeroPrefixMeridiem(value, this.isMeridiem);
  }
  _onMinuteSelected(minute) {
    super._onMinuteSelected(minute);
    this._cdr.detectChanges();
  }
  /** Handles hour selection. */
  _onHourChanged({
    hour,
    changeView = false
  }) {
    if (changeView) {
      this._view.next("minutes");
    }
    this._onHourSelected(hour);
    this._cdr.detectChanges();
  }
  static {
    this.ɵfac = function MatClockDials_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatClockDials)(ɵɵdirectiveInject(MatTimepickerIntl), ɵɵdirectiveInject(TimeAdapter, 8), ɵɵdirectiveInject(NgZone), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatClockDials,
      selectors: [["mat-clock-dials"]],
      hostAttrs: ["role", "dial", 1, "mat-clock-dials"],
      inputs: {
        orientation: "orientation",
        touchUi: "touchUi"
      },
      exportAs: ["matClockDials"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 9,
      vars: 13,
      consts: [[3, "title", "orientation"], ["tabindex", "0", "hours", "", 1, "mat-clock-dial-value", 3, "click", "keydown.space"], ["tabindex", "0", "minutes", "", 1, "mat-clock-dial-value", 3, "click", "keydown.space"], ["mat-time-period", "", 3, "ngIf"], [1, "mat-clock-dial-faces"], [3, "color", "selectedHour", "isMeridiem", "availableHours", "touchUi"], [3, "color", "selectedMinute", "interval", "availableMinutes", "touchUi"], [3, "periodChanged", "period", "disabledPeriod", "vertical"], [3, "selectedChange", "keydown", "color", "selectedHour", "isMeridiem", "availableHours", "touchUi"], [3, "selectedChange", "keydown", "color", "selectedMinute", "interval", "availableMinutes", "touchUi"]],
      template: function MatClockDials_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "mat-timepicker-content-layout", 0)(1, "div", 1);
          ɵɵlistener("click", function MatClockDials_Template_div_click_1_listener($event) {
            return ctx.onViewChange($event, "hours");
          })("keydown.space", function MatClockDials_Template_div_keydown_space_1_listener($event) {
            return ctx.onViewChange($event, "hours");
          });
          ɵɵtext(2);
          ɵɵelementEnd();
          ɵɵelementStart(3, "div", 2);
          ɵɵlistener("click", function MatClockDials_Template_div_click_3_listener($event) {
            return ctx.onViewChange($event, "minutes");
          })("keydown.space", function MatClockDials_Template_div_keydown_space_3_listener($event) {
            return ctx.onViewChange($event, "minutes");
          });
          ɵɵtext(4);
          ɵɵelementEnd();
          ɵɵtemplate(5, MatClockDials_ng_template_5_Template, 1, 3, "ng-template", 3);
          ɵɵelementStart(6, "div", 4);
          ɵɵtemplate(7, MatClockDials_Conditional_7_Template, 1, 6, "mat-hours-clock-dial", 5)(8, MatClockDials_Conditional_8_Template, 1, 6, "mat-minutes-clock-dial", 6);
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          ɵɵproperty("title", ctx._intl.dialsTitle)("orientation", ctx.orientation);
          ɵɵadvance();
          ɵɵclassProp("mat-clock-dial-value-active", ctx.isHoursView);
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._withZeroPrefix(ctx.selectedHour), " ");
          ɵɵadvance();
          ɵɵclassProp("mat-clock-dial-value-active", !ctx.isHoursView);
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._withZeroPrefix(ctx.selectedMinute), " ");
          ɵɵadvance();
          ɵɵproperty("ngIf", ctx.isMeridiem);
          ɵɵadvance();
          ɵɵclassProp("mat-clock-dial-faces-horizontal", ctx.orientation === "horizontal");
          ɵɵadvance();
          ɵɵconditional(ctx.isHoursView ? 7 : -1);
          ɵɵadvance();
          ɵɵconditional(!ctx.isHoursView ? 8 : -1);
        }
      },
      dependencies: [CommonModule, NgIf, MatTimepickerContentLayout, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod],
      styles: [".mat-clock-dial-values{display:flex;width:100%}.mat-clock-dial-value{display:flex;align-items:center;justify-content:center;height:4.5rem;border-radius:.25rem;cursor:pointer}.mat-clock-dial-faces{margin-top:2.25rem}.mat-clock-dial-faces.mat-clock-dial-faces-horizontal{margin-top:0}.mat-clock-dial-cell.mat-clock-dial-cell-disabled:hover{cursor:default}\n"],
      encapsulation: 2,
      data: {
        animation: [enterLeaveAnimation]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatClockDials, [{
    type: Component,
    args: [{
      selector: "mat-clock-dials",
      standalone: true,
      imports: [CommonModule, MatTimepickerContentLayout, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod],
      exportAs: "matClockDials",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        role: "dial",
        class: "mat-clock-dials"
      },
      animations: [enterLeaveAnimation],
      template: `<mat-timepicker-content-layout [title]="_intl.dialsTitle" [orientation]="orientation">
  <div
    tabindex="0"
    class="mat-clock-dial-value"
    hours
    [class.mat-clock-dial-value-active]="isHoursView"
    (click)="onViewChange($event, 'hours')"
    (keydown.space)="onViewChange($event, 'hours')"
  >
    {{ _withZeroPrefix(selectedHour) }}
  </div>
  <div
    tabindex="0"
    class="mat-clock-dial-value"
    minutes
    [class.mat-clock-dial-value-active]="!isHoursView"
    (click)="onViewChange($event, 'minutes')"
    (keydown.space)="onViewChange($event, 'minutes')"
  >
    {{ _withZeroPrefix(selectedMinute) }}
  </div>

  <ng-template mat-time-period [ngIf]="isMeridiem">
    <mat-time-period
      [period]="period"
      [disabledPeriod]="disabledPeriod"
      [vertical]="orientation === 'vertical'"
      (periodChanged)="_onPeriodChanged($event)"
    ></mat-time-period>
  </ng-template>

  <div
    class="mat-clock-dial-faces"
    [class.mat-clock-dial-faces-horizontal]="orientation === 'horizontal'"
  >
    @if (isHoursView) {
      <mat-hours-clock-dial
        [@enterLeaveAnimation]
        [color]="color"
        [selectedHour]="selectedHour"
        [isMeridiem]="isMeridiem"
        [availableHours]="_getAvailableHours()"
        [touchUi]="touchUi"
        (selectedChange)="_onHourChanged($event)"
        (keydown)="_onKeydown($event, 'hour')"
      ></mat-hours-clock-dial>
    }

    @if (!isHoursView) {
      <mat-minutes-clock-dial
        [@enterLeaveAnimation]
        [color]="color"
        [selectedMinute]="selectedMinute"
        [interval]="minuteInterval"
        [availableMinutes]="availableMinutes"
        [touchUi]="touchUi"
        (selectedChange)="_onMinuteSelected($event)"
        (keydown)="_onKeydown($event, 'minute')"
      ></mat-minutes-clock-dial>
    }
  </div>
</mat-timepicker-content-layout>
`,
      styles: [".mat-clock-dial-values{display:flex;width:100%}.mat-clock-dial-value{display:flex;align-items:center;justify-content:center;height:4.5rem;border-radius:.25rem;cursor:pointer}.mat-clock-dial-faces{margin-top:2.25rem}.mat-clock-dial-faces.mat-clock-dial-faces-horizontal{margin-top:0}.mat-clock-dial-cell.mat-clock-dial-cell-disabled:hover{cursor:default}\n"]
    }]
  }], () => [{
    type: MatTimepickerIntl
  }, {
    type: TimeAdapter,
    decorators: [{
      type: Optional
    }]
  }, {
    type: NgZone
  }, {
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }], {
    orientation: [{
      type: Input
    }],
    touchUi: [{
      type: Input
    }]
  });
})();
var MatHourInput = class _MatHourInput extends MatTimeInputBase {
  get availableHours() {
    return this._availableHours;
  }
  set availableHours(value) {
    this._availableHours = value;
  }
  constructor(element, _cdr, _document) {
    super(element, _cdr, _document);
    this._availableHours = [];
  }
  _withZeroPrefix(value) {
    return withZeroPrefixMeridiem(value, this.isMeridiem);
  }
  _formatValue(hour) {
    const getValue = () => {
      if (this.isMeridiem) {
        if (hour === 0 || hour === 24) {
          return 12;
        }
      }
      if (hour === 24) {
        return 0;
      }
      return this.isMeridiem && hour > 12 ? hour - 12 : hour;
    };
    const value = getValue();
    if (this.isMeridiem) {
      if (!this.availableHours.length) {
        return this.value;
      }
      if (value === 12 && this.availableHours.includes(12)) {
        return 12;
      }
      if (value === 12 && !this.availableHours.includes(12)) {
        return Math.min(...this.availableHours);
      }
      if (value >= 1 && value < 12) {
        const maxHour = this.availableHours[this.availableHours.length - 1];
        return Math.min(Math.max(value, Math.min(...this.availableHours)), maxHour);
      }
    }
    return Math.min(Math.max(value, Math.min(...this.availableHours)), Math.max(...this.availableHours));
  }
  static {
    this.ɵfac = function MatHourInput_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatHourInput)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(DOCUMENT));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatHourInput,
      selectors: [["input", "matHourInput", ""]],
      hostAttrs: [1, "mat-time-input"],
      hostBindings: function MatHourInput_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("focus", function MatHourInput_focus_HostBindingHandler($event) {
            return ctx.focus($event);
          })("blur", function MatHourInput_blur_HostBindingHandler($event) {
            return ctx.blur($event);
          });
        }
      },
      inputs: {
        availableHours: "availableHours",
        isMeridiem: "isMeridiem"
      },
      exportAs: ["matTimeInput"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatHourInput, [{
    type: Directive,
    args: [{
      selector: "input[matHourInput]",
      standalone: true,
      exportAs: "matTimeInput",
      host: {
        class: "mat-time-input",
        "(focus)": "focus($event)",
        "(blur)": "blur($event)"
      }
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: Document,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }], {
    availableHours: [{
      type: Input
    }],
    isMeridiem: [{
      type: Input
    }]
  });
})();
var MatMinuteInput = class _MatMinuteInput extends MatTimeInputBase {
  /** Step over minutes. */
  get interval() {
    return this._interval;
  }
  set interval(value) {
    this._interval = coerceNumberProperty(value) || 1;
  }
  get availableMinutes() {
    return this._availableMinutes;
  }
  set availableMinutes(value) {
    this._availableMinutes = value;
  }
  constructor(element, _cdr, _document) {
    super(element, _cdr, _document);
    this._interval = 1;
    this._availableMinutes = [];
  }
  _withZeroPrefix(value) {
    return withZeroPrefix(value);
  }
  _formatValue(value) {
    if (!this.availableMinutes.length) {
      return this.value;
    }
    const roundedValue = Math.round(value / this.interval) * this.interval;
    return Math.min(Math.max(roundedValue, Math.min(...this.availableMinutes)), Math.max(...this.availableMinutes));
  }
  static {
    this.ɵfac = function MatMinuteInput_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatMinuteInput)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(DOCUMENT));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatMinuteInput,
      selectors: [["input", "matMinuteInput", ""]],
      hostAttrs: [1, "mat-time-input"],
      hostBindings: function MatMinuteInput_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("focus", function MatMinuteInput_focus_HostBindingHandler($event) {
            return ctx.focus($event);
          })("blur", function MatMinuteInput_blur_HostBindingHandler($event) {
            return ctx.blur($event);
          });
        }
      },
      inputs: {
        interval: "interval",
        availableMinutes: "availableMinutes"
      },
      exportAs: ["matTimeInput"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatMinuteInput, [{
    type: Directive,
    args: [{
      selector: "input[matMinuteInput]",
      standalone: true,
      exportAs: "matTimeInput",
      host: {
        class: "mat-time-input",
        "(focus)": "focus($event)",
        "(blur)": "blur($event)"
      }
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: Document,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }], {
    interval: [{
      type: Input
    }],
    availableMinutes: [{
      type: Input
    }]
  });
})();
var MatTimeInputs = class _MatTimeInputs extends MatTimeFaceBase {
  constructor(_intl, _timeAdapter, _ngZone, _elementRef) {
    super(_timeAdapter);
    this._intl = _intl;
    this._ngZone = _ngZone;
    this._elementRef = _elementRef;
    this._skipNextTickFocus = false;
  }
  focusActiveCell() {
    this._ngZone.runOutsideAngular(() => {
      this._ngZone.onStable.pipe(take(1)).subscribe(() => {
        const activeCell = this._elementRef.nativeElement.querySelector(".mat-timepicker-content input");
        if (activeCell && !this._skipNextTickFocus) {
          activeCell.focus();
          this._skipNextTickFocus = true;
        }
      });
    });
  }
  static {
    this.ɵfac = function MatTimeInputs_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimeInputs)(ɵɵdirectiveInject(MatTimepickerIntl), ɵɵdirectiveInject(TimeAdapter, 8), ɵɵdirectiveInject(NgZone), ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimeInputs,
      selectors: [["mat-time-inputs"]],
      hostAttrs: [1, "mat-time-inputs"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 10,
      vars: 12,
      consts: [[3, "title"], ["appearance", "outline", "hours", "", 1, "mat-time-inputs-field", 3, "color"], ["type", "text", "inputmode", "numeric", "maxlength", "2", "matInput", "", "matHourInput", "", 3, "timeChanged", "keydown", "isMeridiem", "value", "availableHours"], ["appearance", "outline", "minutes", "", 1, "mat-time-inputs-field", 3, "color"], ["type", "text", "inputmode", "numeric", "maxlength", "2", "matInput", "", "matMinuteInput", "", 3, "timeChanged", "keydown", "value", "interval", "availableMinutes"], ["mat-time-period", "", 3, "ngIf"], [3, "periodChanged", "period", "disabledPeriod"]],
      template: function MatTimeInputs_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "mat-timepicker-content-layout", 0)(1, "mat-form-field", 1)(2, "input", 2);
          ɵɵlistener("timeChanged", function MatTimeInputs_Template_input_timeChanged_2_listener($event) {
            return ctx._onHourSelected($event);
          })("keydown", function MatTimeInputs_Template_input_keydown_2_listener($event) {
            return ctx._onKeydown($event, "hour");
          });
          ɵɵelementEnd();
          ɵɵelementStart(3, "mat-hint");
          ɵɵtext(4);
          ɵɵelementEnd()();
          ɵɵelementStart(5, "mat-form-field", 3)(6, "input", 4);
          ɵɵlistener("timeChanged", function MatTimeInputs_Template_input_timeChanged_6_listener($event) {
            return ctx._onMinuteSelected($event);
          })("keydown", function MatTimeInputs_Template_input_keydown_6_listener($event) {
            return ctx._onKeydown($event, "minute");
          });
          ɵɵelementEnd();
          ɵɵelementStart(7, "mat-hint");
          ɵɵtext(8);
          ɵɵelementEnd()();
          ɵɵtemplate(9, MatTimeInputs_ng_template_9_Template, 1, 2, "ng-template", 5);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("title", ctx._intl.inputsTitle);
          ɵɵadvance();
          ɵɵproperty("color", ctx.color);
          ɵɵadvance();
          ɵɵproperty("isMeridiem", ctx.isMeridiem)("value", ctx.selectedHour)("availableHours", ctx._getAvailableHours());
          ɵɵadvance(2);
          ɵɵtextInterpolate(ctx._intl.hourInputHint);
          ɵɵadvance();
          ɵɵproperty("color", ctx.color);
          ɵɵadvance();
          ɵɵproperty("value", ctx.selectedMinute)("interval", ctx.minuteInterval)("availableMinutes", ctx.availableMinutes);
          ɵɵadvance(2);
          ɵɵtextInterpolate(ctx._intl.minuteInputHint);
          ɵɵadvance();
          ɵɵproperty("ngIf", ctx.isMeridiem);
        }
      },
      dependencies: [CommonModule, NgIf, MatFormFieldModule, MatFormField, MatHint, MatInputModule, MatInput, MatTimepickerContentLayout, MatHourInput, MatMinuteInput, MatTimePeriod],
      styles: [".mat-time-inputs{display:block}.mat-time-inputs .mat-timepicker-content-layout-separator{margin-top:-1.5rem}.mat-time-inputs-field{display:block}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-flex{margin:0}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-text-field-wrapper{top:0;margin:0}.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__leading,.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__notch,.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__trailing{border-style:none}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-subscript-wrapper{margin-top:.75rem;line-height:1;letter-spacing:.05rem}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-subscript-wrapper .mat-mdc-form-field-hint-wrapper{padding-left:0}.mat-time-inputs-field .mat-mdc-form-field-bottom-align:before{height:0}.mat-time-inputs-field .mat-mdc-form-field-infix{border-top:none;text-align:center}.mat-time-inputs-field input.mat-mdc-input-element{margin-top:0;text-align:center}\n"],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimeInputs, [{
    type: Component,
    args: [{
      selector: "mat-time-inputs",
      standalone: true,
      imports: [CommonModule, MatFormFieldModule, MatInputModule, MatTimepickerContentLayout, MatHourInput, MatMinuteInput, MatTimePeriod],
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-time-inputs"
      },
      template: `<mat-timepicker-content-layout [title]="_intl.inputsTitle">
  <mat-form-field
    class="mat-time-inputs-field"
    appearance="outline"
    hours
    [color]="color"
  >
    <input
      type="text"
      inputmode="numeric"
      maxlength="2"
      matInput
      matHourInput
      [isMeridiem]="isMeridiem"
      [value]="selectedHour"
      [availableHours]="_getAvailableHours()"
      (timeChanged)="_onHourSelected($event)"
      (keydown)="_onKeydown($event, 'hour')"
    />
    <mat-hint>{{_intl.hourInputHint}}</mat-hint>
  </mat-form-field>
  <mat-form-field
    class="mat-time-inputs-field"
    appearance="outline"
    minutes
    [color]="color"
  >
    <input
      type="text"
      inputmode="numeric"
      maxlength="2"
      matInput
      matMinuteInput
      [value]="selectedMinute"
      [interval]="minuteInterval"
      [availableMinutes]="availableMinutes"
      (timeChanged)="_onMinuteSelected($event)"
      (keydown)="_onKeydown($event, 'minute')"
    />
    <mat-hint>{{_intl.minuteInputHint}}</mat-hint>
  </mat-form-field>

  <ng-template mat-time-period [ngIf]="isMeridiem">
    <mat-time-period
      [period]="period"
      [disabledPeriod]="disabledPeriod"
      (periodChanged)="_onPeriodChanged($event)"
    ></mat-time-period>
  </ng-template>
</mat-timepicker-content-layout>
`,
      styles: [".mat-time-inputs{display:block}.mat-time-inputs .mat-timepicker-content-layout-separator{margin-top:-1.5rem}.mat-time-inputs-field{display:block}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-flex{margin:0}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-text-field-wrapper{top:0;margin:0}.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__leading,.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__notch,.mat-time-inputs-field.mat-form-field-appearance-outline:not(.mat-focused) .mdc-notched-outline__trailing{border-style:none}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-subscript-wrapper{margin-top:.75rem;line-height:1;letter-spacing:.05rem}.mat-time-inputs-field.mat-form-field-appearance-outline .mat-mdc-form-field-subscript-wrapper .mat-mdc-form-field-hint-wrapper{padding-left:0}.mat-time-inputs-field .mat-mdc-form-field-bottom-align:before{height:0}.mat-time-inputs-field .mat-mdc-form-field-infix{border-top:none;text-align:center}.mat-time-inputs-field input.mat-mdc-input-element{margin-top:0;text-align:center}\n"]
    }]
  }], () => [{
    type: MatTimepickerIntl
  }, {
    type: TimeAdapter,
    decorators: [{
      type: Optional
    }]
  }, {
    type: NgZone
  }, {
    type: ElementRef
  }], null);
})();
var MatTimeSelectionModel = class _MatTimeSelectionModel {
  /**
   * Updates the current selection in the model.
   * @param value New selection that should be assigned.
   * @param source Object that triggered the selection change.
   */
  updateSelection(value, source) {
    const oldValue = this.selection;
    this.selection = value;
    this._selectionChanged.next({
      selection: value,
      source,
      oldValue
    });
  }
  constructor(_adapter) {
    this._adapter = _adapter;
    this._selectionChanged = new Subject();
    this.selectionChanged = this._selectionChanged;
  }
  ngOnDestroy() {
    this._selectionChanged.complete();
  }
  static {
    this.ɵfac = function MatTimeSelectionModel_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimeSelectionModel)(ɵɵinject(TimeAdapter));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _MatTimeSelectionModel,
      factory: _MatTimeSelectionModel.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimeSelectionModel, [{
    type: Injectable
  }], () => [{
    type: TimeAdapter
  }], null);
})();
var MatSingleTimeSelectionModel = class _MatSingleTimeSelectionModel extends MatTimeSelectionModel {
  constructor(adapter) {
    super(adapter);
  }
  /**
   * Adds a time to the current selection. In the case of a single time selection, the added time
   * simply overwrites the previous selection
   */
  add(time) {
    super.updateSelection(time, this);
  }
  /** Clones the selection model. */
  clone() {
    const clone = new _MatSingleTimeSelectionModel(this._adapter);
    clone.updateSelection(this.selection, this);
    return clone;
  }
  static {
    this.ɵfac = function MatSingleTimeSelectionModel_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatSingleTimeSelectionModel)(ɵɵinject(TimeAdapter));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _MatSingleTimeSelectionModel,
      factory: _MatSingleTimeSelectionModel.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatSingleTimeSelectionModel, [{
    type: Injectable
  }], () => [{
    type: TimeAdapter
  }], null);
})();
function MAT_SINGLE_TIME_SELECTION_MODEL_FACTORY(parent, adapter) {
  return parent || new MatSingleTimeSelectionModel(adapter);
}
var MAT_SINGLE_TIME_SELECTION_MODEL_PROVIDER = {
  provide: MatTimeSelectionModel,
  deps: [[new Optional(), new SkipSelf(), MatSingleTimeSelectionModel], TimeAdapter],
  useFactory: MAT_SINGLE_TIME_SELECTION_MODEL_FACTORY
};
var MatTimepickerContent = class _MatTimepickerContent {
  constructor(intl, _globalModel, _changeDetectorRef) {
    this._globalModel = _globalModel;
    this._changeDetectorRef = _changeDetectorRef;
    this._actionsPortal = null;
    this._isAnimating = false;
    this._animationDone = new Subject();
    this._subscriptions = new Subscription();
    this._closeButtonText = intl.closeTimepickerLabel;
  }
  ngOnInit() {
    this._animationState = this.timepicker.openAs === "dialog" ? "enter-dialog" : "enter-dropdown";
  }
  ngAfterViewInit() {
    this._subscriptions.add(this.timepicker.stateChanges.subscribe(() => {
      this._changeDetectorRef.markForCheck();
    }));
    (this._dials || this._inputs)?.focusActiveCell();
  }
  ngOnDestroy() {
    this._subscriptions.unsubscribe();
    this._animationDone.complete();
  }
  /** Changes animation state while closing timepicker content. */
  _startExitAnimation() {
    this._animationState = "void";
    this._changeDetectorRef.markForCheck();
  }
  _handleAnimationEvent(event) {
    this._isAnimating = event.phaseName === "start";
    if (!this._isAnimating) {
      this._animationDone.next();
    }
  }
  onToggleMode(mode) {
    this.mode = mode;
  }
  _getSelected() {
    return this._model?.selection;
  }
  /** Applies the current pending selection to the global model. */
  _applyPendingSelection() {
    if (this._model !== this._globalModel) {
      this._globalModel.updateSelection(this._model.selection, this);
    }
  }
  /**
   * Assigns a new portal containing the timepicker actions.
   * @param portal Portal with the actions to be assigned.
   * @param forceRerender Whether a re-render of the portal should be triggered. This isn't
   * necessary if the portal is assigned during initialization, but it may be required if it's
   * added at a later point.
   */
  _assignActions(portal, forceRerender) {
    this._model = this._globalModel.clone();
    this._actionsPortal = portal;
    if (forceRerender) {
      this._changeDetectorRef.detectChanges();
    }
  }
  _handleUserSelection(event) {
    const value = event;
    this._model.add(value);
  }
  static {
    this.ɵfac = function MatTimepickerContent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerContent)(ɵɵdirectiveInject(MatTimepickerIntl), ɵɵdirectiveInject(MatTimeSelectionModel), ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepickerContent,
      selectors: [["mat-timepicker-content"]],
      viewQuery: function MatTimepickerContent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(MatClockDials, 5);
          ɵɵviewQuery(MatTimeInputs, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx._dials = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx._inputs = _t.first);
        }
      },
      hostAttrs: [1, "mat-timepicker-content"],
      hostVars: 5,
      hostBindings: function MatTimepickerContent_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵsyntheticHostListener("@transformPanel.start", function MatTimepickerContent_animation_transformPanel_start_HostBindingHandler($event) {
            return ctx._handleAnimationEvent($event);
          })("@transformPanel.done", function MatTimepickerContent_animation_transformPanel_done_HostBindingHandler($event) {
            return ctx._handleAnimationEvent($event);
          });
        }
        if (rf & 2) {
          ɵɵsyntheticHostProperty("@transformPanel", ctx._animationState);
          ɵɵclassMap(ctx.color ? "mat-" + ctx.color : "");
          ɵɵclassProp("mat-timepicker-content-touch", ctx.timepicker.touchUi);
        }
      },
      exportAs: ["matTimepickerContent"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      decls: 5,
      vars: 7,
      consts: [["cdkTrapFocus", "", "role", "dialog", 1, "mat-timepicker-content-container"], ["type", "button", "mat-raised-button", "", 1, "mat-timepicker-close-button", 3, "focus", "blur", "click", "color"], [3, "_userSelection", "id", "color", "isMeridiem", "selected", "minTime", "maxTime", "minuteInterval"], [1, "mat-timepicker-content-actions"], ["mat-icon-button", "", 1, "mat-time-toggle-mode-button"], [3, "cdkPortalOutlet"], ["mat-icon-button", "", 1, "mat-time-toggle-mode-button", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "height", "24", "width", "24", "viewBox", "0 0 24 24"], ["d", "m15.175 16.625 1.475-1.45-3.6-3.6V7.1h-2.075v5.3ZM12 21.95q-2.075 0-3.887-.787-1.813-.788-3.15-2.125-1.338-1.338-2.125-3.151Q2.05 14.075 2.05 12t.788-3.887q.787-1.813 2.125-3.15Q6.3 3.625 8.113 2.837 9.925 2.05 12 2.05t3.887.787q1.813.788 3.151 2.126 1.337 1.337 2.125 3.15.787 1.812.787 3.887t-.787 3.887q-.788 1.813-2.125 3.151-1.338 1.337-3.151 2.125-1.812.787-3.887.787ZM12 12Zm0 7.8q3.225 0 5.513-2.275Q19.8 15.25 19.8 12q0-3.25-2.287-5.525Q15.225 4.2 12 4.2T6.488 6.475Q4.2 8.75 4.2 12q0 3.25 2.288 5.525Q8.775 19.8 12 19.8Z"], [3, "_userSelection", "id", "color", "isMeridiem", "selected", "minTime", "maxTime", "minuteInterval", "orientation", "touchUi"], ["d", "M4 19q-.825 0-1.412-.587Q2 17.825 2 17V7q0-.825.588-1.412Q3.175 5 4 5h16q.825 0 1.413.588Q22 6.175 22 7v10q0 .825-.587 1.413Q20.825 19 20 19Zm0-2h16V7H4v10Zm4-1h8v-2H8Zm-3-3h2v-2H5Zm3 0h2v-2H8Zm3 0h2v-2h-2Zm3 0h2v-2h-2Zm3 0h2v-2h-2ZM5 10h2V8H5Zm3 0h2V8H8Zm3 0h2V8h-2Zm3 0h2V8h-2Zm3 0h2V8h-2ZM4 17V7v10Z"]],
      template: function MatTimepickerContent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵtemplate(1, MatTimepickerContent_Case_1_Template, 4, 9)(2, MatTimepickerContent_Case_2_Template, 4, 11);
          ɵɵelementStart(3, "button", 1);
          ɵɵlistener("focus", function MatTimepickerContent_Template_button_focus_3_listener() {
            return ctx._closeButtonFocused = true;
          })("blur", function MatTimepickerContent_Template_button_blur_3_listener() {
            return ctx._closeButtonFocused = false;
          })("click", function MatTimepickerContent_Template_button_click_3_listener() {
            return ctx.timepicker.close();
          });
          ɵɵtext(4);
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          let tmp_1_0;
          let tmp_2_0;
          ɵɵattribute("aria-modal", true)("aria-labelledby", (tmp_1_0 = ctx._dialogLabelId) !== null && tmp_1_0 !== void 0 ? tmp_1_0 : void 0);
          ɵɵadvance();
          ɵɵconditional((tmp_2_0 = ctx.mode) === "input" ? 1 : tmp_2_0 === "dial" ? 2 : -1);
          ɵɵadvance(2);
          ɵɵclassProp("cdk-visually-hidden", !ctx._closeButtonFocused);
          ɵɵproperty("color", ctx.color || "primary");
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._closeButtonText, " ");
        }
      },
      dependencies: [CommonModule, PortalModule, CdkPortalOutlet, A11yModule, CdkTrapFocus, MatTimeInputs, MatClockDials, MatButtonModule, MatButton, MatIconButton],
      styles: [".mat-timepicker-content{display:block}.mat-timepicker-content-container{position:relative;display:flex;flex-direction:column;padding:1rem 1.5rem}.mat-timepicker-content-actions{display:flex;justify-content:space-between;margin-right:-1rem;margin-top:1.5rem}.mat-time-toggle-mode-button{width:3.25rem;height:3.25rem;margin-left:-.75rem;margin-bottom:-.25rem}button.mat-timepicker-close-button{position:absolute;top:100%;left:0;margin-top:.5rem}.ng-animating button.mat-timepicker-close-button{display:none}\n"],
      encapsulation: 2,
      data: {
        animation: [matTimepickerAnimations.transformPanel, matTimepickerAnimations.fadeInTimepicker]
      },
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerContent, [{
    type: Component,
    args: [{
      selector: "mat-timepicker-content",
      standalone: true,
      imports: [CommonModule, PortalModule, A11yModule, MatTimeInputs, MatClockDials, MatButtonModule],
      exportAs: "matTimepickerContent",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-timepicker-content",
        "[class]": 'color ? "mat-" + color : ""',
        "[@transformPanel]": "_animationState",
        "(@transformPanel.start)": "_handleAnimationEvent($event)",
        "(@transformPanel.done)": "_handleAnimationEvent($event)",
        "[class.mat-timepicker-content-touch]": "timepicker.touchUi"
      },
      animations: [matTimepickerAnimations.transformPanel, matTimepickerAnimations.fadeInTimepicker],
      template: `<div
  cdkTrapFocus
  role="dialog"
  class="mat-timepicker-content-container"
  [attr.aria-modal]="true"
  [attr.aria-labelledby]="_dialogLabelId ?? undefined"
>
  @switch (mode) {
    @case ('input') {
      <mat-time-inputs
        [id]="timepicker.id"
        [color]="color"
        [isMeridiem]="isMeridiem"
        [selected]="_getSelected()"
        [minTime]="timepicker._getMinTime()"
        [maxTime]="timepicker._getMaxTime()"
        [minuteInterval]="minuteInterval"
        (_userSelection)="_handleUserSelection($event)"
      ></mat-time-inputs>

      <div class="mat-timepicker-content-actions">
        @if (showToggleModeButton) {
          <button
            class="mat-time-toggle-mode-button"
            mat-icon-button
            (click)="onToggleMode('dial')"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24"
              width="24"
              viewBox="0 0 24 24"
            >
              <path
                d="m15.175 16.625 1.475-1.45-3.6-3.6V7.1h-2.075v5.3ZM12 21.95q-2.075 0-3.887-.787-1.813-.788-3.15-2.125-1.338-1.338-2.125-3.151Q2.05 14.075 2.05 12t.788-3.887q.787-1.813 2.125-3.15Q6.3 3.625 8.113 2.837 9.925 2.05 12 2.05t3.887.787q1.813.788 3.151 2.126 1.337 1.337 2.125 3.15.787 1.812.787 3.887t-.787 3.887q-.788 1.813-2.125 3.151-1.338 1.337-3.151 2.125-1.812.787-3.887.787ZM12 12Zm0 7.8q3.225 0 5.513-2.275Q19.8 15.25 19.8 12q0-3.25-2.287-5.525Q15.225 4.2 12 4.2T6.488 6.475Q4.2 8.75 4.2 12q0 3.25 2.288 5.525Q8.775 19.8 12 19.8Z"
              />
            </svg>
          </button>
        }

        <ng-template [cdkPortalOutlet]="_actionsPortal"></ng-template>
      </div>
    }

    @case ('dial') {
      <mat-clock-dials
        [id]="timepicker.id"
        [color]="color"
        [isMeridiem]="isMeridiem"
        [selected]="_getSelected()"
        [minTime]="timepicker._getMinTime()"
        [maxTime]="timepicker._getMaxTime()"
        [minuteInterval]="minuteInterval"
        [orientation]="orientation"
        [touchUi]="timepicker.touchUi"
        (_userSelection)="_handleUserSelection($event)"
      ></mat-clock-dials>

      <div class="mat-timepicker-content-actions">
        @if (showToggleModeButton) {
          <button
            class="mat-time-toggle-mode-button"
            mat-icon-button
            (click)="onToggleMode('input')"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24"
              width="24"
              viewBox="0 0 24 24"
            >
              <path
                d="M4 19q-.825 0-1.412-.587Q2 17.825 2 17V7q0-.825.588-1.412Q3.175 5 4 5h16q.825 0 1.413.588Q22 6.175 22 7v10q0 .825-.587 1.413Q20.825 19 20 19Zm0-2h16V7H4v10Zm4-1h8v-2H8Zm-3-3h2v-2H5Zm3 0h2v-2H8Zm3 0h2v-2h-2Zm3 0h2v-2h-2Zm3 0h2v-2h-2ZM5 10h2V8H5Zm3 0h2V8H8Zm3 0h2V8h-2Zm3 0h2V8h-2Zm3 0h2V8h-2ZM4 17V7v10Z"
              />
            </svg>
          </button>
        }

        <ng-template [cdkPortalOutlet]="_actionsPortal"></ng-template>
      </div>
    }
  }

  <!-- Invisible close button for screen reader users. -->
  <button
    type="button"
    class="mat-timepicker-close-button"
    mat-raised-button
    [color]="color || 'primary'"
    [class.cdk-visually-hidden]="!_closeButtonFocused"
    (focus)="_closeButtonFocused = true"
    (blur)="_closeButtonFocused = false"
    (click)="timepicker.close()"
  >
    {{ _closeButtonText }}
  </button>
</div>
`,
      styles: [".mat-timepicker-content{display:block}.mat-timepicker-content-container{position:relative;display:flex;flex-direction:column;padding:1rem 1.5rem}.mat-timepicker-content-actions{display:flex;justify-content:space-between;margin-right:-1rem;margin-top:1.5rem}.mat-time-toggle-mode-button{width:3.25rem;height:3.25rem;margin-left:-.75rem;margin-bottom:-.25rem}button.mat-timepicker-close-button{position:absolute;top:100%;left:0;margin-top:.5rem}.ng-animating button.mat-timepicker-close-button{display:none}\n"]
    }]
  }], () => [{
    type: MatTimepickerIntl
  }, {
    type: MatTimeSelectionModel
  }, {
    type: ChangeDetectorRef
  }], {
    _dials: [{
      type: ViewChild,
      args: [MatClockDials]
    }],
    _inputs: [{
      type: ViewChild,
      args: [MatTimeInputs]
    }]
  });
})();
var MAT_DEFAULT_ACITONS = new InjectionToken("MAT_DEFAULT_ACITONS");
var MAT_TIMEPICKER_DEFAULT_OPTIONS = new InjectionToken("MAT_TIMEPICKER_DEFAULT_OPTIONS");
var DEFAULT_OPEN_AS = "popup";
var DEFAULT_MODE = "dial";
var DEFAULT_FORMAT = "12h";
var timepickerUid = 0;
var MatTimepickerBase = class _MatTimepickerBase {
  /** Whether the timepicker pop-up should be disabled. */
  get disabled() {
    return this._disabled === void 0 && this.timepickerInput ? this.timepickerInput.disabled : !!this._disabled;
  }
  set disabled(value) {
    const newValue = coerceBooleanProperty(value);
    if (newValue !== this._disabled) {
      this._disabled = newValue;
      this.stateChanges.next(void 0);
    }
  }
  /** Whether the timepicker is open. */
  get opened() {
    return this._opened;
  }
  set opened(value) {
    coerceBooleanProperty(value) ? this.open() : this.close();
  }
  /** Whether the timepicker mode which determines what the timepicker will be opened as. */
  get openAs() {
    return this._openAs || this._defaults?.openAs || DEFAULT_OPEN_AS;
  }
  set openAs(value) {
    this._openAs = value;
  }
  /** Color palette to use on the timepicker's content. */
  get color() {
    return this._color || this._defaults?.color || (this.timepickerInput ? this.timepickerInput.getThemePalette() : void 0);
  }
  set color(value) {
    this._color = value;
  }
  /** Timepicker display mode. */
  get mode() {
    return this._mode || this._defaults?.mode || DEFAULT_MODE;
  }
  set mode(value) {
    this._mode = value;
  }
  /** Timepicker period format. */
  get format() {
    return this._format || this._defaults?.format || DEFAULT_FORMAT;
  }
  set format(value) {
    this._format = value;
  }
  /** Show or hide toggle button between dial and input. */
  get showToggleModeButton() {
    return this._showToggleModeButton;
  }
  set showToggleModeButton(value) {
    this._showToggleModeButton = value;
  }
  /** Step for minutes. */
  get minuteInterval() {
    return this._minuteInterval || this._defaults?.minuteInterval || 1;
  }
  set minuteInterval(value) {
    this._minuteInterval = coerceNumberProperty(value);
  }
  /** Orientation for dial mode. */
  get orientation() {
    return this._orientation || this._defaults?.orientation || "vertical";
  }
  set orientation(value) {
    this._orientation = value;
  }
  /**
   * Whether the timepicker UI is in touch mode. In touch mode elements are larger for bigger touch targets.
   */
  get touchUi() {
    return this._touchUi;
  }
  set touchUi(value) {
    this._touchUi = coerceBooleanProperty(value);
    if (value) {
      this.openAs = "dialog";
    }
  }
  /** The minimum selectable time. */
  _getMinTime() {
    return this.timepickerInput && this.timepickerInput.min;
  }
  /** The maximum selectable time. */
  _getMaxTime() {
    return this.timepickerInput && this.timepickerInput.max;
  }
  constructor(_viewContainerRef, _overlay, _ngZone, scrollStrategy, _defaultActionsComponent, _model, _defaults) {
    this._viewContainerRef = _viewContainerRef;
    this._overlay = _overlay;
    this._ngZone = _ngZone;
    this._defaultActionsComponent = _defaultActionsComponent;
    this._model = _model;
    this._defaults = _defaults;
    this._opened = false;
    this._showToggleModeButton = true;
    this._touchUi = false;
    this.xPosition = "start";
    this.yPosition = "below";
    this.restoreFocus = true;
    this.openedStream = new EventEmitter();
    this.closedStream = new EventEmitter();
    this.id = `mat-timepicker-${timepickerUid++}`;
    this._actionsPortal = null;
    this.stateChanges = new Subject();
    this._backdropHarnessClass = `${this.id}-backdrop`;
    this._focusedElementBeforeOpen = null;
    this._document = inject(DOCUMENT);
    this._scrollStrategy = scrollStrategy;
    if (_defaults) {
      this.showToggleModeButton = _defaults.showToggleModeButton !== void 0 ? _defaults.showToggleModeButton : true;
    }
  }
  ngOnChanges(changes) {
    const positionChange = changes["xPosition"] || changes["yPosition"];
    if (positionChange && !positionChange.firstChange && this._overlayRef) {
      const positionStrategy = this._overlayRef.getConfig().positionStrategy;
      if (positionStrategy instanceof FlexibleConnectedPositionStrategy) {
        this._setConnectedPositions(positionStrategy);
        if (this.opened) {
          this._overlayRef.updatePosition();
        }
      }
    }
    this.stateChanges.next(void 0);
  }
  ngOnDestroy() {
    this._destroyOverlay();
    this.close();
    this.stateChanges.complete();
  }
  /** Opens the timepicker. */
  open() {
    if (this._opened || this.disabled || this._componentRef?.instance._isAnimating) {
      return;
    }
    if (!this.timepickerInput) {
      throw Error("Attempted to open an MatTimepicker with no associated input.");
    }
    this._focusedElementBeforeOpen = _getFocusedElementPierceShadowDom();
    this._openOverlay();
    this._opened = true;
    this.openedStream.emit();
  }
  /** Closes the timepicker. */
  close() {
    if (!this._opened || this._componentRef?.instance._isAnimating) {
      return;
    }
    const canRestoreFocus = this.restoreFocus && this._focusedElementBeforeOpen && typeof this._focusedElementBeforeOpen.focus === "function";
    const completeClose = () => {
      if (this._opened) {
        this._opened = false;
        this.closedStream.emit();
      }
    };
    if (this._componentRef) {
      const {
        instance,
        location
      } = this._componentRef;
      instance._startExitAnimation();
      instance._animationDone.pipe(take(1)).subscribe(() => {
        const activeElement = this._document.activeElement;
        if (canRestoreFocus && (!activeElement || activeElement === this._document.activeElement || location.nativeElement.contains(activeElement))) {
          this._focusedElementBeforeOpen.focus();
        }
        this._focusedElementBeforeOpen = null;
        this._destroyOverlay();
      });
      if (canRestoreFocus) {
        setTimeout(completeClose);
      } else {
        completeClose();
      }
    }
  }
  /**
   * Register an input with this timepicker.
   * @param input The timepicker input to register with this timepicker.
   * @returns Selection model that the input should hook itself up to.
   */
  registerInput(input) {
    if (this.timepickerInput) {
      throw Error("A MatTimepicker can only be associated with a single input.");
    }
    this.timepickerInput = input;
    return this._model;
  }
  /**
   * Registers a portal containing action buttons with the timepicker.
   * @param portal Portal to be registered.
   */
  registerActions(portal) {
    if (this._actionsPortal) {
      throw Error("A MatTimepicker can only be associated with a single actions row.");
    }
    this._actionsPortal = portal;
    this._componentRef?.instance._assignActions(portal, true);
  }
  /**
   * Removes a portal containing action buttons from the timepicker.
   * @param portal Portal to be removed.
   */
  removeActions(portal) {
    if (portal === this._actionsPortal) {
      this._actionsPortal = null;
      this._componentRef?.instance._assignActions(null, true);
    }
  }
  /** Applies the current pending selection on the overlay to the model. */
  _applyPendingSelection() {
    this._componentRef?.instance?._applyPendingSelection();
  }
  /** Forwards relevant values from the timepicker to the timepicker content inside the overlay. */
  _forwardContentValues(instance) {
    const defaultPortal = new ComponentPortal(this._defaultActionsComponent);
    instance.timepicker = this;
    instance.color = this.color;
    instance.mode = this.mode;
    instance.isMeridiem = this.format === "12h";
    instance.showToggleModeButton = this.showToggleModeButton;
    instance.minuteInterval = this.minuteInterval;
    instance.orientation = this.orientation;
    instance._dialogLabelId = this.timepickerInput.getOverlayLabelId();
    instance._assignActions(this._actionsPortal || defaultPortal, false);
  }
  /** Opens the overlay with the timepicker. */
  _openOverlay() {
    this._destroyOverlay();
    const isDialog = this.openAs === "dialog";
    const portal = new ComponentPortal(MatTimepickerContent, this._viewContainerRef);
    const overlayRef = this._overlayRef = this._overlay.create(new OverlayConfig({
      positionStrategy: isDialog ? this._getDialogStrategy() : this._getDropdownStrategy(),
      hasBackdrop: true,
      backdropClass: [isDialog ? "cdk-overlay-dark-backdrop" : "mat-overlay-transparent-backdrop", this._backdropHarnessClass],
      direction: "ltr",
      scrollStrategy: isDialog ? this._overlay.scrollStrategies.block() : this._scrollStrategy(),
      panelClass: `mat-timepicker-${this.openAs}`
    }));
    this._getCloseStream(overlayRef).subscribe((event) => {
      if (event) {
        event.preventDefault();
      }
      this.close();
    });
    overlayRef.keydownEvents().subscribe((event) => {
      const keyCode = event.keyCode;
      if (keyCode === UP_ARROW || keyCode === DOWN_ARROW || keyCode === PAGE_UP || keyCode === PAGE_DOWN) {
        event.preventDefault();
      }
    });
    this._componentRef = overlayRef.attach(portal);
    this._forwardContentValues(this._componentRef.instance);
    if (!isDialog) {
      this._ngZone.onStable.pipe(first()).subscribe(() => overlayRef.updatePosition());
    }
  }
  /** Destroys the current overlay. */
  _destroyOverlay() {
    if (this._overlayRef) {
      this._overlayRef.dispose();
      this._overlayRef = this._componentRef = null;
    }
  }
  /** Gets a position strategy that will open the timepicker as a dropdown. */
  _getDialogStrategy() {
    return this._overlay.position().global().centerHorizontally().centerVertically();
  }
  /** Gets a position strategy that will open the timepicker as a dropdown. */
  _getDropdownStrategy() {
    const strategy = this._overlay.position().flexibleConnectedTo(this.timepickerInput.getConnectedOverlayOrigin()).withTransformOriginOn(".mat-timepicker-content").withFlexibleDimensions(false).withViewportMargin(8).withLockedPosition();
    return this._setConnectedPositions(strategy);
  }
  /** Sets the positions of the timepicker in dropdown mode based on the current configuration. */
  _setConnectedPositions(strategy) {
    const primaryX = this.xPosition === "end" ? "end" : "start";
    const secondaryX = primaryX === "start" ? "end" : "start";
    const primaryY = this.yPosition === "above" ? "bottom" : "top";
    const secondaryY = primaryY === "top" ? "bottom" : "top";
    return strategy.withPositions([{
      originX: primaryX,
      originY: secondaryY,
      overlayX: primaryX,
      overlayY: primaryY
    }, {
      originX: primaryX,
      originY: primaryY,
      overlayX: primaryX,
      overlayY: secondaryY
    }, {
      originX: secondaryX,
      originY: secondaryY,
      overlayX: secondaryX,
      overlayY: primaryY
    }, {
      originX: secondaryX,
      originY: primaryY,
      overlayX: secondaryX,
      overlayY: secondaryY
    }]);
  }
  /** Gets an observable that will emit when the overlay is supposed to be closed. */
  _getCloseStream(overlayRef) {
    return merge(overlayRef.backdropClick(), overlayRef.detachments(), overlayRef.keydownEvents().pipe(filter((event) => {
      return event.keyCode === ESCAPE && !hasModifierKey(event) || this.timepickerInput && hasModifierKey(event, "altKey") && event.keyCode === UP_ARROW;
    })));
  }
  static {
    this.ɵfac = function MatTimepickerBase_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerBase)(ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Overlay), ɵɵdirectiveInject(NgZone), ɵɵdirectiveInject(MAT_TIMEPICKER_SCROLL_STRATEGY), ɵɵdirectiveInject(MAT_DEFAULT_ACITONS), ɵɵdirectiveInject(MatTimeSelectionModel), ɵɵdirectiveInject(MAT_TIMEPICKER_DEFAULT_OPTIONS, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerBase,
      inputs: {
        disabled: "disabled",
        opened: "opened",
        openAs: "openAs",
        color: "color",
        mode: "mode",
        format: "format",
        showToggleModeButton: "showToggleModeButton",
        minuteInterval: "minuteInterval",
        orientation: "orientation",
        touchUi: [2, "touchUi", "touchUi", booleanAttribute],
        xPosition: "xPosition",
        yPosition: "yPosition",
        restoreFocus: [2, "restoreFocus", "restoreFocus", booleanAttribute]
      },
      outputs: {
        openedStream: "opened",
        closedStream: "closed"
      },
      standalone: true,
      features: [ɵɵInputTransformsFeature, ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerBase, [{
    type: Directive
  }], () => [{
    type: ViewContainerRef
  }, {
    type: Overlay
  }, {
    type: NgZone
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [MAT_TIMEPICKER_SCROLL_STRATEGY]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [MAT_DEFAULT_ACITONS]
    }]
  }, {
    type: MatTimeSelectionModel
  }, {
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [MAT_TIMEPICKER_DEFAULT_OPTIONS]
    }]
  }], {
    disabled: [{
      type: Input
    }],
    opened: [{
      type: Input
    }],
    openAs: [{
      type: Input
    }],
    color: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    format: [{
      type: Input
    }],
    showToggleModeButton: [{
      type: Input
    }],
    minuteInterval: [{
      type: Input
    }],
    orientation: [{
      type: Input
    }],
    touchUi: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    xPosition: [{
      type: Input
    }],
    yPosition: [{
      type: Input
    }],
    restoreFocus: [{
      type: Input,
      args: [{
        transform: booleanAttribute
      }]
    }],
    openedStream: [{
      type: Output,
      args: ["opened"]
    }],
    closedStream: [{
      type: Output,
      args: ["closed"]
    }]
  });
})();
var MatTimepicker = class _MatTimepicker extends MatTimepickerBase {
  static {
    this.ɵfac = /* @__PURE__ */ (() => {
      let ɵMatTimepicker_BaseFactory;
      return function MatTimepicker_Factory(__ngFactoryType__) {
        return (ɵMatTimepicker_BaseFactory || (ɵMatTimepicker_BaseFactory = ɵɵgetInheritedFactory(_MatTimepicker)))(__ngFactoryType__ || _MatTimepicker);
      };
    })();
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepicker,
      selectors: [["mat-timepicker"]],
      hostAttrs: [1, "mat-timepicker"],
      hostVars: 6,
      hostBindings: function MatTimepicker_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("mat-primary", ctx.color !== "accent" && ctx.color !== "warn")("mat-accent", ctx.color === "accent")("mat-warn", ctx.color === "warn");
        }
      },
      exportAs: ["matTimepicker"],
      standalone: true,
      features: [ɵɵProvidersFeature([MAT_SINGLE_TIME_SELECTION_MODEL_PROVIDER, {
        provide: MatTimepickerBase,
        useExisting: _MatTimepicker
      }]), ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 0,
      vars: 0,
      template: function MatTimepicker_Template(rf, ctx) {
      },
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepicker, [{
    type: Component,
    args: [{
      selector: "mat-timepicker",
      standalone: true,
      template: "",
      exportAs: "matTimepicker",
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-timepicker",
        "[class.mat-primary]": 'color !== "accent" && color !== "warn"',
        "[class.mat-accent]": 'color === "accent"',
        "[class.mat-warn]": 'color === "warn"'
      },
      providers: [MAT_SINGLE_TIME_SELECTION_MODEL_PROVIDER, {
        provide: MatTimepickerBase,
        useExisting: MatTimepicker
      }]
    }]
  }], null, null);
})();
var MatTimepickerInputEvent = class {
  constructor(target, targetElement) {
    this.target = target;
    this.targetElement = targetElement;
    this.value = this.target.value;
  }
};
var TIME_FORMATS = {
  hour: "2-digit",
  minute: "2-digit"
};
var MatTimepickerInputBase = class _MatTimepickerInputBase {
  /** The value of the input. */
  get value() {
    return this._model ? this._getValueFromModel(this._model.selection) : this._pendingValue;
  }
  set value(value) {
    this._assignValueProgrammatically(value);
  }
  /** Whether the timepicker-input is disabled. */
  get disabled() {
    return !!this._disabled;
  }
  set disabled(value) {
    const newValue = coerceBooleanProperty(value);
    const element = this._elementRef.nativeElement;
    if (this._disabled !== newValue) {
      this._disabled = newValue;
      this.stateChanges.next(void 0);
    }
    if (newValue && this._isInitialized && element.blur) {
      element.blur();
    }
  }
  constructor(_elementRef, _timeAdapter) {
    this._elementRef = _elementRef;
    this._timeAdapter = _timeAdapter;
    this.timeChange = new EventEmitter();
    this.timeInput = new EventEmitter();
    this.stateChanges = new Subject();
    this._onTouched = () => {
    };
    this._validatorOnChange = () => {
    };
    this._cvaOnChange = () => {
    };
    this._valueChangesSubscription = Subscription.EMPTY;
    this._lastValueValid = false;
    this._parseValidator = () => {
      return this._lastValueValid ? null : {
        matTimepickerParse: {
          text: this._elementRef.nativeElement.value
        }
      };
    };
    this._minValidator = (control) => {
      const controlValue = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(control.value));
      const min = this._getMinTime();
      return !min || !controlValue || this._timeAdapter.compareTime(min, controlValue) <= 0 ? null : {
        matTimepickerMin: {
          min,
          actual: controlValue
        }
      };
    };
    this._maxValidator = (control) => {
      const controlValue = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(control.value));
      const max = this._getMaxTime();
      return !max || !controlValue || this._timeAdapter.compareTime(max, controlValue) >= 0 ? null : {
        matTimepickerMax: {
          max,
          actual: controlValue
        }
      };
    };
  }
  ngOnChanges(changes) {
    if (timeInputsHaveChanged(changes, this._timeAdapter)) {
      this.stateChanges.next(void 0);
    }
  }
  ngOnDestroy() {
    this._valueChangesSubscription.unsubscribe();
    this.stateChanges.complete();
  }
  /** Registers a time selection model with the input. */
  _registerModel(model) {
    this._model = model;
    this._valueChangesSubscription.unsubscribe();
    if (this._pendingValue) {
      this._assignValue(this._pendingValue);
    }
    this._valueChangesSubscription = this._model.selectionChanged.subscribe((event) => {
      if (this._shouldHandleChangeEvent(event)) {
        const value = this._getValueFromModel(event.selection);
        this._lastValueValid = this._isValidValue(value);
        this._cvaOnChange(value);
        this._onTouched();
        this._formatValue(value);
        this.timeInput.emit(new MatTimepickerInputEvent(this, this._elementRef.nativeElement));
        this.timeChange.emit(new MatTimepickerInputEvent(this, this._elementRef.nativeElement));
      }
    });
  }
  _onInput(value) {
    const lastValueWasValid = this._lastValueValid;
    let time = this._timeAdapter.parse(value, TIME_FORMATS);
    this._lastValueValid = this._isValidValue(time);
    time = this._timeAdapter.getValidTimeOrNull(time);
    const hasChanged = !this._timeAdapter.sameTime(time, this.value);
    if (!time || hasChanged) {
      this._cvaOnChange(time);
    } else {
      if (value && !this.value) {
        this._cvaOnChange(time);
      }
      if (lastValueWasValid !== this._lastValueValid) {
        this._validatorOnChange();
      }
    }
    if (hasChanged) {
      this._assignValue(time);
      this.timeInput.emit(new MatTimepickerInputEvent(this, this._elementRef.nativeElement));
    }
  }
  /** Handles change event on the input. */
  _onChange() {
    this.timeChange.emit(new MatTimepickerInputEvent(this, this._elementRef.nativeElement));
  }
  /** Handles blur event on the input. */
  _onBlur() {
    if (this.value) {
      this._formatValue(this.value);
    }
    this._onTouched();
  }
  /** Implemented as part of ControlValueAccessor.  */
  writeValue(value) {
    this.value = value;
  }
  /** Implemented as part of ControlValueAccessor.  */
  registerOnChange(fn) {
    this._cvaOnChange = fn;
  }
  /** Implemented as part of ControlValueAccessor.  */
  registerOnTouched(fn) {
    this._onTouched = fn;
  }
  /** Implemented as part of ControlValueAccessor.  */
  setDisabledState(isDisabled) {
    this.disabled = isDisabled;
  }
  registerOnValidatorChange(fn) {
    this._validatorOnChange = fn;
  }
  validate(c) {
    return this._validator ? this._validator(c) : null;
  }
  /** Programmatically assigns a value to the input. */
  _assignValueProgrammatically(value) {
    value = this._timeAdapter.deserialize(value);
    this._lastValueValid = this._isValidValue(value);
    value = this._timeAdapter.getValidTimeOrNull(value);
    this._assignValue(value);
    this._formatValue(value);
  }
  /** Formats a value and sets it on the input element. */
  _formatValue(value) {
    this._elementRef.nativeElement.value = value != null ? this._timeAdapter.format(value, TIME_FORMATS) : "";
  }
  /** Gets the base validator functions. */
  _getValidators() {
    return [this._parseValidator, this._minValidator, this._maxValidator];
  }
  /** Whether a value is considered valid. */
  _isValidValue(value) {
    return !value || this._timeAdapter.isValid(value);
  }
  /** Assigns a value to the model. */
  _assignValue(value) {
    if (this._model) {
      this._assignValueToModel(value);
      this._pendingValue = null;
    } else {
      this._pendingValue = value;
    }
  }
  static {
    this.ɵfac = function MatTimepickerInputBase_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerInputBase)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(TimeAdapter, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerInputBase,
      inputs: {
        value: "value",
        disabled: "disabled"
      },
      outputs: {
        timeChange: "timeChange",
        timeInput: "timeInput"
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerInputBase, [{
    type: Directive
  }], () => [{
    type: ElementRef
  }, {
    type: TimeAdapter,
    decorators: [{
      type: Optional
    }]
  }], {
    value: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    timeChange: [{
      type: Output
    }],
    timeInput: [{
      type: Output
    }]
  });
})();
function timeInputsHaveChanged(changes, adapter) {
  const keys = Object.keys(changes);
  for (const key of keys) {
    const {
      previousValue,
      currentValue
    } = changes[key];
    if (adapter.isTimeInstance(previousValue) && adapter.isTimeInstance(currentValue)) {
      if (!adapter.sameTime(previousValue, currentValue)) {
        return true;
      }
    } else {
      return true;
    }
  }
  return false;
}
var MAT_TIMEPICKER_VALUE_ACCESSOR = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => MatTimepickerInput),
  multi: true
};
var MAT_TIMEPICKER_VALIDATORS = {
  provide: NG_VALIDATORS,
  useExisting: forwardRef(() => MatTimepickerInput),
  multi: true
};
var MatTimepickerInput = class _MatTimepickerInput extends MatTimepickerInputBase {
  /** The timepicker that this input is associated with. */
  set matTimepicker(timepicker) {
    if (timepicker) {
      this._timepicker = timepicker;
      this._registerModel(timepicker.registerInput(this));
    }
  }
  /** The minimum valid date. */
  get min() {
    return this._min;
  }
  set min(value) {
    const validValue = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(value));
    if (!this._timeAdapter.sameTime(validValue, this._min)) {
      this._min = validValue;
      this._validatorOnChange();
    }
  }
  /** The maximum valid date. */
  get max() {
    return this._max;
  }
  set max(value) {
    const validValue = this._timeAdapter.getValidTimeOrNull(this._timeAdapter.deserialize(value));
    if (!this._timeAdapter.sameTime(validValue, this._max)) {
      this._max = validValue;
      this._validatorOnChange();
    }
  }
  constructor(elementRef, timeAdapter, _formField) {
    super(elementRef, timeAdapter);
    this._formField = _formField;
    this._validator = Validators.compose(super._getValidators());
  }
  /**
   * Gets the element that the timepicker popup should be connected to.
   * @return The element to connect the popup to.
   */
  getConnectedOverlayOrigin() {
    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._elementRef;
  }
  /** Returns the palette used by the input's form field, if any. */
  getThemePalette() {
    return this._formField ? this._formField.color : void 0;
  }
  /** Gets the ID of an element that should be used a description for the timepicker overlay. */
  getOverlayLabelId() {
    if (this._formField) {
      return this._formField.getLabelId();
    }
    return this._elementRef.nativeElement.getAttribute("aria-labelledby");
  }
  /** Gets the input's minimum time. */
  _getMinTime() {
    return this._min;
  }
  /** Gets the input's maximum time. */
  _getMaxTime() {
    return this._max;
  }
  _assignValueToModel(value) {
    if (this._model) {
      this._model.updateSelection(value, this);
    }
  }
  _getValueFromModel(modelValue) {
    return modelValue;
  }
  _shouldHandleChangeEvent(event) {
    return event.source !== this;
  }
  static {
    this.ɵfac = function MatTimepickerInput_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerInput)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(TimeAdapter, 8), ɵɵdirectiveInject(MAT_FORM_FIELD, 8));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerInput,
      selectors: [["input", "matTimepicker", ""]],
      hostAttrs: [1, "mat-timepicker-input"],
      hostVars: 5,
      hostBindings: function MatTimepickerInput_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("input", function MatTimepickerInput_input_HostBindingHandler($event) {
            return ctx._onInput($event.target.value);
          })("change", function MatTimepickerInput_change_HostBindingHandler() {
            return ctx._onChange();
          })("blur", function MatTimepickerInput_blur_HostBindingHandler() {
            return ctx._onBlur();
          });
        }
        if (rf & 2) {
          ɵɵhostProperty("disabled", ctx.disabled);
          ɵɵattribute("aria-haspopup", ctx._timepicker ? "dialog" : null)("aria-owns", (ctx._timepicker == null ? null : ctx._timepicker.opened) && ctx._timepicker.id || null)("min", ctx.min || null)("max", ctx.max || null);
        }
      },
      inputs: {
        matTimepicker: "matTimepicker",
        min: "min",
        max: "max"
      },
      exportAs: ["matTimepickerInput"],
      standalone: true,
      features: [ɵɵProvidersFeature([MAT_TIMEPICKER_VALUE_ACCESSOR, MAT_TIMEPICKER_VALIDATORS, {
        provide: MAT_INPUT_VALUE_ACCESSOR,
        useExisting: _MatTimepickerInput
      }]), ɵɵInheritDefinitionFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerInput, [{
    type: Directive,
    args: [{
      selector: "input[matTimepicker]",
      standalone: true,
      exportAs: "matTimepickerInput",
      providers: [MAT_TIMEPICKER_VALUE_ACCESSOR, MAT_TIMEPICKER_VALIDATORS, {
        provide: MAT_INPUT_VALUE_ACCESSOR,
        useExisting: MatTimepickerInput
      }],
      host: {
        class: "mat-timepicker-input",
        "[attr.aria-haspopup]": '_timepicker ? "dialog" : null',
        "[attr.aria-owns]": "(_timepicker?.opened && _timepicker.id) || null",
        "[attr.min]": "min || null",
        "[attr.max]": "max || null",
        "[disabled]": "disabled",
        "(input)": "_onInput($event.target.value)",
        "(change)": "_onChange()",
        "(blur)": "_onBlur()"
      }
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: TimeAdapter,
    decorators: [{
      type: Optional
    }]
  }, {
    type: MatFormField,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [MAT_FORM_FIELD]
    }]
  }], {
    matTimepicker: [{
      type: Input
    }],
    min: [{
      type: Input
    }],
    max: [{
      type: Input
    }]
  });
})();
var MatTimepickerApply = class _MatTimepickerApply {
  constructor(_timepicker) {
    this._timepicker = _timepicker;
  }
  _applySelection() {
    this._timepicker._applyPendingSelection();
    this._timepicker.close();
  }
  static {
    this.ɵfac = function MatTimepickerApply_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerApply)(ɵɵdirectiveInject(MatTimepickerBase));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerApply,
      selectors: [["", "matTimepickerApply", ""]],
      hostBindings: function MatTimepickerApply_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function MatTimepickerApply_click_HostBindingHandler() {
            return ctx._applySelection();
          });
        }
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerApply, [{
    type: Directive,
    args: [{
      selector: "[matTimepickerApply]",
      standalone: true,
      host: {
        "(click)": "_applySelection()"
      }
    }]
  }], () => [{
    type: MatTimepickerBase
  }], null);
})();
var MatTimepickerCancel = class _MatTimepickerCancel {
  constructor(_timepicker) {
    this._timepicker = _timepicker;
  }
  close() {
    this._timepicker.close();
  }
  static {
    this.ɵfac = function MatTimepickerCancel_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerCancel)(ɵɵdirectiveInject(MatTimepickerBase));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatTimepickerCancel,
      selectors: [["", "matTimepickerCancel", ""]],
      hostBindings: function MatTimepickerCancel_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function MatTimepickerCancel_click_HostBindingHandler() {
            return ctx.close();
          });
        }
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerCancel, [{
    type: Directive,
    args: [{
      selector: "[matTimepickerCancel]",
      standalone: true,
      host: {
        "(click)": "close()"
      }
    }]
  }], () => [{
    type: MatTimepickerBase
  }], null);
})();
var MatTimepickerActions = class _MatTimepickerActions {
  constructor(_timepicker, _viewContainerRef) {
    this._timepicker = _timepicker;
    this._viewContainerRef = _viewContainerRef;
  }
  ngAfterViewInit() {
    this._portal = new TemplatePortal(this._template, this._viewContainerRef);
    this._timepicker.registerActions(this._portal);
  }
  ngOnDestroy() {
    this._timepicker.removeActions(this._portal);
    if (this._portal && this._portal.isAttached) {
      this._portal?.detach();
    }
  }
  static {
    this.ɵfac = function MatTimepickerActions_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerActions)(ɵɵdirectiveInject(MatTimepickerBase), ɵɵdirectiveInject(ViewContainerRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepickerActions,
      selectors: [["mat-timepicker-actions"]],
      viewQuery: function MatTimepickerActions_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(TemplateRef, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx._template = _t.first);
        }
      },
      hostAttrs: [1, "mat-timepicker-actions-container"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c5,
      decls: 1,
      vars: 0,
      consts: [[1, "mat-timepicker-actions"]],
      template: function MatTimepickerActions_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵtemplate(0, MatTimepickerActions_ng_template_0_Template, 2, 0, "ng-template");
        }
      },
      styles: [".mat-timepicker-actions-container{margin-left:auto}.mat-timepicker-actions{display:flex;gap:.5rem;align-items:center;margin-top:.5rem}\n"],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerActions, [{
    type: Component,
    args: [{
      selector: "mat-timepicker-actions",
      standalone: true,
      template: `
    <ng-template>
      <div class="mat-timepicker-actions">
        <ng-content></ng-content>
      </div>
    </ng-template>
  `,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-timepicker-actions-container"
      },
      styles: [".mat-timepicker-actions-container{margin-left:auto}.mat-timepicker-actions{display:flex;gap:.5rem;align-items:center;margin-top:.5rem}\n"]
    }]
  }], () => [{
    type: MatTimepickerBase
  }, {
    type: ViewContainerRef
  }], {
    _template: [{
      type: ViewChild,
      args: [TemplateRef]
    }]
  });
})();
var MatTimepickerDefaultActions = class _MatTimepickerDefaultActions {
  constructor(_timepicker, _intl) {
    this._timepicker = _timepicker;
    this._intl = _intl;
    this.color = signal(void 0);
  }
  ngOnInit() {
    this.color.set(this._timepicker.color);
  }
  static {
    this.ɵfac = function MatTimepickerDefaultActions_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerDefaultActions)(ɵɵdirectiveInject(MatTimepickerBase), ɵɵdirectiveInject(MatTimepickerIntl));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _MatTimepickerDefaultActions,
      selectors: [["mat-timepicker-default-actions"]],
      hostAttrs: [1, "mat-timepicker-actions-container"],
      standalone: true,
      features: [ɵɵStandaloneFeature],
      ngContentSelectors: _c5,
      decls: 6,
      vars: 4,
      consts: [[1, "mat-timepicker-actions"], ["mat-button", "", "matTimepickerCancel", "", 3, "color"], ["mat-button", "", "matTimepickerApply", "", 3, "color"]],
      template: function MatTimepickerDefaultActions_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵelementStart(0, "div", 0);
          ɵɵprojection(1);
          ɵɵelementStart(2, "button", 1);
          ɵɵtext(3);
          ɵɵelementEnd();
          ɵɵelementStart(4, "button", 2);
          ɵɵtext(5);
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          ɵɵadvance(2);
          ɵɵproperty("color", ctx.color());
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._intl.cancelButton, " ");
          ɵɵadvance();
          ɵɵproperty("color", ctx.color());
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx._intl.okButton, " ");
        }
      },
      dependencies: [MatButtonModule, MatButton, MatTimepickerApply, MatTimepickerCancel],
      styles: [_c6],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerDefaultActions, [{
    type: Component,
    args: [{
      selector: "mat-timepicker-default-actions",
      standalone: true,
      imports: [MatButtonModule, MatTimepickerApply, MatTimepickerCancel],
      template: `
    <div class="mat-timepicker-actions">
      <ng-content></ng-content>
      <button [color]="color()" mat-button matTimepickerCancel>
        {{ _intl.cancelButton }}
      </button>
      <button [color]="color()" mat-button matTimepickerApply>
        {{ _intl.okButton }}
      </button>
    </div>
  `,
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation$1.None,
      host: {
        class: "mat-timepicker-actions-container"
      },
      styles: [".mat-timepicker-actions-container{margin-left:auto}.mat-timepicker-actions{display:flex;gap:.5rem;align-items:center;margin-top:.5rem}\n"]
    }]
  }], () => [{
    type: MatTimepickerBase
  }, {
    type: MatTimepickerIntl
  }], null);
})();
var MatTimepickerModule = class _MatTimepickerModule {
  static {
    this.ɵfac = function MatTimepickerModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatTimepickerModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _MatTimepickerModule,
      imports: [OverlayModule, PortalModule, A11yModule, MatTimepicker, MatTimepickerToggle, MatTimepickerToggleIcon, MatTimepickerContent, MatTimepickerContentLayout, MatTimepickerInput, MatTimeInputs, MatHourInput, MatMinuteInput, MatClockDials, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod, MatTimepickerActions, MatTimepickerDefaultActions, MatTimepickerApply, MatTimepickerCancel],
      exports: [CdkScrollableModule, MatTimepicker, MatTimepickerToggle, MatTimepickerToggleIcon, MatTimepickerContent, MatTimepickerContentLayout, MatTimepickerInput, MatTimeInputs, MatHourInput, MatMinuteInput, MatClockDials, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod, MatTimepickerActions, MatTimepickerDefaultActions, MatTimepickerApply, MatTimepickerCancel]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [MatTimepickerIntl, MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER, {
        provide: MAT_DEFAULT_ACITONS,
        useValue: MatTimepickerDefaultActions
      }, {
        provide: MAT_FAB_DEFAULT_OPTIONS,
        useValue: {
          color: "unthemed"
        }
      }],
      imports: [OverlayModule, PortalModule, A11yModule, MatTimepickerToggle, MatTimepickerContent, MatTimeInputs, MatClockDials, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod, MatTimepickerDefaultActions, CdkScrollableModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatTimepickerModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OverlayModule, PortalModule, A11yModule, MatTimepicker, MatTimepickerToggle, MatTimepickerToggleIcon, MatTimepickerContent, MatTimepickerContentLayout, MatTimepickerInput, MatTimeInputs, MatHourInput, MatMinuteInput, MatClockDials, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod, MatTimepickerActions, MatTimepickerDefaultActions, MatTimepickerApply, MatTimepickerCancel],
      exports: [CdkScrollableModule, MatTimepicker, MatTimepickerToggle, MatTimepickerToggleIcon, MatTimepickerContent, MatTimepickerContentLayout, MatTimepickerInput, MatTimeInputs, MatHourInput, MatMinuteInput, MatClockDials, MatHoursClockDial, MatMinutesClockDial, MatTimePeriod, MatTimepickerActions, MatTimepickerDefaultActions, MatTimepickerApply, MatTimepickerCancel],
      providers: [MatTimepickerIntl, MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER, {
        provide: MAT_DEFAULT_ACITONS,
        useValue: MatTimepickerDefaultActions
      }, {
        provide: MAT_FAB_DEFAULT_OPTIONS,
        useValue: {
          color: "unthemed"
        }
      }]
    }]
  }], null, null);
})();
export {
  ALL_HOURS,
  ALL_MINUTES,
  MAT_DATE_TIME_LOCALE_FACTORY,
  MAT_DEFAULT_ACITONS,
  MAT_TIMEPICKER_DEFAULT_OPTIONS,
  MAT_TIMEPICKER_SCROLL_STRATEGY,
  MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY,
  MAT_TIMEPICKER_SCROLL_STRATEGY_FACTORY_PROVIDER,
  MAT_TIMEPICKER_VALIDATORS,
  MAT_TIMEPICKER_VALUE_ACCESSOR,
  MAT_TIME_LOCALE,
  MAT_TIME_LOCALE_PROVIDER,
  MatClockDials,
  MatHourInput,
  MatHoursClockDial,
  MatMinuteInput,
  MatMinutesClockDial,
  MatNativeDateTimeModule,
  MatTimeInputs,
  MatTimePeriod,
  MatTimepicker,
  MatTimepickerActions,
  MatTimepickerApply,
  MatTimepickerBase,
  MatTimepickerCancel,
  MatTimepickerContent,
  MatTimepickerContentLayout,
  MatTimepickerDefaultActions,
  MatTimepickerInput,
  MatTimepickerInputBase,
  MatTimepickerInputEvent,
  MatTimepickerIntl,
  MatTimepickerModule,
  MatTimepickerToggle,
  MatTimepickerToggleIcon,
  NativeDateTimeAdapter,
  NativeDateTimeModule,
  TimeAdapter,
  provideNativeDateTimeAdapter,
  timeInputsHaveChanged
};
//# sourceMappingURL=@dhutaryan_ngx-mat-timepicker.js.map
