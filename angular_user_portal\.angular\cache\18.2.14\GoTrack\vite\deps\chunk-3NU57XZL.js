import {
  Activated<PERSON>out<PERSON>,
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  PRIMARY_OUTLET,
  Router,
  RouterModule,
  RouterOutlet,
  TitleStrategy
} from "./chunk-YKHGT5DJ.js";
import {
  DomSanitizer,
  Title
} from "./chunk-M7LED4FC.js";
import {
  FormGroupDirective,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from "./chunk-JP2LMHJE.js";
import {
  HttpClient,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpHeaders,
  HttpParams,
  provideHttpClient,
  withInterceptorsFromDi,
  withXsrfConfiguration
} from "./chunk-OG4RIIRZ.js";
import {
  toSignal
} from "./chunk-SQ2XSFGA.js";
import {
  CommonModule,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DOCUMENT,
  DatePipe,
  NgComponentOutlet,
  registerLocaleData
} from "./chunk-6D52GKB4.js";
import {
  APP_INITIALIZER,
  ApplicationRef,
  ChangeDetectorRef,
  Compiler,
  Component,
  ComponentFactoryResolver$1,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Injectable,
  InjectionToken,
  Injector,
  Input,
  IterableDiffers,
  LOCALE_ID,
  NgModule,
  NgModuleFactory$1,
  NgZone,
  Optional,
  Output,
  Pipe,
  SecurityContext,
  Self,
  SkipSelf,
  TemplateRef,
  ViewContainerRef,
  computed,
  effect,
  inject,
  isDevMode,
  makeEnvironmentProviders,
  setClassMetadata,
  signal,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵgetInheritedFactory,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresolveWindow,
  ɵɵtemplate
} from "./chunk-QGPYGS5J.js";
import {
  firstValueFrom,
  fromEvent,
  lastValueFrom
} from "./chunk-GJSJXBTC.js";
import {
  BehaviorSubject,
  Observable,
  ReplaySubject,
  Subject,
  Subscription,
  catchError,
  combineLatest,
  concat,
  debounceTime,
  delay,
  distinctUntilChanged,
  filter,
  finalize,
  from,
  map,
  mapTo,
  of,
  pipe,
  retryWhen,
  shareReplay,
  switchMap,
  take,
  takeUntil,
  tap,
  throwError,
  timer
} from "./chunk-DJECZSZD.js";
import {
  __async,
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-ZTELYOIP.js";

// node_modules/just-compare/index.mjs
var collectionCompare = compare;
function compare(value1, value2) {
  if (value1 === value2) {
    return true;
  }
  if (value1 !== value1 && value2 !== value2) {
    return true;
  }
  if (typeof value1 != typeof value2 || // primitive != primitive wrapper
  {}.toString.call(value1) != {}.toString.call(value2)) {
    return false;
  }
  if (value1 !== Object(value1)) {
    return false;
  }
  if (!value1) {
    return false;
  }
  if (Array.isArray(value1)) {
    return compareArrays(value1, value2);
  }
  if ({}.toString.call(value1) == "[object Set]") {
    return compareArrays(Array.from(value1), Array.from(value2));
  }
  if ({}.toString.call(value1) == "[object Object]") {
    return compareObjects(value1, value2);
  }
  return compareNativeSubtypes(value1, value2);
}
function compareNativeSubtypes(value1, value2) {
  return value1.toString() === value2.toString();
}
function compareArrays(value1, value2) {
  var len = value1.length;
  if (len != value2.length) {
    return false;
  }
  for (var i = 0; i < len; i++) {
    if (!compare(value1[i], value2[i])) {
      return false;
    }
  }
  return true;
}
function compareObjects(value1, value2) {
  var keys1 = Object.keys(value1);
  var len = keys1.length;
  if (len != Object.keys(value2).length) {
    return false;
  }
  for (var i = 0; i < len; i++) {
    var key1 = keys1[i];
    if (!(value2.hasOwnProperty(key1) && compare(value1[key1], value2[key1]))) {
      return false;
    }
  }
  return true;
}

// node_modules/just-clone/index.mjs
var collectionClone = clone;
function clone(obj) {
  let result = obj;
  var type = {}.toString.call(obj).slice(8, -1);
  if (type == "Set") {
    return new Set([...obj].map((value) => clone(value)));
  }
  if (type == "Map") {
    return new Map([...obj].map((kv) => [clone(kv[0]), clone(kv[1])]));
  }
  if (type == "Date") {
    return new Date(obj.getTime());
  }
  if (type == "RegExp") {
    return RegExp(obj.source, getRegExpFlags(obj));
  }
  if (type == "Array" || type == "Object") {
    result = Array.isArray(obj) ? [] : {};
    for (var key in obj) {
      result[key] = clone(obj[key]);
    }
  }
  return result;
}
function getRegExpFlags(regExp) {
  if (typeof regExp.source.flags == "string") {
    return regExp.source.flags;
  } else {
    var flags = [];
    regExp.global && flags.push("g");
    regExp.ignoreCase && flags.push("i");
    regExp.multiline && flags.push("m");
    regExp.sticky && flags.push("y");
    regExp.unicode && flags.push("u");
    return flags.join("");
  }
}

// node_modules/@abp/ng.core/fesm2022/abp-ng.core.mjs
function DynamicLayoutComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 0);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("ngComponentOutlet", ctx_r0.layout);
  }
}
function ReplaceableRouteContainerComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
var AbstractNgModelComponent = class _AbstractNgModelComponent {
  constructor() {
    this.cdRef = inject(ChangeDetectorRef);
    this.valueFn = (value) => value;
    this.valueLimitFn = (value) => false;
  }
  set value(value) {
    value = this.valueFn(value, this._value);
    if (this.valueLimitFn(value, this._value) !== false || this.readonly) return;
    this._value = value;
    this.notifyValueChange();
  }
  get value() {
    return this._value || this.defaultValue;
  }
  get defaultValue() {
    return this._value;
  }
  notifyValueChange() {
    if (this.onChange) {
      this.onChange(this.value);
    }
  }
  writeValue(value) {
    this._value = this.valueLimitFn(value, this._value) || value;
    this.cdRef.markForCheck();
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled) {
    this.disabled = isDisabled;
  }
  static {
    this.ɵfac = function AbstractNgModelComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbstractNgModelComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _AbstractNgModelComponent,
      selectors: [["ng-component"]],
      inputs: {
        disabled: "disabled",
        readonly: "readonly",
        valueFn: "valueFn",
        valueLimitFn: "valueLimitFn",
        value: "value"
      },
      decls: 0,
      vars: 0,
      template: function AbstractNgModelComponent_Template(rf, ctx) {
      },
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbstractNgModelComponent, [{
    type: Component,
    args: [{
      template: ""
    }]
  }], null, {
    disabled: [{
      type: Input
    }],
    readonly: [{
      type: Input
    }],
    valueFn: [{
      type: Input
    }],
    valueLimitFn: [{
      type: Input
    }],
    value: [{
      type: Input
    }]
  });
})();
var AuthGuard = class _AuthGuard {
  canActivate() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
    return false;
  }
  static {
    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AuthGuard)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AuthGuard,
      factory: _AuthGuard.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var authGuard = () => {
  console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  return false;
};
var AuthService = class _AuthService {
  warningMessage() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  }
  get oidc() {
    this.warningMessage();
    return false;
  }
  set oidc(value) {
    this.warningMessage();
  }
  init() {
    this.warningMessage();
    return Promise.resolve(void 0);
  }
  login(params) {
    this.warningMessage();
    return of(void 0);
  }
  logout(queryParams) {
    this.warningMessage();
    return of(void 0);
  }
  navigateToLogin(queryParams) {
  }
  get isInternalAuth() {
    throw new Error("not implemented");
  }
  get isAuthenticated() {
    this.warningMessage();
    return false;
  }
  loginUsingGrant(grantType, parameters, headers) {
    console.log({
      grantType,
      parameters,
      headers
    });
    return Promise.reject(new Error("not implemented"));
  }
  getAccessTokenExpiration() {
    this.warningMessage();
    return 0;
  }
  getRefreshToken() {
    this.warningMessage();
    return "";
  }
  getAccessToken() {
    this.warningMessage();
    return "";
  }
  refreshToken() {
    this.warningMessage();
    return Promise.resolve(void 0);
  }
  static {
    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AuthService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AuthService,
      factory: _AuthService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var AbstractAuthErrorFilter = class {
};
var AuthErrorFilterService = class extends AbstractAuthErrorFilter {
  warningMessage() {
    console.error("You should add @abp/ng-oauth packages or create your own auth packages.");
  }
  get(id) {
    this.warningMessage();
    throw new Error("not implemented");
  }
  add(filter2) {
    this.warningMessage();
  }
  patch(item) {
    this.warningMessage();
  }
  remove(id) {
    this.warningMessage();
  }
  run(event) {
    this.warningMessage();
    throw new Error("not implemented");
  }
};
var LOCALIZATIONS = new InjectionToken("LOCALIZATIONS");
function localizationContributor(localizations) {
  if (localizations) {
    localizations$.next([...localizations$.value, ...localizations]);
  }
}
var localizations$ = new BehaviorSubject([]);
var CORE_OPTIONS = new InjectionToken("CORE_OPTIONS");
function coreOptionsFactory(_a) {
  var options = __objRest(_a, []);
  return __spreadValues({}, options);
}
function getLocaleDirection(locale) {
  return /^(ar(-[A-Z]{2})?|ckb(-IR)?|fa(-AF)?|he|ks|lrc(-IQ)?|mzn|pa-Arab|ps(-PK)?|sd|ug|ur(-IN)?|uz-Arab|yi)$/.test(locale) ? "rtl" : "ltr";
}
function createLocalizer(localization) {
  return (resourceName, key, defaultValue) => {
    if (resourceName === "_") return key;
    const resource = localization?.values?.[resourceName];
    if (!resource) return defaultValue;
    return resource[key] || defaultValue;
  };
}
function createLocalizerWithFallback(localization) {
  const findLocalization = createLocalizationFinder(localization);
  return (resourceNames, keys, defaultValue) => {
    const {
      localized
    } = findLocalization(resourceNames, keys);
    return localized || defaultValue;
  };
}
function createLocalizationPipeKeyGenerator(localization) {
  const findLocalization = createLocalizationFinder(localization);
  return (resourceNames, keys, defaultKey) => {
    const {
      resourceName,
      key
    } = findLocalization(resourceNames, keys);
    return !resourceName ? defaultKey : resourceName === "_" ? key : `${resourceName}::${key}`;
  };
}
function createLocalizationFinder(localization) {
  const localize = createLocalizer(localization);
  return (resourceNames, keys) => {
    resourceNames = resourceNames.concat(localization.defaultResourceName || "").filter(Boolean);
    const resourceCount = resourceNames.length;
    const keyCount = keys.length;
    for (let i = 0; i < resourceCount; i++) {
      const resourceName = resourceNames[i];
      for (let j = 0; j < keyCount; j++) {
        const key = keys[j];
        const localized = localize(resourceName, key, null);
        if (localized) return {
          resourceName,
          key,
          localized
        };
      }
    }
    return {
      resourceName: void 0,
      key: void 0,
      localized: void 0
    };
  };
}
function createTokenParser(format) {
  return (str) => {
    const tokens = [];
    const regex = format.replace(/\./g, "\\.").replace(/\{\s?([0-9a-zA-Z]+)\s?\}/g, (_, token) => {
      tokens.push(token);
      return "(.+)";
    });
    const matches = (str.match(regex) || []).slice(1);
    return matches.reduce((acc, v, i) => {
      const key = tokens[i];
      acc[key] = [...acc[key] || [], v].filter(Boolean);
      return acc;
    }, {});
  };
}
function interpolate(text, params) {
  return text.replace(/(['"]?\{\s*(\d+)\s*\}['"]?)/g, (_, match, digit) => params[digit] ?? match).replace(/\s+/g, " ");
}
function escapeHtmlChars(value) {
  return value && typeof value === "string" ? value.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;") : value;
}
var ContentProjectionService = class _ContentProjectionService {
  constructor(injector) {
    this.injector = injector;
  }
  projectContent(projectionStrategy, injector = this.injector) {
    return projectionStrategy.injectContent(injector);
  }
  static {
    this.ɵfac = function ContentProjectionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ContentProjectionService)(ɵɵinject(Injector));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ContentProjectionService,
      factory: _ContentProjectionService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ContentProjectionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
function pushValueTo(array) {
  return (element) => {
    array.push(element);
    return array;
  };
}
function noop() {
  const fn = function() {
  };
  return fn;
}
function isUndefinedOrEmptyString(value) {
  return value === void 0 || value === "";
}
function isNullOrUndefined(obj) {
  return obj === null || obj === void 0;
}
function isNullOrEmpty(obj) {
  return obj === null || obj === void 0 || obj === "";
}
function exists(obj) {
  return !isNullOrUndefined(obj);
}
function isObject(obj) {
  return obj instanceof Object;
}
function isArray(obj) {
  return Array.isArray(obj);
}
function isObjectAndNotArray(obj) {
  return isObject(obj) && !isArray(obj);
}
function isNode(obj) {
  return obj instanceof Node;
}
function isObjectAndNotArrayNotNode(obj) {
  return isObjectAndNotArray(obj) && !isNode(obj);
}
function checkHasProp(object, key) {
  return Object.prototype.hasOwnProperty.call(object, key);
}
function getShortDateFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return dateTimeFormat.shortDatePattern;
}
function getShortTimeFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return dateTimeFormat?.shortTimePattern?.replace("tt", "a");
}
function getShortDateShortTimeFormat(configStateService) {
  const dateTimeFormat = configStateService.getDeep("localization.currentCulture.dateTimeFormat");
  return `${dateTimeFormat.shortDatePattern} ${dateTimeFormat?.shortTimePattern?.replace("tt", "a")}`;
}
function deepMerge(target, source) {
  if (isObjectAndNotArrayNotNode(target) && isObjectAndNotArrayNotNode(source)) {
    return deepMergeRecursively(target, source);
  } else if (isNullOrUndefined(target) && isNullOrUndefined(source)) {
    return {};
  } else {
    return exists(source) ? source : target;
  }
}
function deepMergeRecursively(target, source) {
  const shouldNotRecurse = isNullOrUndefined(target) || isNullOrUndefined(source) || // at least one not defined
  isArray(target) || isArray(source) || // at least one array
  !isObject(target) || !isObject(source) || // at least one not an object
  isNode(target) || isNode(source);
  if (shouldNotRecurse) {
    return exists(source) ? source : target;
  }
  const keysOfTarget = Object.keys(target);
  const keysOfSource = Object.keys(source);
  const uniqueKeys = new Set(keysOfTarget.concat(keysOfSource));
  return [...uniqueKeys].reduce((retVal, key) => {
    retVal[key] = deepMergeRecursively(target[key], source[key]);
    return retVal;
  }, {});
}
var InternalStore = class {
  get state() {
    return this.state$.value;
  }
  constructor(initialState) {
    this.initialState = initialState;
    this.state$ = new BehaviorSubject(this.initialState);
    this.update$ = new Subject();
    this.sliceState = (selector, compareFn = collectionCompare) => this.state$.pipe(map(selector), distinctUntilChanged(compareFn));
    this.sliceUpdate = (selector, filterFn = (x) => x !== void 0) => this.update$.pipe(map(selector), filter(filterFn));
  }
  patch(state) {
    let patchedState = state;
    if (typeof state === "object" && !Array.isArray(state)) {
      patchedState = __spreadValues(__spreadValues({}, this.state), state);
    }
    this.state$.next(patchedState);
    this.update$.next(patchedState);
  }
  deepPatch(state) {
    this.state$.next(deepMerge(this.state, state));
    this.update$.next(state);
  }
  set(state) {
    this.state$.next(state);
    this.update$.next(state);
  }
  reset() {
    this.set(this.initialState);
  }
};
var mapToApiUrl = (key) => (apis) => (key && apis[key] || apis.default).url || apis.default.url;
var mapToIssuer = (issuer) => {
  if (!issuer) {
    return issuer;
  }
  return issuer.endsWith("/") ? issuer : issuer + "/";
};
var EnvironmentService = class _EnvironmentService {
  constructor() {
    this.store = new InternalStore({});
  }
  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }
  getEnvironment$() {
    return this.store.sliceState((state) => state);
  }
  getEnvironment() {
    return this.store.state;
  }
  getApiUrl(key) {
    return mapToApiUrl(key)(this.store.state?.apis);
  }
  getApiUrl$(key) {
    return this.store.sliceState((state) => state.apis).pipe(map(mapToApiUrl(key)));
  }
  setState(environment) {
    this.store.set(environment);
  }
  getIssuer() {
    const issuer = this.store.state?.oAuthConfig?.issuer;
    return mapToIssuer(issuer);
  }
  getIssuer$() {
    return this.store.sliceState((state) => state?.oAuthConfig?.issuer).pipe(map(mapToIssuer));
  }
  getImpersonation() {
    return this.store.state?.oAuthConfig?.impersonation || {};
  }
  getImpersonation$() {
    return this.store.sliceState((state) => state?.oAuthConfig?.impersonation || {});
  }
  static {
    this.ɵfac = function EnvironmentService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EnvironmentService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _EnvironmentService,
      factory: _EnvironmentService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EnvironmentService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var HttpErrorReporterService = class _HttpErrorReporterService {
  constructor() {
    this._reporter$ = new Subject();
    this._errors$ = new BehaviorSubject([]);
  }
  get reporter$() {
    return this._reporter$.asObservable();
  }
  get errors$() {
    return this._errors$.asObservable();
  }
  get errors() {
    return this._errors$.value;
  }
  reportError(error) {
    this._reporter$.next(error);
    this._errors$.next([...this.errors, error]);
  }
  static {
    this.ɵfac = function HttpErrorReporterService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _HttpErrorReporterService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _HttpErrorReporterService,
      factory: _HttpErrorReporterService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HttpErrorReporterService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
function getRemoteEnv(injector, environment) {
  const environmentService = injector.get(EnvironmentService);
  const {
    remoteEnv
  } = environment;
  const {
    headers = {},
    method = "GET",
    url
  } = remoteEnv || {};
  if (!url) return Promise.resolve();
  const http = injector.get(HttpClient);
  const httpErrorReporter = injector.get(HttpErrorReporterService);
  return http.request(method, url, {
    headers
  }).pipe(
    catchError((err) => {
      httpErrorReporter.reportError(err);
      return of(null);
    }),
    // TODO: Consider get handle function from a provider
    tap((env) => environmentService.setState(mergeEnvironments(environment, env || {}, remoteEnv)))
  ).toPromise();
}
function mergeEnvironments(local, remote, config) {
  switch (config.mergeStrategy) {
    case "deepmerge":
      return deepMerge(local, remote);
    case "overwrite":
    case null:
    case void 0:
      return remote;
    default:
      return config.mergeStrategy(local, remote);
  }
}
var LazyModuleFactory = class extends NgModuleFactory$1 {
  get moduleType() {
    return this.moduleWithProviders.ngModule;
  }
  constructor(moduleWithProviders) {
    super();
    this.moduleWithProviders = moduleWithProviders;
  }
  create(parentInjector) {
    const injector = Injector.create(__spreadProps(__spreadValues({}, parentInjector && {
      parent: parentInjector
    }), {
      providers: this.moduleWithProviders.providers
    }));
    const compiler = injector.get(Compiler);
    const factory = compiler.compileModuleSync(this.moduleType);
    return factory.create(injector);
  }
};
function featuresFactory(configState, featureKeys, mapFn = (features) => features) {
  return configState.getFeatures$(featureKeys).pipe(filter(Boolean), map(mapFn));
}
function downloadBlob(blob, filename) {
  const blobUrl = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = blobUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.dispatchEvent(new MouseEvent("click", {
    bubbles: true,
    cancelable: true,
    view: window
  }));
  document.body.removeChild(link);
}
function isNumber(value) {
  return value == Number(value);
}
function mapEnumToOptions(_enum) {
  const options = [];
  for (const member in _enum) if (!isNumber(member)) options.push({
    key: member,
    value: _enum[member]
  });
  return options;
}
function uuid(a) {
  return a ? (a ^ Math.random() * 16 >> a / 4).toString(16) : ("10000000-1000-4000-8000" + -1e11).replace(/[018]/g, uuid);
}
function generateHash(value) {
  let hashed = 0;
  let charCode;
  for (let i = 0; i < value.length; i++) {
    charCode = value.charCodeAt(i);
    hashed = (hashed << 5) - hashed + charCode;
    hashed |= 0;
  }
  return hashed;
}
function generatePassword(injector, length = 8) {
  if (injector) {
    length = getRequiredPasswordLength(injector);
  }
  length = Math.min(Math.max(4, length), 128);
  const lowers = "abcdefghjkmnpqrstuvwxyz";
  const uppers = "ABCDEFGHJKMNPQRSTUVWXYZ";
  const numbers = "23456789";
  const specials = "!*_#/+-.";
  const all = lowers + uppers + numbers + specials;
  const getRandom = (chrSet) => chrSet[Math.floor(Math.random() * chrSet.length)];
  const password = Array({
    length
  });
  password[0] = getRandom(lowers);
  password[1] = getRandom(uppers);
  password[2] = getRandom(numbers);
  password[3] = getRandom(specials);
  for (let i = 4; i < length; i++) {
    password[i] = getRandom(all);
  }
  return password.sort(() => 0.5 - Math.random()).join("");
}
function getRequiredPasswordLength(injector) {
  const configState = injector.get(ConfigStateService);
  const passwordRules = configState.getSettings("Identity.Password");
  return Number(passwordRules["Abp.Identity.Password.RequiredLength"]) || 8;
}
function getPathName(url) {
  const {
    pathname
  } = new URL(url, window.location.origin);
  return pathname;
}
var WebHttpUrlEncodingCodec = class {
  encodeKey(k) {
    return encodeURIComponent(k);
  }
  encodeValue(v) {
    return encodeURIComponent(v);
  }
  decodeKey(k) {
    return decodeURIComponent(k);
  }
  decodeValue(v) {
    return decodeURIComponent(v);
  }
};
var AbpLocalStorageService = class _AbpLocalStorageService {
  constructor() {
  }
  get length() {
    return localStorage.length;
  }
  clear() {
    localStorage.clear();
  }
  getItem(key) {
    return localStorage.getItem(key);
  }
  key(index2) {
    return localStorage.key(index2);
  }
  removeItem(key) {
    localStorage.removeItem(key);
  }
  setItem(key, value) {
    localStorage.setItem(key, value);
  }
  static {
    this.ɵfac = function AbpLocalStorageService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpLocalStorageService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpLocalStorageService,
      factory: _AbpLocalStorageService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpLocalStorageService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var SessionStateService = class _SessionStateService {
  constructor(configState, localStorageService) {
    this.configState = configState;
    this.localStorageService = localStorageService;
    this.store = new InternalStore({});
    this.document = inject(DOCUMENT);
    this.updateLocalStorage = () => {
      this.localStorageService.setItem("abpSession", JSON.stringify(this.store.state));
    };
    this.init();
    this.setInitialLanguage();
  }
  init() {
    const session = this.localStorageService.getItem("abpSession");
    if (session) {
      this.store.set(JSON.parse(session));
    }
    this.store.sliceUpdate((state) => state).subscribe(this.updateLocalStorage);
  }
  setInitialLanguage() {
    const appLanguage = this.getLanguage();
    this.configState.getDeep$("localization.currentCulture.cultureName").pipe(filter((cultureName) => !!cultureName), take(1)).subscribe((lang) => {
      if (lang.includes(";")) {
        lang = lang.split(";")[0];
      }
      this.setLanguage(lang);
    });
  }
  onLanguageChange$() {
    return this.store.sliceUpdate((state) => state.language);
  }
  onTenantChange$() {
    return this.store.sliceUpdate((state) => state.tenant);
  }
  getLanguage() {
    return this.store.state.language;
  }
  getLanguage$() {
    return this.store.sliceState((state) => state.language);
  }
  getTenant() {
    return this.store.state.tenant;
  }
  getTenant$() {
    return this.store.sliceState((state) => state.tenant);
  }
  setTenant(tenant) {
    if (collectionCompare(tenant, this.store.state.tenant)) return;
    this.store.set(__spreadProps(__spreadValues({}, this.store.state), {
      tenant
    }));
  }
  setLanguage(language) {
    const currentLanguage = this.store.state.language;
    if (language !== currentLanguage) {
      this.store.patch({
        language
      });
    }
    const currentAttribute = this.document.documentElement.getAttribute("lang");
    if (language !== currentAttribute) {
      this.document.documentElement.setAttribute("lang", language);
    }
  }
  static {
    this.ɵfac = function SessionStateService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SessionStateService)(ɵɵinject(ConfigStateService), ɵɵinject(AbpLocalStorageService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _SessionStateService,
      factory: _SessionStateService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SessionStateService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: AbpLocalStorageService
  }], null);
})();
var APP_INIT_ERROR_HANDLERS = new InjectionToken("APP_INIT_ERROR_HANDLERS");
var AbpTenantService = class _AbpTenantService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.findTenantById = (id, config) => this.restService.request({
      method: "GET",
      url: `/api/abp/multi-tenancy/tenants/by-id/${id}`
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.findTenantByName = (name, config) => this.restService.request({
      method: "GET",
      url: `/api/abp/multi-tenancy/tenants/by-name/${name}`
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function AbpTenantService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpTenantService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpTenantService,
      factory: _AbpTenantService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpTenantService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var TENANT_KEY = new InjectionToken("TENANT_KEY");
var IS_EXTERNAL_REQUEST = new HttpContextToken(() => false);
var ExternalHttpClient = class _ExternalHttpClient extends HttpClient {
  request(first, url, options = {}) {
    if (typeof first === "string") {
      this.#setPlaceholderContext(options);
      return super.request(first, url || "", options);
    }
    this.#setPlaceholderContext(first);
    return super.request(first);
  }
  #setPlaceholderContext(optionsOrRequest) {
    optionsOrRequest.context ??= new HttpContext();
    optionsOrRequest.context.set(IS_EXTERNAL_REQUEST, true);
  }
  static {
    this.ɵfac = /* @__PURE__ */ (() => {
      let ɵExternalHttpClient_BaseFactory;
      return function ExternalHttpClient_Factory(__ngFactoryType__) {
        return (ɵExternalHttpClient_BaseFactory || (ɵExternalHttpClient_BaseFactory = ɵɵgetInheritedFactory(_ExternalHttpClient)))(__ngFactoryType__ || _ExternalHttpClient);
      };
    })();
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ExternalHttpClient,
      factory: _ExternalHttpClient.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExternalHttpClient, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var RestService = class _RestService {
  constructor(options, http, externalHttp, environment, httpErrorReporter) {
    this.options = options;
    this.http = http;
    this.externalHttp = externalHttp;
    this.environment = environment;
    this.httpErrorReporter = httpErrorReporter;
  }
  getApiFromStore(apiName) {
    return this.environment.getApiUrl(apiName);
  }
  handleError(err) {
    this.httpErrorReporter.reportError(err);
    return throwError(() => err);
  }
  request(request, config, api) {
    config = config || {};
    api = api || this.getApiFromStore(config.apiName);
    const _a = request, {
      method,
      params
    } = _a, options = __objRest(_a, [
      "method",
      "params"
    ]);
    const {
      observe = "body",
      skipHandleError
    } = config;
    const url = this.removeDuplicateSlashes(api + request.url);
    const httpClient = this.getHttpClient(config.skipAddingHeader);
    return httpClient.request(method, url, __spreadValues(__spreadValues({
      observe
    }, params && {
      params: this.getParams(params, config.httpParamEncoder)
    }), options)).pipe(catchError((err) => skipHandleError ? throwError(() => err) : this.handleError(err)));
  }
  getHttpClient(isExternal) {
    return isExternal ? this.externalHttp : this.http;
  }
  getParams(params, encoder) {
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      if (isUndefinedOrEmptyString(value)) return acc;
      if (value === null && !this.options.sendNullsAsQueryParam) return acc;
      acc[key] = value;
      return acc;
    }, {});
    return encoder ? new HttpParams({
      encoder,
      fromObject: filteredParams
    }) : new HttpParams({
      fromObject: filteredParams
    });
  }
  removeDuplicateSlashes(url) {
    return url.replace(/([^:]\/)\/+/g, "$1");
  }
  static {
    this.ɵfac = function RestService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RestService)(ɵɵinject(CORE_OPTIONS), ɵɵinject(HttpClient), ɵɵinject(ExternalHttpClient), ɵɵinject(EnvironmentService), ɵɵinject(HttpErrorReporterService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _RestService,
      factory: _RestService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RestService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Inject,
      args: [CORE_OPTIONS]
    }]
  }, {
    type: HttpClient
  }, {
    type: ExternalHttpClient
  }, {
    type: EnvironmentService
  }, {
    type: HttpErrorReporterService
  }], null);
})();
var MultiTenancyService = class _MultiTenancyService {
  constructor(restService, sessionState, tenantService, configStateService, tenantKey) {
    this.restService = restService;
    this.sessionState = sessionState;
    this.tenantService = tenantService;
    this.configStateService = configStateService;
    this.tenantKey = tenantKey;
    this.domainTenant = null;
    this.isTenantBoxVisible = true;
    this.apiName = "abp";
    this.setTenantToState = (tenant) => {
      this.sessionState.setTenant({
        id: tenant.tenantId,
        name: tenant.name,
        isAvailable: true
      });
      return this.configStateService.refreshAppState().pipe(map((_) => tenant));
    };
  }
  setTenantByName(tenantName) {
    return this.tenantService.findTenantByName(tenantName).pipe(switchMap(this.setTenantToState));
  }
  setTenantById(tenantId) {
    return this.tenantService.findTenantById(tenantId).pipe(switchMap(this.setTenantToState));
  }
  static {
    this.ɵfac = function MultiTenancyService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MultiTenancyService)(ɵɵinject(RestService), ɵɵinject(SessionStateService), ɵɵinject(AbpTenantService), ɵɵinject(ConfigStateService), ɵɵinject(TENANT_KEY));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _MultiTenancyService,
      factory: _MultiTenancyService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MultiTenancyService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }, {
    type: SessionStateService
  }, {
    type: AbpTenantService
  }, {
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [TENANT_KEY]
    }]
  }], null);
})();
var TENANT_NOT_FOUND_BY_NAME = new InjectionToken("TENANT_NOT_FOUND_BY_NAME");
var tenancyPlaceholder = "{0}";
function getCurrentTenancyName(appBaseUrl) {
  if (appBaseUrl.charAt(appBaseUrl.length - 1) !== "/") appBaseUrl += "/";
  const parseTokens = createTokenParser(appBaseUrl);
  const token = tenancyPlaceholder.replace(/[}{]/g, "");
  return parseTokens(window.location.href)[token]?.[0];
}
function getCurrentTenancyNameFromUrl(tenantKey) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(tenantKey);
}
function parseTenantFromUrl(injector) {
  return __async(this, null, function* () {
    const environmentService = injector.get(EnvironmentService);
    const multiTenancyService = injector.get(MultiTenancyService);
    const tenantNotFoundHandler = injector.get(TENANT_NOT_FOUND_BY_NAME, null);
    const baseUrl = environmentService.getEnvironment()?.application?.baseUrl || "";
    const tenancyName = getCurrentTenancyName(baseUrl);
    const hideTenantBox = () => {
      multiTenancyService.isTenantBoxVisible = false;
    };
    const setDomainTenant = (tenant) => {
      multiTenancyService.domainTenant = {
        id: tenant.tenantId,
        name: tenant.name,
        isAvailable: true
      };
    };
    const setEnvironmentWithDomainTenant = (tenant) => {
      hideTenantBox();
      setDomainTenant(tenant);
    };
    if (tenancyName) {
      replaceTenantNameWithinEnvironment(injector, tenancyName);
      const tenant$ = multiTenancyService.setTenantByName(tenancyName);
      try {
        const result = yield firstValueFrom(tenant$);
        setEnvironmentWithDomainTenant(result);
        return Promise.resolve(result);
      } catch (httpError) {
        if (httpError instanceof HttpErrorResponse && httpError.status === 404 && tenantNotFoundHandler) {
          tenantNotFoundHandler(httpError);
        }
        return Promise.reject();
      }
    }
    replaceTenantNameWithinEnvironment(injector, "", tenancyPlaceholder + ".");
    const tenantIdFromQueryParams = getCurrentTenancyNameFromUrl(multiTenancyService.tenantKey);
    if (tenantIdFromQueryParams) {
      const tenantById$ = multiTenancyService.setTenantById(tenantIdFromQueryParams);
      return firstValueFrom(tenantById$);
    }
    return Promise.resolve();
  });
}
function replaceTenantNameWithinEnvironment(injector, tenancyName, placeholder = tenancyPlaceholder) {
  const environmentService = injector.get(EnvironmentService);
  const environment = collectionClone(environmentService.getEnvironment());
  if (environment.application.baseUrl) {
    environment.application.baseUrl = environment.application.baseUrl.replace(placeholder, tenancyName);
  }
  if (environment.oAuthConfig?.redirectUri) {
    environment.oAuthConfig.redirectUri = environment.oAuthConfig.redirectUri.replace(placeholder, tenancyName);
  }
  if (!environment.oAuthConfig) {
    environment.oAuthConfig = {};
  }
  environment.oAuthConfig.issuer = (environment.oAuthConfig.issuer || "").replace(placeholder, tenancyName);
  Object.keys(environment.apis).forEach((api) => {
    Object.keys(environment.apis[api]).forEach((key) => {
      environment.apis[api][key] = (environment.apis[api][key] || "").replace(placeholder, tenancyName);
    });
  });
  return environmentService.setState(environment);
}
var CHECK_AUTHENTICATION_STATE_FN_KEY = new InjectionToken("CHECK_AUTHENTICATION_STATE_FN_KEY");
function getInitialData(injector) {
  const fn = () => __async(this, null, function* () {
    const environmentService = injector.get(EnvironmentService);
    const configState = injector.get(ConfigStateService);
    const options = injector.get(CORE_OPTIONS);
    environmentService.setState(options.environment);
    yield getRemoteEnv(injector, options.environment);
    yield parseTenantFromUrl(injector);
    const authService = injector.get(AuthService, void 0, {
      optional: true
    });
    const checkAuthenticationState = injector.get(CHECK_AUTHENTICATION_STATE_FN_KEY, noop, {
      optional: true
    });
    if (!options.skipInitAuthService && authService) {
      yield authService.init();
    }
    if (options.skipGetAppConfiguration) return;
    const result$ = configState.refreshAppState().pipe(tap(() => checkAuthenticationState(injector)), tap(() => {
      const currentTenant = configState.getOne("currentTenant");
      injector.get(SessionStateService).setTenant(currentTenant);
    }), catchError((error) => {
      const appInitErrorHandlers = injector.get(APP_INIT_ERROR_HANDLERS, null);
      if (appInitErrorHandlers && appInitErrorHandlers.length) {
        appInitErrorHandlers.forEach((func) => func(error));
      }
      return throwError(error);
    }));
    yield lastValueFrom(result$);
  });
  return fn;
}
function localeInitializer(injector) {
  const fn = () => {
    const sessionState = injector.get(SessionStateService);
    const {
      registerLocaleFn
    } = injector.get(CORE_OPTIONS);
    const lang = sessionState.getLanguage() || "en";
    return new Promise((resolve, reject) => {
      registerLocaleFn(lang).then((module) => {
        if (module?.default) registerLocaleData(module.default);
        return resolve("resolved");
      }, reject);
    });
  };
  return fn;
}
var CrossOriginStrategy = class {
  constructor(crossorigin, integrity) {
    this.crossorigin = crossorigin;
    this.integrity = integrity;
  }
  setCrossOrigin(element) {
    if (this.integrity) element.setAttribute("integrity", this.integrity);
    if (this.crossorigin) {
      element.setAttribute("crossorigin", this.crossorigin);
    }
  }
};
var NoCrossOriginStrategy = class extends CrossOriginStrategy {
  setCrossOrigin() {
  }
};
var CROSS_ORIGIN_STRATEGY = {
  Anonymous(integrity) {
    return new CrossOriginStrategy("anonymous", integrity);
  },
  UseCredentials(integrity) {
    return new CrossOriginStrategy("use-credentials", integrity);
  },
  None() {
    return new NoCrossOriginStrategy(null);
  }
};
var DomStrategy = class {
  constructor(target = document.head, position = "beforeend") {
    this.target = target;
    this.position = position;
  }
  insertElement(element) {
    this.target.insertAdjacentElement(this.position, element);
  }
};
var DOM_STRATEGY = {
  AfterElement(element) {
    return new DomStrategy(element, "afterend");
  },
  AppendToBody() {
    return new DomStrategy(document.body, "beforeend");
  },
  AppendToHead() {
    return new DomStrategy(document.head, "beforeend");
  },
  BeforeElement(element) {
    return new DomStrategy(element, "beforebegin");
  },
  PrependToHead() {
    return new DomStrategy(document.head, "afterbegin");
  }
};
function fromLazyLoad(element, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {
  crossOriginStrategy.setCrossOrigin(element);
  domStrategy.insertElement(element);
  return new Observable((observer) => {
    element.onload = (event) => {
      clearCallbacks(element);
      observer.next(event);
      observer.complete();
    };
    const handleError = createErrorHandler(observer, element);
    element.onerror = handleError;
    element.onabort = handleError;
    element.onemptied = handleError;
    element.onstalled = handleError;
    element.onsuspend = handleError;
    return () => {
      clearCallbacks(element);
      observer.complete();
    };
  });
}
function createErrorHandler(observer, element) {
  return function(event) {
    clearCallbacks(element);
    element.parentNode?.removeChild(element);
    observer.error(event);
  };
}
function clearCallbacks(element) {
  element.onload = null;
  element.onerror = null;
  element.onabort = null;
  element.onemptied = null;
  element.onstalled = null;
  element.onsuspend = null;
}
var DefaultQueueManager = class {
  constructor() {
    this.queue = [];
    this.isRunning = false;
    this.stack = 0;
    this.interval = 0;
    this.stackSize = 100;
  }
  init(interval, stackSize) {
    this.interval = interval;
    this.stackSize = stackSize;
  }
  add(fn) {
    this.queue.push(fn);
    this.run();
  }
  run() {
    if (this.isRunning) return;
    this.stack++;
    this.isRunning = true;
    const fn = this.queue.shift();
    if (!fn) {
      this.isRunning = false;
      return;
    }
    fn();
    if (this.stack > this.stackSize) {
      setTimeout(() => {
        this.isRunning = false;
        this.run();
        this.stack = 0;
      }, this.interval);
    } else {
      this.isRunning = false;
      this.run();
    }
  }
};
function findRoute(routesService, path) {
  const node = routesService.find((route) => route.path === path);
  return node || path === "/" ? node : findRoute(routesService, path.split("/").slice(0, -1).join("/") || "/");
}
function getRoutePath(router, url = router.url) {
  const emptyGroup = {
    segments: []
  };
  const primaryGroup = router.parseUrl(url).root.children[PRIMARY_OUTLET];
  return "/" + (primaryGroup || emptyGroup).segments.map(({
    path
  }) => path).join("/");
}
function reloadRoute(router, ngZone) {
  const {
    shouldReuseRoute
  } = router.routeReuseStrategy;
  const setRouteReuse = (reuse) => {
    router.routeReuseStrategy.shouldReuseRoute = reuse;
  };
  setRouteReuse(() => false);
  router.navigated = false;
  ngZone.run(() => __async(this, null, function* () {
    yield router.navigateByUrl(router.url).catch(noop);
    setRouteReuse(shouldReuseRoute);
  }));
}
var BaseTreeNode = class _BaseTreeNode {
  constructor(props) {
    this.children = [];
    this.isLeaf = true;
    Object.assign(this, props);
  }
  static create(props) {
    return new _BaseTreeNode(props);
  }
};
function createTreeFromList(list, keySelector, parentKeySelector, valueMapper) {
  const map2 = createMapFromList(list, keySelector, valueMapper);
  const tree = [];
  list.forEach((row) => {
    const id = keySelector(row);
    const parentId = parentKeySelector(row);
    const node = map2.get(id);
    if (!node) return;
    if (parentId) {
      const parent = map2.get(parentId);
      if (!parent) return;
      parent.children.push(node);
      parent.isLeaf = false;
      node.parent = parent;
    } else {
      tree.push(node);
    }
  });
  return tree;
}
function createMapFromList(list, keySelector, valueMapper) {
  const map2 = /* @__PURE__ */ new Map();
  list.forEach((row) => map2.set(keySelector(row), valueMapper(row)));
  return map2;
}
function createTreeNodeFilterCreator(key, mapperFn) {
  return (search) => {
    const regex = new RegExp(".*" + search + ".*", "i");
    return function collectNodes(nodes, matches = []) {
      for (const node of nodes) {
        if (regex.test(mapperFn(node[key]))) matches.push(node);
        if (node.children.length) collectNodes(node.children, matches);
      }
      return matches;
    };
  };
}
function createGroupMap(list, othersGroupKey) {
  if (!isArray(list) || !list.some((node) => Boolean(node.group))) return void 0;
  const mapGroup = /* @__PURE__ */ new Map();
  for (const node of list) {
    const group = node?.group || othersGroupKey;
    if (typeof group !== "string") {
      throw new Error(`Invalid group: ${group}`);
    }
    const items = mapGroup.get(group) || [];
    items.push(node);
    mapGroup.set(group, items);
  }
  return mapGroup;
}
var DomInsertionService = class _DomInsertionService {
  constructor() {
    this.inserted = /* @__PURE__ */ new Set();
  }
  insertContent(contentStrategy) {
    const hash = generateHash(contentStrategy.content);
    if (this.inserted.has(hash)) return;
    const element = contentStrategy.insertElement();
    this.inserted.add(hash);
    return element;
  }
  removeContent(element) {
    if (element.textContent) {
      const hash = generateHash(element.textContent);
      this.inserted.delete(hash);
      element.parentNode?.removeChild(element);
    }
  }
  has(content) {
    const hash = generateHash(content);
    return this.inserted.has(hash);
  }
  static {
    this.ɵfac = function DomInsertionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _DomInsertionService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _DomInsertionService,
      factory: _DomInsertionService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DomInsertionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var LOADER_DELAY = new InjectionToken("LOADER_DELAY");
var HttpWaitService = class _HttpWaitService {
  constructor(injector) {
    this.store = new InternalStore({
      requests: [],
      filteredRequests: []
    });
    this.destroy$ = new Subject();
    this.delay = injector.get(LOADER_DELAY, 500);
  }
  getLoading() {
    return !!this.applyFilter(this.store.state.requests).length;
  }
  getLoading$() {
    return this.store.sliceState(({
      requests
    }) => requests).pipe(map((requests) => !!this.applyFilter(requests).length), switchMap((condition) => condition ? this.delay === 0 ? of(true) : timer(this.delay).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next()));
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      requests
    }) => !!this.applyFilter(requests).length);
  }
  clearLoading() {
    this.store.patch({
      requests: []
    });
  }
  addRequest(request) {
    this.store.patch({
      requests: [...this.store.state.requests, request]
    });
  }
  deleteRequest(request) {
    const requests = this.store.state.requests.filter((r) => r !== request);
    this.store.patch({
      requests
    });
  }
  addFilter(request) {
    const requests = Array.isArray(request) ? request : [request];
    const filteredRequests = [...this.store.state.filteredRequests.filter((f) => !requests.some((r) => this.isSameRequest(f, r))), ...requests];
    this.store.patch({
      filteredRequests
    });
  }
  removeFilter(request) {
    const requests = Array.isArray(request) ? request : [request];
    const filteredRequests = this.store.state.filteredRequests.filter((f) => !requests.some((r) => this.isSameRequest(f, r)));
    this.store.patch({
      filteredRequests
    });
  }
  applyFilter(requests) {
    if (!requests) {
      return [];
    }
    const {
      filteredRequests
    } = this.store.state;
    return requests.filter(({
      method,
      url
    }) => !filteredRequests.find((filteredRequest) => this.isSameRequest(filteredRequest, {
      method,
      endpoint: getPathName(url)
    })));
  }
  isSameRequest(filteredRequest, request) {
    const {
      method,
      endpoint
    } = filteredRequest;
    return endpoint === request.endpoint && method === request.method;
  }
  static {
    this.ɵfac = function HttpWaitService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _HttpWaitService)(ɵɵinject(Injector));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _HttpWaitService,
      factory: _HttpWaitService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HttpWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
var ResourceWaitService = class _ResourceWaitService {
  constructor() {
    this.store = new InternalStore({
      resources: /* @__PURE__ */ new Set()
    });
  }
  getLoading() {
    return !!this.store.state.resources.size;
  }
  getLoading$() {
    return this.store.sliceState(({
      resources
    }) => !!resources.size);
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      resources
    }) => !!resources?.size);
  }
  clearLoading() {
    this.store.patch({
      resources: /* @__PURE__ */ new Set()
    });
  }
  addResource(resource) {
    const resources = this.store.state.resources;
    resources.add(resource);
    this.store.patch({
      resources
    });
  }
  deleteResource(resource) {
    const resources = this.store.state.resources;
    resources.delete(resource);
    this.store.patch({
      resources
    });
  }
  static {
    this.ɵfac = function ResourceWaitService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ResourceWaitService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ResourceWaitService,
      factory: _ResourceWaitService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ResourceWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var LazyLoadService = class _LazyLoadService {
  constructor(resourceWaitService) {
    this.resourceWaitService = resourceWaitService;
    this.loaded = /* @__PURE__ */ new Map();
  }
  load(strategy, retryTimes, retryDelay) {
    if (this.loaded.has(strategy.path)) return of(new CustomEvent("load"));
    this.resourceWaitService.addResource(strategy.path);
    const delayOperator = retryDelay ? pipe(delay(retryDelay)) : pipe();
    const takeOp = retryTimes ? pipe(take(retryTimes)) : pipe();
    return strategy.createStream().pipe(retryWhen((error$) => concat(error$.pipe(delayOperator, takeOp), throwError(() => new CustomEvent("error")))), tap(() => {
      this.loaded.set(strategy.path, strategy.element);
      this.resourceWaitService.deleteResource(strategy.path);
    }), delay(100), shareReplay({
      bufferSize: 1,
      refCount: true
    }));
  }
  remove(path) {
    const element = this.loaded.get(path);
    if (!element) return false;
    element.parentNode?.removeChild(element);
    this.loaded.delete(path);
    return true;
  }
  static {
    this.ɵfac = function LazyLoadService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LazyLoadService)(ɵɵinject(ResourceWaitService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _LazyLoadService,
      factory: _LazyLoadService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LazyLoadService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ResourceWaitService
  }], null);
})();
var LIST_QUERY_DEBOUNCE_TIME = new InjectionToken("LIST_QUERY_DEBOUNCE_TIME");
var ListService = class _ListService {
  set filter(value) {
    this._filter = value;
    this.get();
  }
  get filter() {
    return this._filter;
  }
  set maxResultCount(value) {
    this._maxResultCount = value;
    this.get();
  }
  get maxResultCount() {
    return this._maxResultCount;
  }
  set page(value) {
    if (value === this._page) return;
    this._page = value;
    this.get();
  }
  get page() {
    return this._page;
  }
  set totalCount(value) {
    if (value === this._totalCount) return;
    this._totalCount = value;
    this.get();
  }
  get totalCount() {
    return this._totalCount;
  }
  set sortKey(value) {
    this._sortKey = value;
    this.get();
  }
  get sortKey() {
    return this._sortKey;
  }
  set sortOrder(value) {
    this._sortOrder = value;
    this.get();
  }
  get sortOrder() {
    return this._sortOrder;
  }
  get query$() {
    return this._query$.asObservable().pipe(this.delay, shareReplay({
      bufferSize: 1,
      refCount: true
    }));
  }
  get isLoading$() {
    return this._isLoading$.asObservable();
  }
  constructor(injector) {
    this._filter = "";
    this._maxResultCount = 10;
    this._skipCount = 0;
    this._page = 0;
    this._totalCount = 0;
    this._sortKey = "";
    this._sortOrder = "";
    this._query$ = new ReplaySubject(1);
    this._isLoading$ = new BehaviorSubject(false);
    this.destroy$ = new Subject();
    this.get = () => {
      this.resetPageWhenUnchanged();
      this.next();
    };
    this.getWithoutPageReset = () => {
      this.next();
    };
    const delay2 = injector.get(LIST_QUERY_DEBOUNCE_TIME, 300);
    this.delay = delay2 ? debounceTime(delay2) : tap();
    this.get();
  }
  hookToQuery(streamCreatorCallback) {
    return this.query$.pipe(tap(() => this._isLoading$.next(true)), switchMap((query) => streamCreatorCallback(query).pipe(catchError(() => of(null)))), filter(Boolean), tap(() => this._isLoading$.next(false)), shareReplay({
      bufferSize: 1,
      refCount: true
    }), takeUntil(this.destroy$));
  }
  ngOnDestroy() {
    this.destroy$.next();
  }
  resetPageWhenUnchanged() {
    const maxPage = Number(Number(this.totalCount / this._maxResultCount).toFixed());
    const skipCount = this._page * this._maxResultCount;
    if (skipCount !== this._totalCount) {
      this._skipCount = skipCount;
      return;
    }
    if (this.page === maxPage && this.page > 0) {
      this._skipCount = skipCount - this._maxResultCount;
      this.page = this.page - 1;
    }
  }
  next() {
    this._query$.next({
      filter: this._filter || void 0,
      maxResultCount: this._maxResultCount,
      skipCount: this._page * this._maxResultCount,
      sorting: this._sortOrder ? `${this._sortKey} ${this._sortOrder}` : void 0
    });
  }
  static {
    this.ɵfac = function ListService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ListService)(ɵɵinject(Injector));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ListService,
      factory: _ListService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ListService, [{
    type: Injectable
  }], () => [{
    type: Injector
  }], null);
})();
var PermissionService = class _PermissionService {
  constructor(configState) {
    this.configState = configState;
  }
  getGrantedPolicy$(key) {
    return this.getStream().pipe(map((grantedPolicies) => this.isPolicyGranted(key, grantedPolicies)));
  }
  getGrantedPolicy(key) {
    const policies = this.getSnapshot();
    return this.isPolicyGranted(key, policies);
  }
  filterItemsByPolicy(items) {
    const policies = this.getSnapshot();
    return items.filter((item) => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies));
  }
  filterItemsByPolicy$(items) {
    return this.getStream().pipe(map((policies) => items.filter((item) => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies))));
  }
  isPolicyGranted(key, grantedPolicies) {
    if (!key) return true;
    const orRegexp = /\|\|/g;
    const andRegexp = /&&/g;
    if (orRegexp.test(key)) {
      const keys = key.split("||").filter(Boolean);
      if (keys.length < 2) return false;
      return keys.some((k) => this.getPolicy(k.trim(), grantedPolicies));
    } else if (andRegexp.test(key)) {
      const keys = key.split("&&").filter(Boolean);
      if (keys.length < 2) return false;
      return keys.every((k) => this.getPolicy(k.trim(), grantedPolicies));
    }
    return this.getPolicy(key, grantedPolicies);
  }
  getStream() {
    return this.configState.getAll$().pipe(map(this.mapToPolicies));
  }
  getSnapshot() {
    return this.mapToPolicies(this.configState.getAll());
  }
  mapToPolicies(applicationConfiguration) {
    return applicationConfiguration?.auth?.grantedPolicies || {};
  }
  getPolicy(key, grantedPolicies) {
    return grantedPolicies[key] || false;
  }
  static {
    this.ɵfac = function PermissionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PermissionService)(ɵɵinject(ConfigStateService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _PermissionService,
      factory: _PermissionService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: ConfigStateService
  }], null);
})();
var ReplaceableComponentsService = class _ReplaceableComponentsService {
  get replaceableComponents$() {
    return this.store.sliceState((state) => state);
  }
  get replaceableComponents() {
    return this.store.state;
  }
  get onUpdate$() {
    return this.store.sliceUpdate((state) => state);
  }
  constructor(ngZone, router) {
    this.ngZone = ngZone;
    this.router = router;
    this.store = new InternalStore([]);
  }
  add(replaceableComponent, reload) {
    const replaceableComponents = [...this.store.state];
    const index2 = replaceableComponents.findIndex((component) => component.key === replaceableComponent.key);
    if (index2 > -1) {
      replaceableComponents[index2] = replaceableComponent;
    } else {
      replaceableComponents.push(replaceableComponent);
    }
    this.store.set(replaceableComponents);
    if (reload) reloadRoute(this.router, this.ngZone);
  }
  get(replaceableComponentKey) {
    return this.replaceableComponents.find((component) => component.key === replaceableComponentKey);
  }
  get$(replaceableComponentKey) {
    return this.replaceableComponents$.pipe(map((components) => components.find((component) => component.key === replaceableComponentKey)));
  }
  static {
    this.ɵfac = function ReplaceableComponentsService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ReplaceableComponentsService)(ɵɵinject(NgZone), ɵɵinject(Router));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ReplaceableComponentsService,
      factory: _ReplaceableComponentsService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableComponentsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: NgZone
  }, {
    type: Router
  }], null);
})();
var NavigationEvent = {
  Cancel: NavigationCancel,
  End: NavigationEnd,
  Error: NavigationError,
  Start: NavigationStart
};
var RouterEvents = class _RouterEvents {
  #previousNavigation;
  #currentNavigation;
  constructor() {
    this.router = inject(Router);
    this.#previousNavigation = signal(void 0);
    this.previousNavigation = this.#previousNavigation.asReadonly();
    this.#currentNavigation = signal(void 0);
    this.currentNavigation = this.#currentNavigation.asReadonly();
    this.listenToNavigation();
  }
  listenToNavigation() {
    const routerEvent$ = this.router.events.pipe(filter((e) => e instanceof NavigationEvent.End && !e.url.includes("error")));
    routerEvent$.subscribe((event) => {
      this.#previousNavigation.set(this.currentNavigation());
      this.#currentNavigation.set(event.url);
    });
  }
  getEvents(...eventTypes) {
    const filterRouterEvents = (event) => eventTypes.some((type) => event instanceof type);
    return this.router.events.pipe(filter(filterRouterEvents));
  }
  getNavigationEvents(...navigationEventKeys) {
    const filterNavigationEvents = (event) => navigationEventKeys.some((key) => event instanceof NavigationEvent[key]);
    return this.router.events.pipe(filter(filterNavigationEvents));
  }
  getAllEvents() {
    return this.router.events;
  }
  getAllNavigationEvents() {
    const keys = Object.keys(NavigationEvent);
    return this.getNavigationEvents(...keys);
  }
  static {
    this.ɵfac = function RouterEvents_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RouterEvents)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _RouterEvents,
      factory: _RouterEvents.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterEvents, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var RouterWaitService = class _RouterWaitService {
  constructor(routerEvents, injector) {
    this.routerEvents = routerEvents;
    this.store = new InternalStore({
      loading: false
    });
    this.destroy$ = new Subject();
    this.delay = injector.get(LOADER_DELAY, 500);
    this.updateLoadingStatusOnNavigationEvents();
  }
  updateLoadingStatusOnNavigationEvents() {
    this.routerEvents.getAllNavigationEvents().pipe(map((event) => event instanceof NavigationStart), switchMap((condition) => condition ? this.delay === 0 ? of(true) : timer(this.delay || 0).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next())).subscribe((status) => {
      this.setLoading(status);
    });
  }
  getLoading() {
    return this.store.state.loading;
  }
  getLoading$() {
    return this.store.sliceState(({
      loading
    }) => loading);
  }
  updateLoading$() {
    return this.store.sliceUpdate(({
      loading
    }) => loading);
  }
  setLoading(loading) {
    this.store.patch({
      loading
    });
  }
  static {
    this.ɵfac = function RouterWaitService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RouterWaitService)(ɵɵinject(RouterEvents), ɵɵinject(Injector));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _RouterWaitService,
      factory: _RouterWaitService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterWaitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RouterEvents
  }, {
    type: Injector
  }], null);
})();
var COOKIE_LANGUAGE_KEY = new InjectionToken("COOKIE_LANGUAGE_KEY", {
  factory: () => ".AspNetCore.Culture"
});
var NAVIGATE_TO_MANAGE_PROFILE = new InjectionToken("NAVIGATE_TO_MANAGE_PROFILE");
var QUEUE_MANAGER = new InjectionToken("QUEUE_MANAGER");
var INCUDE_LOCALIZATION_RESOURCES_TOKEN = new InjectionToken("INCUDE_LOCALIZATION_RESOURCES_TOKEN");
var PIPE_TO_LOGIN_FN_KEY = new InjectionToken("PIPE_TO_LOGIN_FN_KEY");
var SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY = new InjectionToken("SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY");
var OTHERS_GROUP = new InjectionToken("OTHERS_GROUP");
var SORT_COMPARE_FUNC = new InjectionToken("SORT_COMPARE_FUNC");
function compareFuncFactory() {
  const localizationService = inject(LocalizationService);
  const fn = (a, b) => {
    const aNumber = a.order;
    const bNumber = b.order;
    if (aNumber > bNumber) return 1;
    if (aNumber < bNumber) return -1;
    if (a.id > b.id) return 1;
    if (a.id < b.id) return -1;
    if (!Number.isInteger(aNumber)) return 1;
    if (!Number.isInteger(bNumber)) return -1;
    const aName = localizationService.instant(a.name);
    const bName = localizationService.instant(b.name);
    if (aName > bName) return 1;
    if (aName < bName) return -1;
    return 0;
  };
  return fn;
}
var DYNAMIC_LAYOUTS_TOKEN = new InjectionToken("DYNAMIC_LAYOUTS");
var DISABLE_PROJECT_NAME = new InjectionToken("DISABLE_APP_NAME");
var AbstractTreeService = class {
  constructor() {
    this._flat$ = new BehaviorSubject([]);
    this._tree$ = new BehaviorSubject([]);
    this._visible$ = new BehaviorSubject([]);
    this.shouldSingularizeRoutes = true;
  }
  get flat() {
    return this._flat$.value;
  }
  get flat$() {
    return this._flat$.asObservable();
  }
  get tree() {
    return this._tree$.value;
  }
  get tree$() {
    return this._tree$.asObservable();
  }
  get visible() {
    return this._visible$.value;
  }
  get visible$() {
    return this._visible$.asObservable();
  }
  filterWith(setOrMap) {
    return this._flat$.value.filter((item) => !setOrMap.has(item[this.id]));
  }
  findItemsToRemove(set) {
    return this._flat$.value.reduce((acc, item) => {
      if (!acc.has(item[this.parentId])) {
        return acc;
      }
      const childSet = /* @__PURE__ */ new Set([item[this.id]]);
      const children = this.findItemsToRemove(childSet);
      return /* @__PURE__ */ new Set([...acc, ...children]);
    }, set);
  }
  publish(flatItems) {
    this._flat$.next(flatItems);
    this._tree$.next(this.createTree(flatItems));
    this._visible$.next(this.createTree(flatItems.filter((item) => !this.hide(item))));
    return flatItems;
  }
  createTree(items) {
    return createTreeFromList(items, (item) => item[this.id], (item) => item[this.parentId], (item) => BaseTreeNode.create(item));
  }
  createGroupedTree(list) {
    const map2 = createGroupMap(list, this.othersGroup);
    if (!map2) {
      return void 0;
    }
    return Array.from(map2, ([key, items]) => ({
      group: key,
      items
    }));
  }
  add(items) {
    let flatItems = [];
    if (!this.shouldSingularizeRoutes) {
      flatItems = [...this.flat, ...items];
    }
    if (this.shouldSingularizeRoutes) {
      const map2 = /* @__PURE__ */ new Map();
      items.forEach((item) => map2.set(item[this.id], item));
      flatItems = this.filterWith(map2);
      map2.forEach(pushValueTo(flatItems));
    }
    flatItems.sort(this.sort);
    return this.publish(flatItems);
  }
  find(predicate, tree = this.tree) {
    return tree.reduce((acc, node) => {
      if (acc) {
        return acc;
      }
      if (predicate(node)) {
        return node;
      }
      return this.find(predicate, node.children);
    }, null);
  }
  patch(identifier, props) {
    const flatItems = this._flat$.value;
    const index2 = flatItems.findIndex((item) => item[this.id] === identifier);
    if (index2 < 0) {
      return false;
    }
    flatItems[index2] = __spreadValues(__spreadValues({}, flatItems[index2]), props);
    flatItems.sort(this.sort);
    return this.publish(flatItems);
  }
  refresh() {
    return this.add([]);
  }
  remove(identifiers) {
    const set = /* @__PURE__ */ new Set();
    identifiers.forEach((id) => set.add(id));
    const setToRemove = this.findItemsToRemove(set);
    const flatItems = this.filterWith(setToRemove);
    return this.publish(flatItems);
  }
  removeByParam(params) {
    if (!params) {
      return null;
    }
    const keys = Object.keys(params);
    if (keys.length === 0) {
      return null;
    }
    const excludedList = this.flat.filter((item) => keys.every((key) => item[key] === params[key]));
    if (!excludedList?.length) {
      return null;
    }
    for (const item of excludedList) {
      this.removeByParam({
        [this.parentId]: item[this.id]
      });
    }
    const flatItems = this.flat.filter((item) => !excludedList.includes(item));
    return this.publish(flatItems);
  }
  search(params, tree = this.tree) {
    const searchKeys = Object.keys(params);
    return tree.reduce((acc, node) => {
      if (acc) {
        return acc;
      }
      if (searchKeys.every((key) => node[key] === params[key])) {
        return node;
      }
      return this.search(params, node.children);
    }, null);
  }
  setSingularizeStatus(singularize = true) {
    this.shouldSingularizeRoutes = singularize;
  }
};
var AbstractNavTreeService = class _AbstractNavTreeService extends AbstractTreeService {
  constructor(injector) {
    super();
    this.injector = injector;
    this.id = "name";
    this.parentId = "parentName";
    this.hide = (item) => item.invisible || !this.isGranted(item);
    this.sort = (a, b) => {
      return this.compareFunc(a, b);
    };
    const configState = this.injector.get(ConfigStateService);
    this.subscription = configState.createOnUpdateStream((state) => state).subscribe(() => this.refresh());
    this.permissionService = injector.get(PermissionService);
    this.othersGroup = injector.get(OTHERS_GROUP);
    this.compareFunc = injector.get(SORT_COMPARE_FUNC);
  }
  isGranted({
    requiredPolicy
  }) {
    return this.permissionService.getGrantedPolicy(requiredPolicy);
  }
  hasChildren(identifier) {
    const node = this.find((item) => item[this.id] === identifier);
    return Boolean(node?.children?.length);
  }
  hasInvisibleChild(identifier) {
    const node = this.find((item) => item[this.id] === identifier);
    return node?.children?.some((child) => child.invisible) || false;
  }
  /* istanbul ignore next */
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  static {
    this.ɵfac = function AbstractNavTreeService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbstractNavTreeService)(ɵɵinject(Injector));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbstractNavTreeService,
      factory: _AbstractNavTreeService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbstractNavTreeService, [{
    type: Injectable
  }], () => [{
    type: Injector
  }], null);
})();
var RoutesService = class _RoutesService extends AbstractNavTreeService {
  hasPathOrChild(item) {
    return Boolean(item.path) || this.hasChildren(item.name);
  }
  get groupedVisible() {
    return this.createGroupedTree(this.visible.filter((item) => this.hasPathOrChild(item)));
  }
  get groupedVisible$() {
    return this.visible$.pipe(map((items) => items.filter((item) => this.hasPathOrChild(item))), map((visible) => this.createGroupedTree(visible)));
  }
  static {
    this.ɵfac = /* @__PURE__ */ (() => {
      let ɵRoutesService_BaseFactory;
      return function RoutesService_Factory(__ngFactoryType__) {
        return (ɵRoutesService_BaseFactory || (ɵRoutesService_BaseFactory = ɵɵgetInheritedFactory(_RoutesService)))(__ngFactoryType__ || _RoutesService);
      };
    })();
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _RoutesService,
      factory: _RoutesService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoutesService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var SubscriptionService = class _SubscriptionService {
  constructor() {
    this.subscription = new Subscription();
  }
  get isClosed() {
    return this.subscription.closed;
  }
  addOne(source$, nextOrObserver, error) {
    const subscription = source$.subscribe(nextOrObserver, error);
    this.subscription.add(subscription);
    return subscription;
  }
  closeAll() {
    this.subscription.unsubscribe();
  }
  closeOne(subscription) {
    this.removeOne(subscription);
    if (subscription) {
      subscription.unsubscribe();
    }
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  removeOne(subscription) {
    if (!subscription) return;
    this.subscription.remove(subscription);
  }
  reset() {
    this.subscription.unsubscribe();
    this.subscription = new Subscription();
  }
  static {
    this.ɵfac = function SubscriptionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SubscriptionService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _SubscriptionService,
      factory: _SubscriptionService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SubscriptionService, [{
    type: Injectable
  }], null, null);
})();
var trackBy = (key) => (_, item) => item[key];
var trackByDeep = (...keys) => (_, item) => keys.reduce((acc, key) => acc[key], item);
var TrackByService = class _TrackByService {
  constructor() {
    this.by = trackBy;
    this.byDeep = trackByDeep;
  }
  static {
    this.ɵfac = function TrackByService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TrackByService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TrackByService,
      factory: _TrackByService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TrackByService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var AbpWindowService = class _AbpWindowService {
  constructor() {
    this.document = inject(DOCUMENT);
    this.window = this.document.defaultView;
    this.navigator = this.window.navigator;
  }
  copyToClipboard(text) {
    return this.navigator.clipboard.writeText(text);
  }
  open(url, target, features) {
    return this.window.open(url, target, features);
  }
  reloadPage() {
    this.window.location.reload();
  }
  downloadBlob(blob, fileName) {
    const blobUrl = this.window.URL.createObjectURL(blob);
    const a = this.document.createElement("a");
    a.style.display = "none";
    a.href = blobUrl;
    a.download = fileName;
    this.document.body.appendChild(a);
    a.dispatchEvent(new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      view: this.window
    }));
    this.window.URL.revokeObjectURL(blobUrl);
    this.document.body.removeChild(a);
  }
  static {
    this.ɵfac = function AbpWindowService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpWindowService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpWindowService,
      factory: _AbpWindowService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpWindowService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var InternetConnectionService = class _InternetConnectionService {
  constructor() {
    this.document = inject(DOCUMENT);
    this.window = this.document.defaultView;
    this.navigator = this.window.navigator;
    this.status$ = new BehaviorSubject(this.navigator.onLine);
    this.status = signal(this.navigator.onLine);
    this.networkStatus = computed(() => this.status());
    this.window.addEventListener("offline", () => this.setStatus(false));
    this.window.addEventListener("online", () => this.setStatus(true));
  }
  setStatus(val) {
    this.status.set(val);
    this.status$.next(val);
  }
  get networkStatus$() {
    return this.status$.asObservable();
  }
  static {
    this.ɵfac = function InternetConnectionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _InternetConnectionService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _InternetConnectionService,
      factory: _InternetConnectionService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InternetConnectionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var LocalStorageListenerService = class _LocalStorageListenerService {
  constructor() {
    this.window = inject(DOCUMENT).defaultView;
    this.window.addEventListener("storage", (event) => {
      if (event.key === "access_token" && event.newValue === null) {
        this.window.location.reload();
      }
    });
  }
  static {
    this.ɵfac = function LocalStorageListenerService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LocalStorageListenerService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _LocalStorageListenerService,
      factory: _LocalStorageListenerService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalStorageListenerService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var AbpTitleStrategy = class _AbpTitleStrategy extends TitleStrategy {
  constructor() {
    super();
    this.title = inject(Title);
    this.localizationService = inject(LocalizationService);
    this.disableProjectName = inject(DISABLE_PROJECT_NAME, {
      optional: true
    });
    this.langugageChange = toSignal(this.localizationService.languageChange$);
    effect(() => {
      if (this.langugageChange()) {
        this.updateTitle(this.routerState);
      }
    });
  }
  updateTitle(routerState) {
    this.routerState = routerState;
    const title = this.buildTitle(routerState);
    const projectName = this.localizationService.instant({
      key: "::AppName",
      defaultValue: "MyProjectName"
    });
    if (!title) {
      return this.title.setTitle(projectName);
    }
    let localizedText = this.localizationService.instant({
      key: title,
      defaultValue: title
    });
    if (!this.disableProjectName) {
      localizedText += ` | ${projectName}`;
    }
    this.title.setTitle(localizedText);
  }
  static {
    this.ɵfac = function AbpTitleStrategy_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpTitleStrategy)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpTitleStrategy,
      factory: _AbpTitleStrategy.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpTitleStrategy, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var AbpApplicationConfigurationService = class _AbpApplicationConfigurationService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.get = (options, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/application-configuration",
      params: {
        includeLocalizationResources: options.includeLocalizationResources
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function AbpApplicationConfigurationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpApplicationConfigurationService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpApplicationConfigurationService,
      factory: _AbpApplicationConfigurationService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApplicationConfigurationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var AbpApplicationLocalizationService = class _AbpApplicationLocalizationService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.get = (input, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/application-localization",
      params: {
        cultureName: input.cultureName,
        onlyDynamics: input.onlyDynamics
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function AbpApplicationLocalizationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpApplicationLocalizationService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpApplicationLocalizationService,
      factory: _AbpApplicationLocalizationService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApplicationLocalizationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var ConfigStateService = class _ConfigStateService {
  setState(config) {
    this.store.set(config);
  }
  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }
  constructor(abpConfigService, abpApplicationLocalizationService, includeLocalizationResources) {
    this.abpConfigService = abpConfigService;
    this.abpApplicationLocalizationService = abpApplicationLocalizationService;
    this.includeLocalizationResources = includeLocalizationResources;
    this.updateSubject = new Subject();
    this.store = new InternalStore({});
    this.initUpdateStream();
  }
  initUpdateStream() {
    this.updateSubject.pipe(switchMap(() => this.abpConfigService.get({
      includeLocalizationResources: !!this.includeLocalizationResources
    }))).pipe(switchMap((appState) => this.getLocalizationAndCombineWithAppState(appState))).subscribe((res) => this.store.set(res));
  }
  getLocalizationAndCombineWithAppState(appState) {
    if (!appState.localization.currentCulture.cultureName) {
      throw new Error("culture name should defined");
    }
    return this.getlocalizationResource(appState.localization.currentCulture.cultureName).pipe(map((result) => __spreadProps(__spreadValues({}, appState), {
      localization: __spreadValues(__spreadValues({}, appState.localization), result)
    })));
  }
  getlocalizationResource(cultureName) {
    return this.abpApplicationLocalizationService.get({
      cultureName,
      onlyDynamics: false
    });
  }
  refreshAppState() {
    this.updateSubject.next();
    return this.createOnUpdateStream((state) => state).pipe(take(1));
  }
  refreshLocalization(lang) {
    if (this.includeLocalizationResources) {
      return this.refreshAppState().pipe(map(() => null));
    }
    return this.getlocalizationResource(lang).pipe(tap((result) => this.store.patch({
      localization: __spreadValues(__spreadValues({}, this.store.state.localization), result)
    }))).pipe(map(() => null));
  }
  getOne$(key) {
    return this.store.sliceState((state) => state[key]);
  }
  getOne(key) {
    return this.store.state[key];
  }
  getAll$() {
    return this.store.sliceState((state) => state);
  }
  getAll() {
    return this.store.state;
  }
  getDeep$(keys) {
    keys = splitKeys(keys);
    return this.store.sliceState((state) => state).pipe(map((state) => {
      return keys.reduce((acc, val) => {
        if (acc) {
          return acc[val];
        }
        return void 0;
      }, state);
    }));
  }
  getDeep(keys) {
    keys = splitKeys(keys);
    return keys.reduce((acc, val) => {
      if (acc) {
        return acc[val];
      }
      return void 0;
    }, this.store.state);
  }
  getFeature(key) {
    return this.store.state.features?.values?.[key];
  }
  getFeature$(key) {
    return this.store.sliceState((state) => state.features?.values?.[key]);
  }
  getFeatures(keys) {
    const {
      features
    } = this.store.state;
    if (!features) return;
    return keys.reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
      [key]: features.values[key]
    }), {});
  }
  getFeatures$(keys) {
    return this.store.sliceState(({
      features
    }) => {
      if (!features?.values) return;
      return keys.reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
        [key]: features.values[key]
      }), {});
    });
  }
  getSetting(key) {
    return this.store.state.setting?.values?.[key];
  }
  getSetting$(key) {
    return this.store.sliceState((state) => state.setting?.values?.[key]);
  }
  getSettings(keyword) {
    const settings = this.store.state.setting?.values || {};
    if (!keyword) return settings;
    const keysFound = Object.keys(settings).filter((key) => key.indexOf(keyword) > -1);
    return keysFound.reduce((acc, key) => {
      acc[key] = settings[key];
      return acc;
    }, {});
  }
  getSettings$(keyword) {
    return this.store.sliceState((state) => state.setting?.values).pipe(map((settings = {}) => {
      if (!keyword) return settings;
      const keysFound = Object.keys(settings).filter((key) => key.indexOf(keyword) > -1);
      return keysFound.reduce((acc, key) => {
        acc[key] = settings[key];
        return acc;
      }, {});
    }));
  }
  getGlobalFeatures() {
    return this.store.state.globalFeatures;
  }
  getGlobalFeatures$() {
    return this.store.sliceState((state) => state.globalFeatures);
  }
  isGlobalFeatureEnabled(key, globalFeatures) {
    const features = globalFeatures.enabledFeatures || [];
    return features.some((f) => key === f);
  }
  getGlobalFeatureIsEnabled(key) {
    return this.isGlobalFeatureEnabled(key, this.store.state.globalFeatures);
  }
  getGlobalFeatureIsEnabled$(key) {
    return this.store.sliceState((state) => this.isGlobalFeatureEnabled(key, state.globalFeatures));
  }
  static {
    this.ɵfac = function ConfigStateService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ConfigStateService)(ɵɵinject(AbpApplicationConfigurationService), ɵɵinject(AbpApplicationLocalizationService), ɵɵinject(INCUDE_LOCALIZATION_RESOURCES_TOKEN, 8));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ConfigStateService,
      factory: _ConfigStateService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ConfigStateService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: AbpApplicationConfigurationService
  }, {
    type: AbpApplicationLocalizationService
  }, {
    type: void 0,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [INCUDE_LOCALIZATION_RESOURCES_TOKEN]
    }]
  }], null);
})();
function splitKeys(keys) {
  if (typeof keys === "string") {
    keys = keys.split(".");
  }
  if (!Array.isArray(keys)) {
    throw new Error("The argument must be a dot string or an string array.");
  }
  return keys;
}
var LocalizationService = class _LocalizationService {
  /**
   * Returns currently selected language
   * Even though this looks like it's redundant to return the same value as `getLanguage()`,
   * it's actually not. This could be invoked any time, and the latestLang could be different from the
   * sessionState.getLanguage() value.
   */
  get currentLang() {
    return this.latestLang || this.sessionState.getLanguage();
  }
  get currentLang$() {
    return this.sessionState.getLanguage$();
  }
  get languageChange$() {
    return this._languageChange$.asObservable();
  }
  constructor(sessionState, injector, otherInstance, configState) {
    this.sessionState = sessionState;
    this.injector = injector;
    this.configState = configState;
    this.latestLang = this.sessionState.getLanguage();
    this._languageChange$ = new Subject();
    this.uiLocalizations$ = new BehaviorSubject(/* @__PURE__ */ new Map());
    this.localizations$ = new BehaviorSubject(/* @__PURE__ */ new Map());
    if (otherInstance) throw new Error("LocalizationService should have only one instance.");
    this.listenToSetLanguage();
    this.initLocalizationValues();
  }
  initLocalizationValues() {
    localizations$.subscribe((val) => this.addLocalization(val));
    const legacyResources$ = this.configState.getDeep$("localization.values");
    const remoteLocalizations$ = this.configState.getDeep$("localization.resources");
    const currentLanguage$ = this.sessionState.getLanguage$();
    const uiLocalizations$ = combineLatest([currentLanguage$, this.uiLocalizations$]).pipe(map(([currentLang, localizations]) => localizations.get(currentLang)));
    combineLatest([legacyResources$, remoteLocalizations$, uiLocalizations$]).pipe(map(([legacy, resource, local]) => {
      if (!resource) {
        return;
      }
      const remote = combineLegacyandNewResources(legacy || {}, resource);
      if (remote) {
        if (!local) {
          local = /* @__PURE__ */ new Map();
        }
        Object.entries(remote).forEach((entry) => {
          const resourceName = entry[0];
          const remoteTexts = entry[1];
          let resource2 = local?.get(resourceName) || {};
          resource2 = __spreadValues(__spreadValues({}, resource2), remoteTexts);
          local?.set(resourceName, resource2);
        });
      }
      return local;
    }), filter(Boolean)).subscribe((val) => this.localizations$.next(val));
  }
  addLocalization(localizations) {
    if (!localizations) return;
    const localizationMap = this.uiLocalizations$.value;
    localizations.forEach((loc) => {
      const cultureMap = localizationMap.get(loc.culture) || /* @__PURE__ */ new Map();
      loc.resources.forEach((res) => {
        let resource = cultureMap.get(res.resourceName) || {};
        resource = __spreadValues(__spreadValues({}, resource), res.texts);
        cultureMap.set(res.resourceName, resource);
      });
      localizationMap.set(loc.culture, cultureMap);
    });
    this.uiLocalizations$.next(localizationMap);
  }
  listenToSetLanguage() {
    this.sessionState.onLanguageChange$().pipe(filter((lang) => this.configState.getDeep("localization.currentCulture.cultureName") !== lang), switchMap((lang) => this.configState.refreshLocalization(lang).pipe(map(() => lang))), filter(Boolean), switchMap((lang) => from(this.registerLocale(lang).then(() => lang)))).subscribe((lang) => this._languageChange$.next(lang));
  }
  registerLocale(locale) {
    const {
      registerLocaleFn
    } = this.injector.get(CORE_OPTIONS);
    return registerLocaleFn(locale).then((module) => {
      if (module?.default) registerLocaleData(module.default);
      this.latestLang = locale;
    });
  }
  /**
   * Returns an observable localized text with the given interpolation parameters in current language.
   * @param key Localizaton key to replace with localized text
   * @param interpolateParams Values to interpolate
   */
  get(key, ...interpolateParams) {
    return this.configState.getAll$().pipe(map((state) => this.getLocalization(state, key, ...interpolateParams)));
  }
  getResource(resourceName) {
    return this.localizations$.value.get(resourceName);
  }
  getResource$(resourceName) {
    return this.localizations$.pipe(map((res) => res.get(resourceName)));
  }
  /**
   * Returns localized text with the given interpolation parameters in current language.
   * @param key Localization key to replace with localized text
   * @param interpolateParams Values to intepolate.
   */
  instant(key, ...interpolateParams) {
    return this.getLocalization(this.configState.getAll(), key, ...interpolateParams);
  }
  localize(resourceName, key, defaultValue) {
    return this.configState.getOne$("localization").pipe(map(createLocalizer), map((localize) => localize(resourceName, key, defaultValue)));
  }
  localizeSync(resourceName, key, defaultValue) {
    const localization = this.configState.getOne("localization");
    return createLocalizer(localization)(resourceName, key, defaultValue);
  }
  localizeWithFallback(resourceNames, keys, defaultValue) {
    return this.configState.getOne$("localization").pipe(map(createLocalizerWithFallback), map((localizeWithFallback) => localizeWithFallback(resourceNames, keys, defaultValue)));
  }
  localizeWithFallbackSync(resourceNames, keys, defaultValue) {
    const localization = this.configState.getOne("localization");
    return createLocalizerWithFallback(localization)(resourceNames, keys, defaultValue);
  }
  getLocalization(state, key, ...interpolateParams) {
    let defaultValue = "";
    if (!key) {
      return defaultValue;
    }
    if (typeof key !== "string") {
      defaultValue = key.defaultValue;
      key = key.key;
    }
    const keys = key.split("::");
    const warn = (message) => {
      if (isDevMode()) console.warn(message);
    };
    if (keys.length < 2) {
      warn("The localization source separator (::) not found.");
      return defaultValue || key;
    }
    if (!state.localization) return defaultValue || keys[1];
    const sourceName = keys[0] || state.localization.defaultResourceName;
    const sourceKey = keys[1];
    if (sourceName === "_") {
      return defaultValue || sourceKey;
    }
    if (!sourceName) {
      warn("Localization source name is not specified and the defaultResourceName was not defined!");
      return defaultValue || sourceKey;
    }
    const source = this.localizations$.value.get(sourceName);
    if (!source) {
      warn("Could not find localization source: " + sourceName);
      return defaultValue || sourceKey;
    }
    let localization = source[sourceKey];
    if (typeof localization === "undefined") {
      return defaultValue || sourceKey;
    }
    interpolateParams = interpolateParams.filter((params) => params != null);
    if (localization) localization = interpolate(localization, interpolateParams);
    if (typeof localization !== "string") localization = "";
    return localization || defaultValue || key;
  }
  static {
    this.ɵfac = function LocalizationService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LocalizationService)(ɵɵinject(SessionStateService), ɵɵinject(Injector), ɵɵinject(_LocalizationService, 12), ɵɵinject(ConfigStateService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _LocalizationService,
      factory: _LocalizationService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: SessionStateService
  }, {
    type: Injector
  }, {
    type: LocalizationService,
    decorators: [{
      type: Optional
    }, {
      type: SkipSelf
    }]
  }, {
    type: ConfigStateService
  }], null);
})();
function recursivelyMergeBaseResources(baseResourceName, source) {
  const item = source[baseResourceName];
  if (item.baseResources.length === 0) {
    return item;
  }
  return item.baseResources.reduce((acc, baseResource) => {
    const baseItem = recursivelyMergeBaseResources(baseResource, source);
    const texts = __spreadValues(__spreadValues({}, baseItem.texts), item.texts);
    return __spreadProps(__spreadValues({}, acc), {
      texts
    });
  }, item);
}
function mergeResourcesWithBaseResource(resource) {
  const entities = Object.keys(resource).map((key) => {
    const newValue = recursivelyMergeBaseResources(key, resource);
    return [key, newValue];
  });
  return entities.reduce((acc, [key, value]) => __spreadProps(__spreadValues({}, acc), {
    [key]: value
  }), {});
}
function combineLegacyandNewResources(legacy, resource) {
  const mergedResource = mergeResourcesWithBaseResource(resource);
  return Object.entries(mergedResource).reduce((acc, [key, value]) => {
    return __spreadProps(__spreadValues({}, acc), {
      [key]: value.texts
    });
  }, legacy);
}
var DynamicLayoutComponent = class _DynamicLayoutComponent {
  constructor(dynamicLayoutComponent) {
    this.layouts = inject(DYNAMIC_LAYOUTS_TOKEN);
    this.isLayoutVisible = true;
    this.router = inject(Router);
    this.route = inject(ActivatedRoute);
    this.routes = inject(RoutesService);
    this.localizationService = inject(LocalizationService);
    this.replaceableComponents = inject(ReplaceableComponentsService);
    this.subscription = inject(SubscriptionService);
    this.routerEvents = inject(RouterEvents);
    this.environment = inject(EnvironmentService);
    if (dynamicLayoutComponent) {
      if (isDevMode()) console.warn("DynamicLayoutComponent must be used only in AppComponent.");
      return;
    }
    this.checkLayoutOnNavigationEnd();
    this.listenToLanguageChange();
  }
  ngOnInit() {
    if (this.layout) {
      return;
    }
    const {
      oAuthConfig
    } = this.environment.getEnvironment();
    if (oAuthConfig.responseType === "code") {
      this.getLayout();
    }
  }
  checkLayoutOnNavigationEnd() {
    const navigationEnd$ = this.routerEvents.getNavigationEvents("End");
    this.subscription.addOne(navigationEnd$, () => this.getLayout());
  }
  getLayout() {
    let expectedLayout = this.getExtractedLayout();
    if (!expectedLayout) expectedLayout = "empty";
    if (this.layoutKey === expectedLayout) return;
    const key = this.layouts.get(expectedLayout);
    if (key) {
      this.layout = this.getComponent(key)?.component;
      this.layoutKey = expectedLayout;
    }
    if (!this.layout) {
      this.showLayoutNotFoundError(expectedLayout);
    }
  }
  getExtractedLayout() {
    const routeData = this.route.snapshot.data || {};
    let expectedLayout = routeData["layout"];
    let node = findRoute(this.routes, getRoutePath(this.router));
    node = {
      parent: node
    };
    while (node.parent) {
      node = node.parent;
      if (node.layout) {
        expectedLayout = node.layout;
        break;
      }
    }
    return expectedLayout;
  }
  showLayoutNotFoundError(layoutName) {
    let message = `Layout ${layoutName} not found.`;
    if (layoutName === "account") {
      message = 'Account layout not found. Please check your configuration. If you are using LeptonX, please make sure you have added "AccountLayoutModule.forRoot()" to your app.module configuration.';
    }
    console.warn(message);
  }
  listenToLanguageChange() {
    this.subscription.addOne(this.localizationService.languageChange$, () => {
      this.isLayoutVisible = false;
      setTimeout(() => this.isLayoutVisible = true, 0);
    });
  }
  getComponent(key) {
    return this.replaceableComponents.get(key);
  }
  static {
    this.ɵfac = function DynamicLayoutComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _DynamicLayoutComponent)(ɵɵdirectiveInject(_DynamicLayoutComponent, 12));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _DynamicLayoutComponent,
      selectors: [["abp-dynamic-layout"]],
      features: [ɵɵProvidersFeature([SubscriptionService])],
      decls: 1,
      vars: 1,
      consts: [[3, "ngComponentOutlet"]],
      template: function DynamicLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, DynamicLayoutComponent_Conditional_0_Template, 1, 1, "ng-container", 0);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.isLayoutVisible ? 0 : -1);
        }
      },
      dependencies: [NgComponentOutlet],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DynamicLayoutComponent, [{
    type: Component,
    args: [{
      selector: "abp-dynamic-layout",
      template: `
    @if (isLayoutVisible) {
      <ng-container [ngComponentOutlet]="layout"></ng-container>
    }
  `,
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: DynamicLayoutComponent,
    decorators: [{
      type: Optional
    }, {
      type: SkipSelf
    }]
  }], null);
})();
var ReplaceableRouteContainerComponent = class _ReplaceableRouteContainerComponent {
  constructor(route, replaceableComponents, subscription) {
    this.route = route;
    this.replaceableComponents = replaceableComponents;
    this.subscription = subscription;
  }
  ngOnInit() {
    this.defaultComponent = this.route.snapshot.data.replaceableComponent.defaultComponent;
    this.componentKey = this.route.snapshot.data.replaceableComponent.key;
    const component$ = this.replaceableComponents.get$(this.componentKey).pipe(distinctUntilChanged());
    this.subscription.addOne(component$, (res = {}) => {
      this.externalComponent = res.component;
    });
  }
  static {
    this.ɵfac = function ReplaceableRouteContainerComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ReplaceableRouteContainerComponent)(ɵɵdirectiveInject(ActivatedRoute), ɵɵdirectiveInject(ReplaceableComponentsService), ɵɵdirectiveInject(SubscriptionService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ReplaceableRouteContainerComponent,
      selectors: [["abp-replaceable-route-container"]],
      features: [ɵɵProvidersFeature([SubscriptionService])],
      decls: 1,
      vars: 1,
      consts: [[4, "ngComponentOutlet"]],
      template: function ReplaceableRouteContainerComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, ReplaceableRouteContainerComponent_ng_container_0_Template, 1, 0, "ng-container", 0);
        }
        if (rf & 2) {
          ɵɵproperty("ngComponentOutlet", ctx.externalComponent || ctx.defaultComponent);
        }
      },
      dependencies: [NgComponentOutlet],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableRouteContainerComponent, [{
    type: Component,
    args: [{
      selector: "abp-replaceable-route-container",
      template: `
    <ng-container *ngComponentOutlet="externalComponent || defaultComponent"></ng-container>
  `,
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ActivatedRoute
  }, {
    type: ReplaceableComponentsService
  }, {
    type: SubscriptionService
  }], null);
})();
var RouterOutletComponent = class _RouterOutletComponent {
  static {
    this.ɵfac = function RouterOutletComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RouterOutletComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _RouterOutletComponent,
      selectors: [["abp-router-outlet"]],
      decls: 1,
      vars: 0,
      template: function RouterOutletComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelement(0, "router-outlet");
        }
      },
      dependencies: [RouterOutlet],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RouterOutletComponent, [{
    type: Component,
    args: [{
      selector: "abp-router-outlet",
      template: ` <router-outlet></router-outlet> `
    }]
  }], null, null);
})();
var differentLocales = {
  aa: "en",
  "aa-DJ": "en",
  "aa-ER": "en",
  "aa-ET": "en",
  "af-ZA": "af",
  "agq-CM": "agq",
  "ak-GH": "ak",
  "am-ET": "am",
  "ar-001": "ar",
  arn: "en",
  "arn-CL": "en",
  "as-IN": "as",
  "asa-TZ": "asa",
  "ast-ES": "ast",
  "az-Cyrl-AZ": "az-Cyrl",
  "az-Latn-AZ": "az-Latn",
  ba: "ru",
  "ba-RU": "ru",
  "bas-CM": "bas",
  "be-BY": "be",
  "bem-ZM": "bem",
  "bez-TZ": "bez",
  "bg-BG": "bg",
  bin: "en",
  "bin-NG": "en",
  "bm-Latn": "bm",
  "bm-Latn-ML": "bm",
  "bn-BD": "bn",
  "bo-CN": "bo",
  "br-FR": "br",
  "brx-IN": "brx",
  "bs-Cyrl-BA": "bs-Cyrl",
  "bs-Latn-BA": "bs-Latn",
  byn: "en",
  "byn-ER": "en",
  "ca-ES": "ca",
  "ca-ES-valencia": "ca-ES-VALENCIA",
  "ce-RU": "ce",
  "cgg-UG": "cgg",
  "chr-Cher": "chr",
  "chr-Cher-US": "chr",
  co: "en",
  "co-FR": "fr",
  "cs-CZ": "cs",
  "cu-RU": "cu",
  "cy-GB": "cy",
  "da-DK": "da",
  "dav-KE": "dav",
  "de-DE": "de",
  "dje-NE": "dje",
  "dsb-DE": "dsb",
  "dua-CM": "dua",
  dv: "en",
  "dv-MV": "en",
  "dyo-SN": "dyo",
  "dz-BT": "dz",
  "ebu-KE": "ebu",
  "ee-GH": "ee",
  "el-GR": "el",
  "en-029": "en",
  "en-ID": "en",
  "en-US": "en",
  "eo-001": "en",
  "es-ES": "es",
  "et-EE": "et",
  "eu-ES": "eu",
  "ewo-CM": "ewo",
  "fa-IR": "fa",
  "ff-Latn-SN": "ff-Latn",
  "ff-NG": "ff",
  "fi-FI": "fi",
  "fil-PH": "fil",
  "fo-FO": "fo",
  "fr-029": "fr",
  "fr-FR": "fr",
  "fur-IT": "fur",
  "fy-NL": "fy",
  "ga-IE": "ga",
  "gd-GB": "gd",
  "gl-ES": "gl",
  gn: "en",
  "gn-PY": "en",
  "gsw-CH": "gsw",
  "gu-IN": "gu",
  "guz-KE": "guz",
  "gv-IM": "gv",
  "ha-Latn": "ha",
  "ha-Latn-GH": "ha-GH",
  "ha-Latn-NE": "ha-NE",
  "ha-Latn-NG": "ha",
  "haw-US": "haw",
  "he-IL": "he",
  "hi-IN": "hi",
  "hr-HR": "hr",
  "hsb-DE": "hsb",
  "hu-HU": "hu",
  "hy-AM": "hy",
  "ia-001": "ia",
  "ia-FR": "ia",
  ibb: "en",
  "ibb-NG": "en",
  "id-ID": "id",
  "ig-NG": "ig",
  "ii-CN": "ii",
  "is-IS": "is",
  "it-IT": "it",
  iu: "en",
  "iu-Cans": "en",
  "iu-Cans-CA": "en",
  "iu-Latn": "en",
  "iu-Latn-CA": "en",
  "ja-JP": "ja",
  "jgo-CM": "jgo",
  "jmc-TZ": "jmc",
  "jv-Java": "jv",
  "jv-Java-ID": "jv",
  "jv-Latn": "jv",
  "jv-Latn-ID": "jv",
  "ka-GE": "ka",
  "kab-DZ": "kab",
  "kam-KE": "kam",
  "kde-TZ": "kde",
  "kea-CV": "kea",
  "khq-ML": "khq",
  "ki-KE": "ki",
  "kk-KZ": "kk",
  "kkj-CM": "kkj",
  "kl-GL": "kl",
  "kln-KE": "kln",
  "km-KH": "km",
  "kn-IN": "kn",
  "ko-KR": "ko",
  "kok-IN": "kok",
  kr: "en",
  "kr-NG": "en",
  "ks-Arab": "ks",
  "ks-Arab-IN": "ks",
  "ks-Deva": "ks",
  "ks-Deva-IN": "ks",
  "ksb-TZ": "ksb",
  "ksf-CM": "ksf",
  "ksh-DE": "ksh",
  "ku-Arab": "ku",
  "ku-Arab-IQ": "ku",
  "ku-Arab-IR": "ku",
  "kw-GB": "kw",
  "ky-KG": "ky",
  la: "en",
  "la-001": "en",
  "lag-TZ": "lag",
  "lb-LU": "lb",
  "lg-UG": "lg",
  "lkt-US": "lkt",
  "ln-CD": "ln",
  "lo-LA": "lo",
  "lrc-IR": "lrc",
  "lt-LT": "lt",
  "lu-CD": "lu",
  "luo-KE": "luo",
  "luy-KE": "luy",
  "lv-LV": "lv",
  "mas-KE": "mas",
  "mer-KE": "mer",
  "mfe-MU": "mfe",
  "mg-MG": "mg",
  "mgh-MZ": "mgh",
  "mgo-CM": "mgo",
  "mi-NZ": "mi",
  "mk-MK": "mk",
  "ml-IN": "ml",
  "mn-Cyrl": "mn",
  "mn-MN": "mn",
  "mn-Mong": "mn",
  "mn-Mong-CN": "mn",
  "mn-Mong-MN": "mn",
  mni: "en",
  "mni-IN": "en",
  moh: "en",
  "moh-CA": "en",
  "mr-IN": "mr",
  "ms-MY": "ms",
  "mt-MT": "mt",
  "mua-CM": "mua",
  "my-MM": "my",
  "mzn-IR": "mzn",
  "naq-NA": "naq",
  "nb-NO": "nb",
  "nd-ZW": "nd",
  "ne-NP": "ne",
  "nl-NL": "nl",
  "nmg-CM": "ngm",
  "nn-NO": "nn",
  "nnh-CM": "nnh",
  no: "en",
  nqo: "en",
  "nqo-GN": "en",
  nr: "en",
  "nr-ZA": "en",
  nso: "en",
  "nso-ZA": "en",
  "nus-SS": "nus",
  "nyn-UG": "nyn",
  oc: "en",
  "oc-FR": "fr",
  "om-ET": "om",
  "or-IN": "or",
  "os-GE": "os",
  "pa-Arab-PK": "pa-Arab",
  "pa-IN": "pa",
  pap: "en",
  "pap-029": "en",
  "pl-PL": "pl",
  "prg-001": "prg",
  prs: "en",
  "prs-AF": "en",
  "ps-AF": "ps",
  "pt-BR": "pt",
  quc: "en",
  "quc-Latn": "en",
  "quc-Latn-GT": "en",
  quz: "en",
  "quz-BO": "en",
  "quz-EC": "en",
  "quz-PE": "en",
  "rm-CH": "rm",
  "rn-BI": "rn",
  "ro-RO": "ro",
  "rof-TZ": "rof",
  "ru-RU": "ru",
  "rw-RW": "rw",
  "rwk-TZ": "rwk",
  sa: "en",
  "sa-IN": "en",
  "sah-RU": "sah",
  "saq-KE": "saq",
  "sbp-TZ": "en",
  "sd-Arab": "sd",
  "sd-Arab-PK": "sd",
  "sd-Deva": "sd",
  "sd-Deva-IN": "sd",
  "se-NO": "se",
  "seh-MZ": "seh",
  "ses-ML": "ses",
  "sg-CF": "sg",
  "shi-Latn-MA": "shi-Latn",
  "shi-Tfng-MA": "shi-Tfng",
  "si-LK": "si",
  "sk-SK": "sk",
  "sl-SI": "sl",
  sma: "en",
  "sma-NO": "en",
  "sma-SE": "en",
  smj: "en",
  "smj-NO": "en",
  "smj-SE": "en",
  "smn-FI": "en",
  sms: "en",
  "sms-FI": "en",
  "sn-Latn": "sn",
  "sn-Latn-ZW": "sn",
  "so-SO": "so",
  "sq-AL": "so",
  "sr-Cyrl-RS": "sr-Cryl",
  "sr-Latn-RS": "sr-Latn",
  ss: "en",
  "ss-SZ": "en",
  "ss-ZA": "en",
  ssy: "en",
  "ssy-ER": "en",
  st: "en",
  "st-LS": "en",
  "st-ZA": "en",
  "sv-SE": "sv",
  "sw-TZ": "sw",
  syr: "en",
  "syr-SY": "en",
  "ta-IN": "ta",
  "te-IN": "te",
  "teo-UG": "teo",
  "tg-Cyrl": "tg",
  "tg-Cyrl-TJ": "tg",
  "th-TH": "th",
  "ti-ET": "ti",
  tig: "en",
  "tig-ER": "en",
  "tk-TM": "tk",
  tn: "en",
  "tn-BW": "en",
  "tn-ZA": "en",
  "to-TO": "to",
  "tr-TR": "tr",
  ts: "en",
  "ts-ZA": "en",
  "tt-RU": "tt",
  "twq-NE": "twq",
  "tzm-Arab": "tzm",
  "tzm-Arab-MA": "tzm",
  "tzm-Latn": "tzm",
  "tzm-Latn-DZ": "tzm",
  "tzm-Latn-MA": "tzm",
  "tzm-Tfng": "tzm",
  "tzm-Tfng-MA": "tzm",
  "ug-CN": "ug",
  "uk-UA": "uk",
  "ur-PK": "ur",
  "uz-Arab-AF": "uz-Arab",
  "uz-Cyrl-UZ": "uz-Cyrl",
  "uz-Latn-UZ": "uz-Latn",
  "vai-Latn-LR": "vai-Latn",
  "vai-Vaii-LR": "vai-Vaii",
  ve: "en",
  "ve-ZA": "en",
  "vi-VN": "vi",
  "vo-001": "vo",
  "vun-TZ": "vun",
  "wae-CH": "wae",
  wal: "en",
  "wal-ET": "en",
  "wo-SN": "wo",
  "xh-ZA": "xh",
  "xog-UG": "xog",
  "yav-CM": "yav",
  "yi-001": "yi",
  "yo-NG": "yo",
  "zgh-Tfng": "zgh",
  "zgh-Tfng-MA": "zgh",
  "zh-CN": "zh",
  "zh-HK": "zh",
  "zh-MO": "zh",
  "zh-SG": "zh",
  "zh-TW": "zh",
  "zu-ZA": "zu"
};
var DEFAULT_DYNAMIC_LAYOUTS = /* @__PURE__ */ new Map([[
  "application",
  "Theme.ApplicationLayoutComponent"
  /* eThemeSharedComponents.ApplicationLayoutComponent */
], [
  "account",
  "Theme.AccountLayoutComponent"
  /* eThemeSharedComponents.AccountLayoutComponent */
], [
  "empty",
  "Theme.EmptyLayoutComponent"
  /* eThemeSharedComponents.EmptyLayoutComponent */
]]);
var AutofocusDirective = class _AutofocusDirective {
  set delay(val) {
    this._delay = Number(val) || 0;
  }
  get delay() {
    return this._delay;
  }
  constructor(elRef) {
    this.elRef = elRef;
    this._delay = 0;
  }
  ngAfterViewInit() {
    setTimeout(() => this.elRef.nativeElement.focus(), this.delay);
  }
  static {
    this.ɵfac = function AutofocusDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AutofocusDirective)(ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _AutofocusDirective,
      selectors: [["", "autofocus", ""]],
      inputs: {
        delay: [0, "autofocus", "delay"]
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AutofocusDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[autofocus]"
    }]
  }], () => [{
    type: ElementRef
  }], {
    delay: [{
      type: Input,
      args: ["autofocus"]
    }]
  });
})();
var InputEventDebounceDirective = class _InputEventDebounceDirective {
  constructor(el, subscription) {
    this.el = el;
    this.subscription = subscription;
    this.debounce = 300;
    this.debounceEvent = new EventEmitter();
  }
  ngOnInit() {
    const input$ = fromEvent(this.el.nativeElement, "input").pipe(debounceTime(this.debounce));
    this.subscription.addOne(input$, (event) => {
      this.debounceEvent.emit(event);
    });
  }
  static {
    this.ɵfac = function InputEventDebounceDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _InputEventDebounceDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(SubscriptionService));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _InputEventDebounceDirective,
      selectors: [["", "input.debounce", ""]],
      inputs: {
        debounce: "debounce"
      },
      outputs: {
        debounceEvent: "input.debounce"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([SubscriptionService])]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InputEventDebounceDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[input.debounce]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: SubscriptionService
  }], {
    debounce: [{
      type: Input
    }],
    debounceEvent: [{
      type: Output,
      args: ["input.debounce"]
    }]
  });
})();
var AbpForContext = class {
  constructor($implicit, index2, count, list) {
    this.$implicit = $implicit;
    this.index = index2;
    this.count = count;
    this.list = list;
  }
};
var RecordView = class {
  constructor(record, view) {
    this.record = record;
    this.view = view;
  }
};
var ForDirective = class _ForDirective {
  get compareFn() {
    return this.compareBy || collectionCompare;
  }
  get trackByFn() {
    return this.trackBy || ((index2, item) => item.id || index2);
  }
  constructor(tempRef, vcRef, differs) {
    this.tempRef = tempRef;
    this.vcRef = vcRef;
    this.differs = differs;
  }
  iterateOverAppliedOperations(changes) {
    const rw = [];
    changes.forEachOperation((record, previousIndex, currentIndex) => {
      if (record.previousIndex == null) {
        const view = this.vcRef.createEmbeddedView(this.tempRef, new AbpForContext(null, -1, -1, this.items), currentIndex || 0);
        rw.push(new RecordView(record, view));
      } else if (currentIndex == null && previousIndex !== null) {
        this.vcRef.remove(previousIndex);
      } else {
        if (previousIndex !== null) {
          const view = this.vcRef.get(previousIndex);
          if (view && currentIndex !== null) {
            this.vcRef.move(view, currentIndex);
            rw.push(new RecordView(record, view));
          }
        }
      }
    });
    for (let i = 0, l = rw.length; i < l; i++) {
      rw[i].view.context.$implicit = rw[i].record.item;
    }
  }
  iterateOverAttachedViews(changes) {
    for (let i = 0, l = this.vcRef.length; i < l; i++) {
      const viewRef = this.vcRef.get(i);
      viewRef.context.index = i;
      viewRef.context.count = l;
      viewRef.context.list = this.items;
    }
    changes.forEachIdentityChange((record) => {
      if (record.currentIndex !== null) {
        const viewRef = this.vcRef.get(record.currentIndex);
        viewRef.context.$implicit = record.item;
      }
    });
  }
  projectItems(items) {
    if (!items.length && this.emptyRef) {
      this.vcRef.clear();
      this.vcRef.createEmbeddedView(this.emptyRef).rootNodes;
      this.isShowEmptyRef = true;
      this.differ = null;
      return;
    }
    if (this.emptyRef && this.isShowEmptyRef) {
      this.vcRef.clear();
      this.isShowEmptyRef = false;
    }
    if (!this.differ && items) {
      this.differ = this.differs.find(items).create(this.trackByFn);
    }
    if (this.differ) {
      const changes = this.differ.diff(items);
      if (changes) {
        this.iterateOverAppliedOperations(changes);
        this.iterateOverAttachedViews(changes);
      }
    }
  }
  sortItems(items) {
    const orderBy = this.orderBy;
    if (orderBy) {
      items.sort((a, b) => a[orderBy] > b[orderBy] ? 1 : a[orderBy] < b[orderBy] ? -1 : 0);
    } else {
      items.sort();
    }
  }
  ngOnChanges() {
    let items = collectionClone(this.items);
    if (!Array.isArray(items)) return;
    const compareFn = this.compareFn;
    const filterBy = this.filterBy;
    if (typeof filterBy !== "undefined" && typeof this.filterVal !== "undefined" && this.filterVal !== "") {
      items = items.filter((item) => compareFn(item[filterBy], this.filterVal));
    }
    switch (this.orderDir) {
      case "ASC":
        this.sortItems(items);
        this.projectItems(items);
        break;
      case "DESC":
        this.sortItems(items);
        items.reverse();
        this.projectItems(items);
        break;
      default:
        this.projectItems(items);
    }
  }
  static {
    this.ɵfac = function ForDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ForDirective)(ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(IterableDiffers));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _ForDirective,
      selectors: [["", "abpFor", ""]],
      inputs: {
        items: [0, "abpForOf", "items"],
        orderBy: [0, "abpForOrderBy", "orderBy"],
        orderDir: [0, "abpForOrderDir", "orderDir"],
        filterBy: [0, "abpForFilterBy", "filterBy"],
        filterVal: [0, "abpForFilterVal", "filterVal"],
        trackBy: [0, "abpForTrackBy", "trackBy"],
        compareBy: [0, "abpForCompareBy", "compareBy"],
        emptyRef: [0, "abpForEmptyRef", "emptyRef"]
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ForDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpFor]"
    }]
  }], () => [{
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }, {
    type: IterableDiffers
  }], {
    items: [{
      type: Input,
      args: ["abpForOf"]
    }],
    orderBy: [{
      type: Input,
      args: ["abpForOrderBy"]
    }],
    orderDir: [{
      type: Input,
      args: ["abpForOrderDir"]
    }],
    filterBy: [{
      type: Input,
      args: ["abpForFilterBy"]
    }],
    filterVal: [{
      type: Input,
      args: ["abpForFilterVal"]
    }],
    trackBy: [{
      type: Input,
      args: ["abpForTrackBy"]
    }],
    compareBy: [{
      type: Input,
      args: ["abpForCompareBy"]
    }],
    emptyRef: [{
      type: Input,
      args: ["abpForEmptyRef"]
    }]
  });
})();
var FormSubmitDirective = class _FormSubmitDirective {
  constructor(formGroupDirective, host, cdRef, subscription) {
    this.formGroupDirective = formGroupDirective;
    this.host = host;
    this.cdRef = cdRef;
    this.subscription = subscription;
    this.debounce = 200;
    this.markAsDirtyWhenSubmit = true;
    this.ngSubmit = new EventEmitter();
    this.executedNgSubmit = false;
  }
  ngOnInit() {
    this.subscription.addOne(this.formGroupDirective.ngSubmit, () => {
      if (this.markAsDirtyWhenSubmit) {
        this.markAsDirty();
      }
      this.executedNgSubmit = true;
    });
    const keyup$ = fromEvent(this.host.nativeElement, "keyup").pipe(debounceTime(this.debounce), filter((event) => !(event.target instanceof HTMLTextAreaElement)), filter((event) => event && event.key === "Enter"));
    this.subscription.addOne(keyup$, () => {
      if (!this.executedNgSubmit) {
        this.host.nativeElement.dispatchEvent(new Event("submit", {
          bubbles: true,
          cancelable: true
        }));
      }
      this.executedNgSubmit = false;
    });
  }
  markAsDirty() {
    const {
      form
    } = this.formGroupDirective;
    setDirty(form.controls);
    form.markAsDirty();
    this.cdRef.detectChanges();
  }
  static {
    this.ɵfac = function FormSubmitDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _FormSubmitDirective)(ɵɵdirectiveInject(FormGroupDirective, 2), ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(SubscriptionService));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _FormSubmitDirective,
      selectors: [["form", "ngSubmit", "", "formGroup", ""]],
      inputs: {
        debounce: "debounce",
        notValidateOnSubmit: "notValidateOnSubmit",
        markAsDirtyWhenSubmit: "markAsDirtyWhenSubmit"
      },
      outputs: {
        ngSubmit: "ngSubmit"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([SubscriptionService])]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormSubmitDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "form[ngSubmit][formGroup]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: FormGroupDirective,
    decorators: [{
      type: Self
    }]
  }, {
    type: ElementRef
  }, {
    type: ChangeDetectorRef
  }, {
    type: SubscriptionService
  }], {
    debounce: [{
      type: Input
    }],
    notValidateOnSubmit: [{
      type: Input
    }],
    markAsDirtyWhenSubmit: [{
      type: Input
    }],
    ngSubmit: [{
      type: Output
    }]
  });
})();
function setDirty(controls) {
  if (Array.isArray(controls)) {
    controls.forEach((group) => {
      setDirty(group.controls);
    });
    return;
  }
  Object.keys(controls).forEach((key) => {
    controls[key].markAsDirty();
    controls[key].updateValueAndValidity();
  });
}
var InitDirective = class _InitDirective {
  constructor(elRef) {
    this.elRef = elRef;
    this.init = new EventEmitter();
  }
  ngAfterViewInit() {
    this.init.emit(this.elRef);
  }
  static {
    this.ɵfac = function InitDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _InitDirective)(ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _InitDirective,
      selectors: [["", "abpInit", ""]],
      outputs: {
        init: "abpInit"
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InitDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpInit]"
    }]
  }], () => [{
    type: ElementRef
  }], {
    init: [{
      type: Output,
      args: ["abpInit"]
    }]
  });
})();
var PermissionDirective = class _PermissionDirective {
  constructor(templateRef, vcRef, permissionService, cdRef, queue) {
    this.templateRef = templateRef;
    this.vcRef = vcRef;
    this.permissionService = permissionService;
    this.cdRef = cdRef;
    this.queue = queue;
    this.runChangeDetection = true;
    this.cdrSubject = new ReplaySubject();
    this.rendered = false;
  }
  check() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription = this.permissionService.getGrantedPolicy$(this.condition || "").pipe(distinctUntilChanged()).subscribe((isGranted) => {
      this.vcRef.clear();
      if (isGranted) this.vcRef.createEmbeddedView(this.templateRef);
      if (this.runChangeDetection) {
        if (!this.rendered) {
          this.cdrSubject.next();
        } else {
          this.cdRef.detectChanges();
        }
      } else {
        this.cdRef.markForCheck();
      }
    });
  }
  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe();
  }
  ngOnChanges() {
    this.check();
  }
  ngAfterViewInit() {
    this.cdrSubject.pipe(take(1)).subscribe(() => this.queue.add(() => this.cdRef.detectChanges()));
    this.rendered = true;
  }
  static {
    this.ɵfac = function PermissionDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PermissionDirective)(ɵɵdirectiveInject(TemplateRef, 8), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(PermissionService), ɵɵdirectiveInject(ChangeDetectorRef), ɵɵdirectiveInject(QUEUE_MANAGER));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _PermissionDirective,
      selectors: [["", "abpPermission", ""]],
      inputs: {
        condition: [0, "abpPermission", "condition"],
        runChangeDetection: [0, "abpPermissionRunChangeDetection", "runChangeDetection"]
      },
      standalone: true,
      features: [ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpPermission]"
    }]
  }], () => [{
    type: TemplateRef,
    decorators: [{
      type: Optional
    }]
  }, {
    type: ViewContainerRef
  }, {
    type: PermissionService
  }, {
    type: ChangeDetectorRef
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [QUEUE_MANAGER]
    }]
  }], {
    condition: [{
      type: Input,
      args: ["abpPermission"]
    }],
    runChangeDetection: [{
      type: Input,
      args: ["abpPermissionRunChangeDetection"]
    }]
  });
})();
var ReplaceableTemplateDirective = class _ReplaceableTemplateDirective {
  constructor(injector, templateRef, vcRef, replaceableComponents, subscription) {
    this.injector = injector;
    this.templateRef = templateRef;
    this.vcRef = vcRef;
    this.replaceableComponents = replaceableComponents;
    this.subscription = subscription;
    this.providedData = {
      inputs: {},
      outputs: {}
    };
    this.context = {};
    this.defaultComponentSubscriptions = {};
    this.initialized = false;
    this.context = {
      initTemplate: (ref) => {
        this.resetDefaultComponent();
        this.defaultComponentRef = ref;
        this.setDefaultComponentInputs();
      }
    };
  }
  ngOnInit() {
    const component$ = this.replaceableComponents.get$(this.data.componentKey).pipe(filter((res = {}) => !this.initialized || !collectionCompare(res.component, this.externalComponent)));
    this.subscription.addOne(component$, (res = {}) => {
      this.vcRef.clear();
      this.externalComponent = res.component;
      if (this.defaultComponentRef) {
        this.resetDefaultComponent();
      }
      if (res.component) {
        this.setProvidedData();
        const customInjector = Injector.create({
          providers: [{
            provide: "REPLACEABLE_DATA",
            useValue: this.providedData
          }],
          parent: this.injector
        });
        const ref = this.vcRef.createComponent(res.component, {
          index: 0,
          injector: customInjector
        });
      } else {
        this.vcRef.createEmbeddedView(this.templateRef, this.context);
      }
      this.initialized = true;
    });
  }
  ngOnChanges(changes) {
    if (changes?.data?.currentValue?.inputs && this.defaultComponentRef) {
      this.setDefaultComponentInputs();
    }
  }
  setDefaultComponentInputs() {
    if (!this.defaultComponentRef || !this.data.inputs && !this.data.outputs) return;
    if (this.data.inputs) {
      for (const key in this.data.inputs) {
        if (Object.prototype.hasOwnProperty.call(this.data.inputs, key)) {
          if (!collectionCompare(this.defaultComponentRef[key], this.data.inputs[key].value)) {
            this.defaultComponentRef[key] = this.data.inputs[key].value;
          }
        }
      }
    }
    if (this.data.outputs) {
      for (const key in this.data.outputs) {
        if (Object.prototype.hasOwnProperty.call(this.data.outputs, key)) {
          if (!this.defaultComponentSubscriptions[key]) {
            this.defaultComponentSubscriptions[key] = this.defaultComponentRef[key].subscribe((value) => {
              this.data.outputs?.[key](value);
            });
          }
        }
      }
    }
  }
  setProvidedData() {
    this.providedData = __spreadProps(__spreadValues({
      outputs: {}
    }, this.data), {
      inputs: {}
    });
    if (!this.data.inputs) return;
    Object.defineProperties(this.providedData.inputs, __spreadValues({}, Object.keys(this.data.inputs).reduce((acc, key) => __spreadProps(__spreadValues({}, acc), {
      [key]: __spreadValues({
        enumerable: true,
        configurable: true,
        get: () => this.data.inputs?.[key]?.value
      }, this.data.inputs?.[key]?.twoWay && {
        set: (newValue) => {
          if (this.data.inputs?.[key]) {
            this.data.inputs[key].value = newValue;
          }
          if (this.data.outputs?.[`${key}Change`]) {
            this.data.outputs[`${key}Change`](newValue);
          }
        }
      })
    }), {})));
  }
  resetDefaultComponent() {
    Object.keys(this.defaultComponentSubscriptions).forEach((key) => {
      this.defaultComponentSubscriptions[key].unsubscribe();
    });
    this.defaultComponentSubscriptions = {};
    this.defaultComponentRef = null;
  }
  static {
    this.ɵfac = function ReplaceableTemplateDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ReplaceableTemplateDirective)(ɵɵdirectiveInject(Injector), ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(ReplaceableComponentsService), ɵɵdirectiveInject(SubscriptionService));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _ReplaceableTemplateDirective,
      selectors: [["", "abpReplaceableTemplate", ""]],
      inputs: {
        data: [0, "abpReplaceableTemplate", "data"]
      },
      standalone: true,
      features: [ɵɵProvidersFeature([SubscriptionService]), ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplaceableTemplateDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpReplaceableTemplate]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: Injector
  }, {
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }, {
    type: ReplaceableComponentsService
  }, {
    type: SubscriptionService
  }], {
    data: [{
      type: Input,
      args: ["abpReplaceableTemplate"]
    }]
  });
})();
var StopPropagationDirective = class _StopPropagationDirective {
  constructor(el, subscription) {
    this.el = el;
    this.subscription = subscription;
    this.stopPropEvent = new EventEmitter();
  }
  ngOnInit() {
    this.subscription.addOne(fromEvent(this.el.nativeElement, "click"), (event) => {
      event.stopPropagation();
      this.stopPropEvent.emit(event);
    });
  }
  static {
    this.ɵfac = function StopPropagationDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _StopPropagationDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(SubscriptionService));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _StopPropagationDirective,
      selectors: [["", "click.stop", ""]],
      outputs: {
        stopPropEvent: "click.stop"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([SubscriptionService])]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StopPropagationDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[click.stop]",
      providers: [SubscriptionService]
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: SubscriptionService
  }], {
    stopPropEvent: [{
      type: Output,
      args: ["click.stop"]
    }]
  });
})();
var LocalizationPipe = class _LocalizationPipe {
  constructor(localization) {
    this.localization = localization;
  }
  transform(value = "", ...interpolateParams) {
    const params = interpolateParams.reduce((acc, val) => {
      if (!acc) {
        return val;
      }
      if (!val) {
        return acc;
      }
      return Array.isArray(val) ? [...acc, ...val] : [...acc, val];
    }, []) || [];
    return this.localization.instant(value, ...params);
  }
  static {
    this.ɵfac = function LocalizationPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LocalizationPipe)(ɵɵdirectiveInject(LocalizationService, 16));
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "abpLocalization",
      type: _LocalizationPipe,
      pure: true
    });
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _LocalizationPipe,
      factory: _LocalizationPipe.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      name: "abpLocalization"
    }]
  }], () => [{
    type: LocalizationService
  }], null);
})();
var LocalizationModule = class _LocalizationModule {
  static {
    this.ɵfac = function LocalizationModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LocalizationModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _LocalizationModule,
      declarations: [LocalizationPipe],
      exports: [LocalizationPipe]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocalizationModule, [{
    type: NgModule,
    args: [{
      exports: [LocalizationPipe],
      declarations: [LocalizationPipe]
    }]
  }], null, null);
})();
var SortPipe = class _SortPipe {
  transform(value, sortOrder = "asc", sortKey) {
    sortOrder = sortOrder && sortOrder.toLowerCase();
    if (!value || sortOrder !== "asc" && sortOrder !== "desc") return value;
    let numberArray = [];
    let stringArray = [];
    if (!sortKey) {
      numberArray = value.filter((item) => typeof item === "number").sort();
      stringArray = value.filter((item) => typeof item === "string").sort();
    } else {
      numberArray = value.filter((item) => typeof item[sortKey] === "number").sort((a, b) => a[sortKey] - b[sortKey]);
      stringArray = value.filter((item) => typeof item[sortKey] === "string").sort((a, b) => {
        if (a[sortKey] < b[sortKey]) return -1;
        else if (a[sortKey] > b[sortKey]) return 1;
        else return 0;
      });
    }
    const sorted = [...numberArray, ...stringArray, ...value.filter((item) => typeof (sortKey ? item[sortKey] : item) !== "number" && typeof (sortKey ? item[sortKey] : item) !== "string")];
    return sortOrder === "asc" ? sorted : sorted.reverse();
  }
  static {
    this.ɵfac = function SortPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SortPipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "abpSort",
      type: _SortPipe,
      pure: true
    });
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _SortPipe,
      factory: _SortPipe.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SortPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      name: "abpSort"
    }]
  }], null, null);
})();
var INJECTOR_PIPE_DATA_TOKEN = new InjectionToken("INJECTOR_PIPE_DATA_TOKEN");
var ToInjectorPipe = class _ToInjectorPipe {
  constructor(injector) {
    this.injector = injector;
  }
  transform(value, token = INJECTOR_PIPE_DATA_TOKEN, name = "ToInjectorPipe") {
    return Injector.create({
      providers: [{
        provide: token,
        useValue: value
      }],
      parent: this.injector,
      name
    });
  }
  static {
    this.ɵfac = function ToInjectorPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ToInjectorPipe)(ɵɵdirectiveInject(Injector, 16));
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "toInjector",
      type: _ToInjectorPipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ToInjectorPipe, [{
    type: Pipe,
    args: [{
      name: "toInjector"
    }]
  }], () => [{
    type: Injector
  }], null);
})();
Date.prototype.toLocalISOString = function() {
  const timezoneOffset = this.getTimezoneOffset();
  return new Date(this.getTime() - timezoneOffset * 6e4).toISOString();
};
var ShortDateTimePipe = class _ShortDateTimePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortDateShortTimeFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
  static {
    this.ɵfac = function ShortDateTimePipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ShortDateTimePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "shortDateTime",
      type: _ShortDateTimePipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortDateTimePipe, [{
    type: Pipe,
    args: [{
      name: "shortDateTime",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var ShortTimePipe = class _ShortTimePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortTimeFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
  static {
    this.ɵfac = function ShortTimePipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ShortTimePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "shortTime",
      type: _ShortTimePipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortTimePipe, [{
    type: Pipe,
    args: [{
      name: "shortTime",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var ShortDatePipe = class _ShortDatePipe extends DatePipe {
  constructor(configStateService, locale, defaultTimezone) {
    super(locale, defaultTimezone);
    this.configStateService = configStateService;
  }
  transform(value, timezone, locale) {
    const format = getShortDateFormat(this.configStateService);
    return super.transform(value, format, timezone, locale);
  }
  static {
    this.ɵfac = function ShortDatePipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ShortDatePipe)(ɵɵdirectiveInject(ConfigStateService, 16), ɵɵdirectiveInject(LOCALE_ID, 16), ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "shortDate",
      type: _ShortDatePipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShortDatePipe, [{
    type: Pipe,
    args: [{
      name: "shortDate",
      pure: true
    }]
  }], () => [{
    type: ConfigStateService
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOCALE_ID]
    }]
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DATE_PIPE_DEFAULT_TIMEZONE]
    }, {
      type: Optional
    }]
  }], null);
})();
var SafeHtmlPipe = class _SafeHtmlPipe {
  constructor() {
    this.sanitizer = inject(DomSanitizer);
  }
  transform(value) {
    if (typeof value !== "string") return "";
    return this.sanitizer.sanitize(SecurityContext.HTML, value);
  }
  static {
    this.ɵfac = function SafeHtmlPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _SafeHtmlPipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "abpSafeHtml",
      type: _SafeHtmlPipe,
      pure: true
    });
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _SafeHtmlPipe,
      factory: _SafeHtmlPipe.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SafeHtmlPipe, [{
    type: Injectable
  }, {
    type: Pipe,
    args: [{
      name: "abpSafeHtml"
    }]
  }], null, null);
})();
function setLanguageToCookie(injector) {
  return () => {
    const sessionState = injector.get(SessionStateService);
    const document2 = injector.get(DOCUMENT);
    const cookieLanguageKey = injector.get(COOKIE_LANGUAGE_KEY);
    sessionState.getLanguage$().subscribe((language) => {
      const cookieValue = encodeURIComponent(`c=${language}|uic=${language}`);
      document2.cookie = `${cookieLanguageKey}=${cookieValue}`;
    });
  };
}
var CookieLanguageProvider = {
  provide: APP_INITIALIZER,
  useFactory: setLanguageToCookie,
  deps: [Injector],
  multi: true
};
var LocaleId = class extends String {
  constructor(localizationService) {
    super();
    this.localizationService = localizationService;
  }
  toString() {
    const {
      currentLang
    } = this.localizationService;
    if (checkHasProp(differentLocales, currentLang)) {
      return differentLocales[currentLang];
    }
    return currentLang;
  }
  valueOf() {
    return this.toString();
  }
};
var LocaleProvider = {
  provide: LOCALE_ID,
  useClass: LocaleId,
  deps: [LocalizationService]
};
var IncludeLocalizationResourcesProvider = {
  provide: INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  useValue: false
};
var RoutesHandler = class _RoutesHandler {
  constructor(routes, router) {
    this.routes = routes;
    this.router = router;
    this.addRoutes();
  }
  addRoutes() {
    this.router?.config?.forEach(({
      path = "",
      data
    }) => {
      const routes = data?.routes;
      if (!routes) return;
      if (Array.isArray(routes)) {
        this.routes.add(routes);
      } else {
        const routesFlatten = flatRoutes([__spreadValues({
          path
        }, routes)], {
          path: ""
        });
        this.routes.add(routesFlatten);
      }
    });
  }
  static {
    this.ɵfac = function RoutesHandler_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RoutesHandler)(ɵɵinject(RoutesService), ɵɵinject(Router, 8));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _RoutesHandler,
      factory: _RoutesHandler.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoutesHandler, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RoutesService
  }, {
    type: Router,
    decorators: [{
      type: Optional
    }]
  }], null);
})();
function flatRoutes(routes, parent) {
  if (!routes) return [];
  return routes.reduce((acc, route) => {
    const _a = __spreadProps(__spreadValues({}, route), {
      parentName: parent.name,
      path: (parent.path + "/" + route.path).replace(/\/\//g, "/")
    }), {
      children
    } = _a, current = __objRest(_a, [
      "children"
    ]);
    acc.push(current, ...flatRoutes(children, current));
    return acc;
  }, []);
}
var CoreFeatureKind;
(function(CoreFeatureKind2) {
  CoreFeatureKind2[CoreFeatureKind2["Options"] = 0] = "Options";
  CoreFeatureKind2[CoreFeatureKind2["CompareFunctionFactory"] = 1] = "CompareFunctionFactory";
  CoreFeatureKind2[CoreFeatureKind2["TitleStrategy"] = 2] = "TitleStrategy";
})(CoreFeatureKind || (CoreFeatureKind = {}));
function makeCoreFeature(kind, providers) {
  return {
    ɵkind: kind,
    ɵproviders: providers
  };
}
function withOptions(options = {}) {
  return makeCoreFeature(CoreFeatureKind.Options, [{
    provide: "CORE_OPTIONS",
    useValue: options
  }, {
    provide: CORE_OPTIONS,
    useFactory: coreOptionsFactory,
    deps: ["CORE_OPTIONS"]
  }, {
    provide: TENANT_KEY,
    useValue: options.tenantKey || "__tenant"
  }, {
    provide: LOCALIZATIONS,
    multi: true,
    useValue: localizationContributor(options.localizations),
    deps: [LocalizationService]
  }, {
    provide: OTHERS_GROUP,
    useValue: options.othersGroup || "AbpUi::OthersGroup"
  }, {
    provide: DYNAMIC_LAYOUTS_TOKEN,
    useValue: options.dynamicLayouts || DEFAULT_DYNAMIC_LAYOUTS
  }]);
}
function withTitleStrategy(strategy) {
  return makeCoreFeature(CoreFeatureKind.TitleStrategy, [{
    provide: TitleStrategy,
    useExisting: strategy
  }]);
}
function withCompareFuncFactory(factory) {
  return makeCoreFeature(CoreFeatureKind.CompareFunctionFactory, [{
    provide: SORT_COMPARE_FUNC,
    useFactory: factory
  }]);
}
function provideAbpCore(...features) {
  const providers = [LocaleProvider, CookieLanguageProvider, {
    provide: APP_INITIALIZER,
    multi: true,
    deps: [Injector],
    useFactory: getInitialData
  }, {
    provide: APP_INITIALIZER,
    multi: true,
    deps: [Injector],
    useFactory: localeInitializer
  }, {
    provide: APP_INITIALIZER,
    multi: true,
    deps: [LocalizationService],
    useFactory: noop
  }, {
    provide: APP_INITIALIZER,
    multi: true,
    deps: [LocalStorageListenerService],
    useFactory: noop
  }, {
    provide: APP_INITIALIZER,
    multi: true,
    deps: [RoutesHandler],
    useFactory: noop
  }, {
    provide: SORT_COMPARE_FUNC,
    useFactory: compareFuncFactory
  }, {
    provide: QUEUE_MANAGER,
    useClass: DefaultQueueManager
  }, AuthErrorFilterService, IncludeLocalizationResourcesProvider, {
    provide: TitleStrategy,
    useExisting: AbpTitleStrategy
  }];
  for (const feature of features) {
    providers.push(...feature.ɵproviders);
  }
  return makeEnvironmentProviders(providers);
}
function provideAbpCoreChild(options = {}) {
  return makeEnvironmentProviders([{
    provide: LOCALIZATIONS,
    multi: true,
    useValue: localizationContributor(options.localizations),
    deps: [LocalizationService]
  }]);
}
var standaloneDirectives = [AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective];
var BaseCoreModule = class _BaseCoreModule {
  static {
    this.ɵfac = function BaseCoreModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _BaseCoreModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _BaseCoreModule,
      declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],
      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective],
      exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())],
      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BaseCoreModule, [{
    type: NgModule,
    args: [{
      exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, ...standaloneDirectives],
      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, ...standaloneDirectives],
      declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],
      providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())]
    }]
  }], null, null);
})();
var RootCoreModule = class _RootCoreModule {
  static {
    this.ɵfac = function RootCoreModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RootCoreModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _RootCoreModule,
      imports: [BaseCoreModule, LocalizationModule],
      exports: [BaseCoreModule, LocalizationModule]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [provideHttpClient(withXsrfConfiguration({
        cookieName: "XSRF-TOKEN",
        headerName: "RequestVerificationToken"
      }))],
      imports: [BaseCoreModule, LocalizationModule, BaseCoreModule, LocalizationModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RootCoreModule, [{
    type: NgModule,
    args: [{
      exports: [BaseCoreModule, LocalizationModule],
      imports: [BaseCoreModule, LocalizationModule],
      providers: [provideHttpClient(withXsrfConfiguration({
        cookieName: "XSRF-TOKEN",
        headerName: "RequestVerificationToken"
      }))]
    }]
  }], null, null);
})();
var CoreModule = class _CoreModule {
  /**
   * @deprecated forRoot method is deprecated, use `provideAbpCore` *function* for config settings.
   */
  static forRoot(options = {}) {
    return {
      ngModule: RootCoreModule,
      providers: [provideAbpCore(withOptions(options))]
    };
  }
  /**
   * @deprecated forChild method is deprecated, use `provideAbpCoreChild` *function* for config settings.
   */
  static forChild(options = {}) {
    return {
      ngModule: RootCoreModule,
      providers: [provideAbpCoreChild(options)]
    };
  }
  static {
    this.ɵfac = function CoreModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _CoreModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _CoreModule,
      imports: [BaseCoreModule],
      exports: [BaseCoreModule]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [BaseCoreModule, BaseCoreModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CoreModule, [{
    type: NgModule,
    args: [{
      exports: [BaseCoreModule],
      imports: [BaseCoreModule]
    }]
  }], null, null);
})();
var ShowPasswordDirective = class _ShowPasswordDirective {
  constructor() {
    this.elementRef = inject(ElementRef);
  }
  set abpShowPassword(visible) {
    const element = this.elementRef.nativeElement;
    if (!element) return;
    element.type = visible ? "text" : "password";
  }
  static {
    this.ɵfac = function ShowPasswordDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ShowPasswordDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _ShowPasswordDirective,
      selectors: [["", "abpShowPassword", ""]],
      inputs: {
        abpShowPassword: "abpShowPassword"
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ShowPasswordDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpShowPassword]"
    }]
  }], null, {
    abpShowPassword: [{
      type: Input
    }]
  });
})();
var TrackCapsLockDirective = class _TrackCapsLockDirective {
  constructor() {
    this.capsLock = new EventEmitter();
  }
  onKeyDown(event) {
    this.capsLock.emit(this.isCapsLockOpen(event));
  }
  onKeyUp(event) {
    this.capsLock.emit(this.isCapsLockOpen(event));
  }
  isCapsLockOpen(e) {
    const s = String.fromCharCode(e.which);
    if (s.toUpperCase() === s && s.toLowerCase() !== s && e.shiftKey || s.toUpperCase() !== s && s.toLowerCase() === s && e.shiftKey || e.getModifierState && e.getModifierState("CapsLock")) {
      return true;
    }
    return false;
  }
  static {
    this.ɵfac = function TrackCapsLockDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TrackCapsLockDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _TrackCapsLockDirective,
      selectors: [["", "abpCapsLock", ""]],
      hostBindings: function TrackCapsLockDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("keydown", function TrackCapsLockDirective_keydown_HostBindingHandler($event) {
            return ctx.onKeyDown($event);
          }, false, ɵɵresolveWindow)("keyup", function TrackCapsLockDirective_keyup_HostBindingHandler($event) {
            return ctx.onKeyUp($event);
          }, false, ɵɵresolveWindow);
        }
      },
      outputs: {
        capsLock: "abpCapsLock"
      },
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TrackCapsLockDirective, [{
    type: Directive,
    args: [{
      standalone: true,
      selector: "[abpCapsLock]"
    }]
  }], null, {
    capsLock: [{
      type: Output,
      args: ["abpCapsLock"]
    }],
    onKeyDown: [{
      type: HostListener,
      args: ["window:keydown", ["$event"]]
    }],
    onKeyUp: [{
      type: HostListener,
      args: ["window:keyup", ["$event"]]
    }]
  });
})();
var PermissionGuard = class _PermissionGuard {
  constructor() {
    this.router = inject(Router);
    this.routesService = inject(RoutesService);
    this.authService = inject(AuthService);
    this.permissionService = inject(PermissionService);
    this.httpErrorReporter = inject(HttpErrorReporterService);
  }
  canActivate(route, state) {
    let {
      requiredPolicy
    } = route.data || {};
    if (!requiredPolicy) {
      const routeFound = findRoute(this.routesService, getRoutePath(this.router, state.url));
      requiredPolicy = routeFound?.requiredPolicy;
    }
    if (!requiredPolicy) return of(true);
    return this.permissionService.getGrantedPolicy$(requiredPolicy).pipe(tap((access) => {
      if (!access && this.authService.isAuthenticated) {
        this.httpErrorReporter.reportError({
          status: 403
        });
      }
    }));
  }
  static {
    this.ɵfac = function PermissionGuard_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PermissionGuard)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _PermissionGuard,
      factory: _PermissionGuard.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PermissionGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var permissionGuard = (route, state) => {
  const router = inject(Router);
  const routesService = inject(RoutesService);
  const authService = inject(AuthService);
  const permissionService = inject(PermissionService);
  const httpErrorReporter = inject(HttpErrorReporterService);
  let {
    requiredPolicy
  } = route.data || {};
  if (!requiredPolicy) {
    const routeFound = findRoute(routesService, getRoutePath(router, state.url));
    requiredPolicy = routeFound?.requiredPolicy;
  }
  if (!requiredPolicy) return of(true);
  return permissionService.getGrantedPolicy$(requiredPolicy).pipe(tap((access) => {
    if (!access && authService.isAuthenticated) {
      httpErrorReporter.reportError({
        status: 403
      });
    }
  }));
};
var ListResultDto = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key)) {
        this[key] = initialValues[key];
      }
    }
  }
};
var PagedResultDto = class extends ListResultDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleObject = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key) && initialValues[key] !== void 0) {
        this[key] = initialValues[key];
      }
    }
  }
};
var ExtensibleEntityDto = class extends ExtensibleObject {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var LimitedResultRequestDto = class {
  constructor(initialValues = {}) {
    this.maxResultCount = 10;
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key) && initialValues[key] !== void 0) {
        this[key] = initialValues[key];
      }
    }
  }
};
var ExtensibleLimitedResultRequestDto = class extends ExtensibleEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
    this.maxResultCount = 10;
  }
};
var PagedResultRequestDto = class extends LimitedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensiblePagedResultRequestDto = class extends ExtensibleLimitedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var PagedAndSortedResultRequestDto = class extends PagedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensiblePagedAndSortedResultRequestDto = class extends ExtensiblePagedResultRequestDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var EntityDto = class {
  constructor(initialValues = {}) {
    for (const key in initialValues) {
      if (checkHasProp(initialValues, key)) {
        this[key] = initialValues[key];
      }
    }
  }
};
var CreationAuditedEntityDto = class extends EntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var CreationAuditedEntityWithUserDto = class extends CreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuditedEntityDto = class extends CreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuditedEntityWithUserDto = class extends AuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var FullAuditedEntityDto = class extends AuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var FullAuditedEntityWithUserDto = class extends FullAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleCreationAuditedEntityDto = class extends ExtensibleEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleAuditedEntityDto = class extends ExtensibleCreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleAuditedEntityWithUserDto = class extends ExtensibleAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleCreationAuditedEntityWithUserDto = class extends ExtensibleCreationAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleFullAuditedEntityDto = class extends ExtensibleAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var ExtensibleFullAuditedEntityWithUserDto = class extends ExtensibleFullAuditedEntityDto {
  constructor(initialValues = {}) {
    super(initialValues);
  }
};
var AuthEvent = class {
  constructor(type) {
    this.type = type;
    this.type = type;
  }
};
var AuthSuccessEvent = class extends AuthEvent {
  constructor(type, info) {
    super(type);
    this.type = type;
    this.info = info;
  }
};
var AuthInfoEvent = class extends AuthEvent {
  constructor(type, info) {
    super(type);
    this.type = type;
    this.info = info;
  }
};
var AuthErrorEvent = class extends AuthEvent {
  constructor(type, reason, params) {
    super(type);
    this.type = type;
    this.reason = reason;
    this.params = params;
  }
};
var AbpApiDefinitionService = class _AbpApiDefinitionService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "abp";
    this.getByModel = (model, config) => this.restService.request({
      method: "GET",
      url: "/api/abp/api-definition",
      params: {
        includeTypes: model.includeTypes
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function AbpApiDefinitionService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbpApiDefinitionService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AbpApiDefinitionService,
      factory: _AbpApiDefinitionService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbpApiDefinitionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var index = Object.freeze({
  __proto__: null
});
var ContainerStrategy = class {
  constructor(containerRef) {
    this.containerRef = containerRef;
  }
  prepare() {
  }
};
var ClearContainerStrategy = class extends ContainerStrategy {
  getIndex() {
    return 0;
  }
  prepare() {
    this.containerRef.clear();
  }
};
var InsertIntoContainerStrategy = class extends ContainerStrategy {
  constructor(containerRef, index2) {
    super(containerRef);
    this.index = index2;
  }
  getIndex() {
    return Math.min(Math.max(0, this.index), this.containerRef.length);
  }
};
var CONTAINER_STRATEGY = {
  Clear(containerRef) {
    return new ClearContainerStrategy(containerRef);
  },
  Append(containerRef) {
    return new InsertIntoContainerStrategy(containerRef, containerRef.length);
  },
  Prepend(containerRef) {
    return new InsertIntoContainerStrategy(containerRef, 0);
  },
  Insert(containerRef, index2) {
    return new InsertIntoContainerStrategy(containerRef, index2);
  }
};
var ContentSecurityStrategy = class {
  constructor(nonce) {
    this.nonce = nonce;
  }
};
var LooseContentSecurityStrategy = class extends ContentSecurityStrategy {
  constructor(nonce) {
    super(nonce);
  }
  applyCSP(element) {
    if (this.nonce) {
      element.setAttribute("nonce", this.nonce);
    }
  }
};
var NoContentSecurityStrategy = class extends ContentSecurityStrategy {
  constructor() {
    super();
  }
  applyCSP(_) {
  }
};
var CONTENT_SECURITY_STRATEGY = {
  Loose(nonce) {
    return new LooseContentSecurityStrategy(nonce);
  },
  None() {
    return new NoContentSecurityStrategy();
  }
};
var ContentStrategy = class {
  constructor(content, domStrategy = DOM_STRATEGY.AppendToHead(), contentSecurityStrategy = CONTENT_SECURITY_STRATEGY.None(), options = {}) {
    this.content = content;
    this.domStrategy = domStrategy;
    this.contentSecurityStrategy = contentSecurityStrategy;
    this.options = options;
  }
  insertElement() {
    const element = this.createElement();
    if (this.options && Object.keys(this.options).length > 0) {
      Object.keys(this.options).forEach((key) => {
        if (this.options[key]) {
          element[key] = this.options[key];
        }
      });
    }
    this.contentSecurityStrategy.applyCSP(element);
    this.domStrategy.insertElement(element);
    return element;
  }
};
var StyleContentStrategy = class extends ContentStrategy {
  createElement() {
    const element = document.createElement("style");
    element.textContent = this.content;
    return element;
  }
};
var ScriptContentStrategy = class extends ContentStrategy {
  createElement() {
    const element = document.createElement("script");
    element.textContent = this.content;
    return element;
  }
};
var CONTENT_STRATEGY = {
  AppendScriptToBody(content, options) {
    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToBody(), void 0, options);
  },
  AppendScriptToHead(content, options) {
    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToHead(), void 0, options);
  },
  AppendStyleToHead(content, options) {
    return new StyleContentStrategy(content, DOM_STRATEGY.AppendToHead(), void 0, options);
  },
  PrependStyleToHead(content, options) {
    return new StyleContentStrategy(content, DOM_STRATEGY.PrependToHead(), void 0, options);
  }
};
var ContextStrategy = class {
  constructor(context) {
    this.context = context;
  }
  setContext(componentRef) {
    return this.context;
  }
};
var NoContextStrategy = class extends ContextStrategy {
  constructor() {
    super(void 0);
  }
};
var ComponentContextStrategy = class extends ContextStrategy {
  setContext(componentRef) {
    Object.keys(this.context).forEach((key) => componentRef.instance[key] = this.context[key]);
    componentRef.changeDetectorRef.detectChanges();
    return this.context;
  }
};
var TemplateContextStrategy = class extends ContextStrategy {
  setContext() {
    return this.context;
  }
};
var CONTEXT_STRATEGY = {
  None() {
    return new NoContextStrategy();
  },
  Component(context) {
    return new ComponentContextStrategy(context);
  },
  Template(context) {
    return new TemplateContextStrategy(context);
  }
};
var LoadingStrategy = class {
  constructor(path, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {
    this.path = path;
    this.domStrategy = domStrategy;
    this.crossOriginStrategy = crossOriginStrategy;
  }
  createStream() {
    this.element = this.createElement();
    return of(null).pipe(switchMap(() => fromLazyLoad(this.element, this.domStrategy, this.crossOriginStrategy)));
  }
};
var ScriptLoadingStrategy = class extends LoadingStrategy {
  constructor(src, domStrategy, crossOriginStrategy) {
    super(src, domStrategy, crossOriginStrategy);
  }
  createElement() {
    const element = document.createElement("script");
    element.src = this.path;
    return element;
  }
};
var StyleLoadingStrategy = class extends LoadingStrategy {
  constructor(href, domStrategy, crossOriginStrategy) {
    super(href, domStrategy, crossOriginStrategy);
  }
  createElement() {
    const element = document.createElement("link");
    element.rel = "stylesheet";
    element.href = this.path;
    return element;
  }
};
var LOADING_STRATEGY = {
  AppendScriptToBody(src) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.None());
  },
  AppendAnonymousScriptToBody(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  AppendAnonymousScriptToHead(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  AppendAnonymousStyleToHead(src, integrity) {
    return new StyleLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  PrependAnonymousScriptToHead(src, integrity) {
    return new ScriptLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  },
  PrependAnonymousStyleToHead(src, integrity) {
    return new StyleLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));
  }
};
var ProjectionStrategy = class {
  constructor(content) {
    this.content = content;
  }
};
var ComponentProjectionStrategy = class extends ProjectionStrategy {
  constructor(component, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {
    super(component);
    this.containerStrategy = containerStrategy;
    this.contextStrategy = contextStrategy;
  }
  injectContent(injector) {
    this.containerStrategy.prepare();
    const resolver = injector.get(ComponentFactoryResolver$1);
    const factory = resolver.resolveComponentFactory(this.content);
    const componentRef = this.containerStrategy.containerRef.createComponent(factory, this.containerStrategy.getIndex(), injector);
    this.contextStrategy.setContext(componentRef);
    return componentRef;
  }
};
var RootComponentProjectionStrategy = class extends ProjectionStrategy {
  constructor(component, contextStrategy = CONTEXT_STRATEGY.None(), domStrategy = DOM_STRATEGY.AppendToBody()) {
    super(component);
    this.contextStrategy = contextStrategy;
    this.domStrategy = domStrategy;
  }
  injectContent(injector) {
    const appRef = injector.get(ApplicationRef);
    const resolver = injector.get(ComponentFactoryResolver$1);
    const componentRef = resolver.resolveComponentFactory(this.content).create(injector);
    this.contextStrategy.setContext(componentRef);
    appRef.attachView(componentRef.hostView);
    const element = componentRef.hostView.rootNodes[0];
    this.domStrategy.insertElement(element);
    return componentRef;
  }
};
var TemplateProjectionStrategy = class extends ProjectionStrategy {
  constructor(templateRef, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {
    super(templateRef);
    this.containerStrategy = containerStrategy;
    this.contextStrategy = contextStrategy;
  }
  injectContent() {
    this.containerStrategy.prepare();
    const embeddedViewRef = this.containerStrategy.containerRef.createEmbeddedView(this.content, this.contextStrategy.context, this.containerStrategy.getIndex());
    embeddedViewRef.detectChanges();
    return embeddedViewRef;
  }
};
var PROJECTION_STRATEGY = {
  AppendComponentToBody(component, context) {
    return new RootComponentProjectionStrategy(component, context && CONTEXT_STRATEGY.Component(context));
  },
  AppendComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  AppendTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Template(context));
  },
  PrependComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  PrependTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Template(context));
  },
  ProjectComponentToContainer(component, containerRef, context) {
    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Component(context));
  },
  ProjectTemplateToContainer(templateRef, containerRef, context) {
    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Template(context));
  }
};
function validateMinAge({
  age = 18
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    return isValidMinAge(control.value, age) ? null : {
      minAge: {
        age
      }
    };
  };
}
function isValidMinAge(value, minAge) {
  const date = /* @__PURE__ */ new Date();
  date.setFullYear(date.getFullYear() - minAge);
  date.setHours(23, 59, 59, 999);
  return Number(new Date(value)) <= date.valueOf();
}
function validateCreditCard() {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    return isValidCreditCard(String(control.value)) ? null : {
      creditCard: true
    };
  };
}
function isValidCreditCard(value) {
  value = value.replace(/[ -]/g, "");
  if (!/^[0-9]{13,19}$/.test(value)) return false;
  let checksum = 0;
  let multiplier = 1;
  for (let i = value.length; i > 0; i--) {
    const digit = Number(value[i - 1]) * multiplier;
    checksum += digit % 10 + ~~(digit / 10);
    multiplier = multiplier * 2 % 3;
  }
  return checksum % 10 === 0;
}
function validateRange({
  maximum = Infinity,
  minimum = 0
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    const value = Number(control.value);
    return getMinError(value, minimum, maximum) || getMaxError(value, maximum, minimum);
  };
}
function getMaxError(value, max, min) {
  return value > max ? {
    range: {
      max,
      min
    }
  } : null;
}
function getMinError(value, min, max) {
  return value < min ? {
    range: {
      min,
      max
    }
  } : null;
}
function validateRequired({
  allowEmptyStrings
} = {}) {
  const required = (control) => {
    return isValidRequired(control.value, allowEmptyStrings) ? null : {
      required: true
    };
  };
  return required;
}
function isValidRequired(value, allowEmptyStrings) {
  if (value || value === 0 || value === false) return true;
  if (allowEmptyStrings && value === "") return true;
  return false;
}
function validateStringLength({
  maximumLength = Infinity,
  minimumLength = 0
} = {}) {
  return (control) => {
    if (["", null, void 0].indexOf(control.value) > -1) return null;
    const value = String(control.value);
    return getMinLengthError(value, minimumLength) || getMaxLengthError(value, maximumLength);
  };
}
function getMaxLengthError(value, requiredLength) {
  return value.length > requiredLength ? {
    maxlength: {
      requiredLength
    }
  } : null;
}
function getMinLengthError(value, requiredLength) {
  return value.length < requiredLength ? {
    minlength: {
      requiredLength
    }
  } : null;
}
function validateUniqueCharacter() {
  return (control) => {
    if (isNullOrEmpty(control.value)) return null;
    return isUnqiueCharacter(control.value) ? null : {
      uniqueCharacter: true
    };
  };
}
function isUnqiueCharacter(value) {
  const set = new Set(value.split(""));
  return set.size == value.length;
}
function validateUrl() {
  return (control) => {
    if (isNullOrEmpty(control.value)) return null;
    return isValidUrl(control.value) ? null : {
      url: true
    };
  };
}
function isValidUrl(value) {
  if (/^http(s)?:\/\/[^/]/.test(value) || /^ftp:\/\/[^/]/.test(value)) {
    const a = document.createElement("a");
    a.href = value;
    return !!a.host;
  }
  return false;
}
var onlyLetterAndNumberRegex = /^[a-zA-Z0-9]+$/;
function validateUsername({
  pattern = /.*/
} = {
  pattern: onlyLetterAndNumberRegex
}) {
  return (control) => {
    const isValid = isValidUserName(control.value, pattern);
    return isValid ? null : {
      usernamePattern: {
        actualValue: control.value
      }
    };
  };
}
function isValidUserName(value, pattern) {
  if (isNullOrEmpty(value)) return true;
  return pattern.test(value);
}
var AbpValidators = {
  creditCard: validateCreditCard,
  emailAddress: () => Validators.email,
  minAge: validateMinAge,
  range: validateRange,
  required: validateRequired,
  stringLength: validateStringLength,
  url: validateUrl,
  username: validateUsername,
  uniqueCharacter: validateUniqueCharacter
};
var ApiInterceptor = class _ApiInterceptor {
  constructor(httpWaitService) {
    this.httpWaitService = httpWaitService;
  }
  getAdditionalHeaders(existingHeaders) {
    return existingHeaders || new HttpHeaders();
  }
  intercept(request, next) {
    this.httpWaitService.addRequest(request);
    return next.handle(request).pipe(finalize(() => this.httpWaitService.deleteRequest(request)));
  }
  static {
    this.ɵfac = function ApiInterceptor_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ApiInterceptor)(ɵɵinject(HttpWaitService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ApiInterceptor,
      factory: _ApiInterceptor.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApiInterceptor, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: HttpWaitService
  }], null);
})();

export {
  collectionCompare,
  AbstractNgModelComponent,
  AuthGuard,
  authGuard,
  AuthService,
  AbstractAuthErrorFilter,
  AuthErrorFilterService,
  LOCALIZATIONS,
  localizationContributor,
  localizations$,
  CORE_OPTIONS,
  coreOptionsFactory,
  getLocaleDirection,
  createLocalizer,
  createLocalizerWithFallback,
  createLocalizationPipeKeyGenerator,
  createTokenParser,
  interpolate,
  escapeHtmlChars,
  ContentProjectionService,
  pushValueTo,
  noop,
  isUndefinedOrEmptyString,
  isNullOrUndefined,
  isNullOrEmpty,
  exists,
  isObject,
  isArray,
  isObjectAndNotArray,
  isNode,
  isObjectAndNotArrayNotNode,
  checkHasProp,
  getShortDateFormat,
  getShortTimeFormat,
  getShortDateShortTimeFormat,
  deepMerge,
  InternalStore,
  EnvironmentService,
  HttpErrorReporterService,
  getRemoteEnv,
  LazyModuleFactory,
  featuresFactory,
  downloadBlob,
  isNumber,
  mapEnumToOptions,
  uuid,
  generateHash,
  generatePassword,
  getPathName,
  WebHttpUrlEncodingCodec,
  AbpLocalStorageService,
  SessionStateService,
  APP_INIT_ERROR_HANDLERS,
  AbpTenantService,
  TENANT_KEY,
  IS_EXTERNAL_REQUEST,
  ExternalHttpClient,
  RestService,
  MultiTenancyService,
  TENANT_NOT_FOUND_BY_NAME,
  parseTenantFromUrl,
  CHECK_AUTHENTICATION_STATE_FN_KEY,
  getInitialData,
  localeInitializer,
  CrossOriginStrategy,
  NoCrossOriginStrategy,
  CROSS_ORIGIN_STRATEGY,
  DomStrategy,
  DOM_STRATEGY,
  fromLazyLoad,
  DefaultQueueManager,
  findRoute,
  getRoutePath,
  reloadRoute,
  BaseTreeNode,
  createTreeFromList,
  createMapFromList,
  createTreeNodeFilterCreator,
  createGroupMap,
  DomInsertionService,
  LOADER_DELAY,
  HttpWaitService,
  ResourceWaitService,
  LazyLoadService,
  LIST_QUERY_DEBOUNCE_TIME,
  ListService,
  PermissionService,
  ReplaceableComponentsService,
  NavigationEvent,
  RouterEvents,
  RouterWaitService,
  COOKIE_LANGUAGE_KEY,
  NAVIGATE_TO_MANAGE_PROFILE,
  QUEUE_MANAGER,
  INCUDE_LOCALIZATION_RESOURCES_TOKEN,
  PIPE_TO_LOGIN_FN_KEY,
  SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY,
  OTHERS_GROUP,
  SORT_COMPARE_FUNC,
  compareFuncFactory,
  DYNAMIC_LAYOUTS_TOKEN,
  DISABLE_PROJECT_NAME,
  AbstractTreeService,
  AbstractNavTreeService,
  RoutesService,
  SubscriptionService,
  trackBy,
  trackByDeep,
  TrackByService,
  AbpWindowService,
  InternetConnectionService,
  LocalStorageListenerService,
  AbpTitleStrategy,
  AbpApplicationConfigurationService,
  AbpApplicationLocalizationService,
  ConfigStateService,
  LocalizationService,
  DynamicLayoutComponent,
  ReplaceableRouteContainerComponent,
  RouterOutletComponent,
  differentLocales,
  DEFAULT_DYNAMIC_LAYOUTS,
  AutofocusDirective,
  InputEventDebounceDirective,
  ForDirective,
  FormSubmitDirective,
  InitDirective,
  PermissionDirective,
  ReplaceableTemplateDirective,
  StopPropagationDirective,
  LocalizationPipe,
  LocalizationModule,
  SortPipe,
  INJECTOR_PIPE_DATA_TOKEN,
  ToInjectorPipe,
  ShortDateTimePipe,
  ShortTimePipe,
  ShortDatePipe,
  SafeHtmlPipe,
  setLanguageToCookie,
  CookieLanguageProvider,
  LocaleId,
  LocaleProvider,
  IncludeLocalizationResourcesProvider,
  CoreFeatureKind,
  withOptions,
  withTitleStrategy,
  withCompareFuncFactory,
  provideAbpCore,
  provideAbpCoreChild,
  BaseCoreModule,
  RootCoreModule,
  CoreModule,
  ShowPasswordDirective,
  TrackCapsLockDirective,
  PermissionGuard,
  permissionGuard,
  ListResultDto,
  PagedResultDto,
  ExtensibleObject,
  ExtensibleEntityDto,
  LimitedResultRequestDto,
  ExtensibleLimitedResultRequestDto,
  PagedResultRequestDto,
  ExtensiblePagedResultRequestDto,
  PagedAndSortedResultRequestDto,
  ExtensiblePagedAndSortedResultRequestDto,
  EntityDto,
  CreationAuditedEntityDto,
  CreationAuditedEntityWithUserDto,
  AuditedEntityDto,
  AuditedEntityWithUserDto,
  FullAuditedEntityDto,
  FullAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityDto,
  ExtensibleAuditedEntityDto,
  ExtensibleAuditedEntityWithUserDto,
  ExtensibleCreationAuditedEntityWithUserDto,
  ExtensibleFullAuditedEntityDto,
  ExtensibleFullAuditedEntityWithUserDto,
  AuthEvent,
  AuthSuccessEvent,
  AuthInfoEvent,
  AuthErrorEvent,
  AbpApiDefinitionService,
  index,
  ContainerStrategy,
  ClearContainerStrategy,
  InsertIntoContainerStrategy,
  CONTAINER_STRATEGY,
  ContentSecurityStrategy,
  LooseContentSecurityStrategy,
  NoContentSecurityStrategy,
  CONTENT_SECURITY_STRATEGY,
  ContentStrategy,
  StyleContentStrategy,
  ScriptContentStrategy,
  CONTENT_STRATEGY,
  ContextStrategy,
  NoContextStrategy,
  ComponentContextStrategy,
  TemplateContextStrategy,
  CONTEXT_STRATEGY,
  LoadingStrategy,
  ScriptLoadingStrategy,
  StyleLoadingStrategy,
  LOADING_STRATEGY,
  ProjectionStrategy,
  ComponentProjectionStrategy,
  RootComponentProjectionStrategy,
  TemplateProjectionStrategy,
  PROJECTION_STRATEGY,
  validateMinAge,
  validateCreditCard,
  validateRange,
  validateRequired,
  validateStringLength,
  validateUniqueCharacter,
  validateUrl,
  AbpValidators,
  ApiInterceptor
};
//# sourceMappingURL=chunk-3NU57XZL.js.map
