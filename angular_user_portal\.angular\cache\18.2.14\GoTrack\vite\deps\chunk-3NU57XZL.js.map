{"version": 3, "sources": ["../../../../../../node_modules/just-compare/index.mjs", "../../../../../../node_modules/just-clone/index.mjs", "../../../../../../node_modules/@abp/ng.core/fesm2022/abp-ng.core.mjs"], "sourcesContent": ["var collectionCompare = compare;\n\n/*\n  primitives: value1 === value2\n  functions: value1.toString == value2.toString\n  arrays: if length, sequence and values of properties are identical\n  objects: if length, names and values of properties are identical\n  compare([[1, [2, 3]], [[1, [2, 3]]); // true\n  compare([[1, [2, 3], 4], [[1, [2, 3]]); // false\n  compare({a: 2, b: 3}, {a: 2, b: 3}); // true\n  compare({a: 2, b: 3}, {b: 3, a: 2}); // true\n  compare({a: 2, b: 3, c: 4}, {a: 2, b: 3}); // false\n  compare({a: 2, b: 3}, {a: 2, b: 3, c: 4}); // false\n  compare([[1, [2, {a: 4}], 4], [[1, [2, {a: 4}]]); // true\n*/\n\nfunction compare(value1, value2) {\n  if (value1 === value2) {\n    return true;\n  }\n\n  /* eslint-disable no-self-compare */\n  // if both values are NaNs return true\n  if (value1 !== value1 && value2 !== value2) {\n    return true;\n  }\n  if (typeof value1 != typeof value2 ||\n  // primitive != primitive wrapper\n  {}.toString.call(value1) != {}.toString.call(value2) // check for other (maybe nullish) objects\n  ) {\n    return false;\n  }\n  if (value1 !== Object(value1)) {\n    // non equal primitives\n    return false;\n  }\n  if (!value1) {\n    return false;\n  }\n  if (Array.isArray(value1)) {\n    return compareArrays(value1, value2);\n  }\n  if ({}.toString.call(value1) == '[object Set]') {\n    return compareArrays(Array.from(value1), Array.from(value2));\n  }\n  if ({}.toString.call(value1) == '[object Object]') {\n    return compareObjects(value1, value2);\n  }\n  return compareNativeSubtypes(value1, value2);\n}\nfunction compareNativeSubtypes(value1, value2) {\n  // e.g. Function, RegExp, Date\n  return value1.toString() === value2.toString();\n}\nfunction compareArrays(value1, value2) {\n  var len = value1.length;\n  if (len != value2.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    if (!compare(value1[i], value2[i])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction compareObjects(value1, value2) {\n  var keys1 = Object.keys(value1);\n  var len = keys1.length;\n  if (len != Object.keys(value2).length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    var key1 = keys1[i];\n    if (!(value2.hasOwnProperty(key1) && compare(value1[key1], value2[key1]))) {\n      return false;\n    }\n  }\n  return true;\n}\nexport { collectionCompare as default };", "var collectionClone = clone;\n\n/*\n  Deep clones all properties except functions\n\n  var arr = [1, 2, 3];\n  var subObj = {aa: 1};\n  var obj = {a: 3, b: 5, c: arr, d: subObj};\n  var objClone = clone(obj);\n  arr.push(4);\n  subObj.bb = 2;\n  obj; // {a: 3, b: 5, c: [1, 2, 3, 4], d: {aa: 1}}\n  objClone; // {a: 3, b: 5, c: [1, 2, 3], d: {aa: 1, bb: 2}}\n*/\n\nfunction clone(obj) {\n  let result = obj;\n  var type = {}.toString.call(obj).slice(8, -1);\n  if (type == 'Set') {\n    return new Set([...obj].map(value => clone(value)));\n  }\n  if (type == 'Map') {\n    return new Map([...obj].map(kv => [clone(kv[0]), clone(kv[1])]));\n  }\n  if (type == 'Date') {\n    return new Date(obj.getTime());\n  }\n  if (type == 'RegExp') {\n    return RegExp(obj.source, getRegExpFlags(obj));\n  }\n  if (type == 'Array' || type == 'Object') {\n    result = Array.isArray(obj) ? [] : {};\n    for (var key in obj) {\n      // include prototype properties\n      result[key] = clone(obj[key]);\n    }\n  }\n  // primitives and non-supported objects (e.g. functions) land here\n  return result;\n}\nfunction getRegExpFlags(regExp) {\n  if (typeof regExp.source.flags == 'string') {\n    return regExp.source.flags;\n  } else {\n    var flags = [];\n    regExp.global && flags.push('g');\n    regExp.ignoreCase && flags.push('i');\n    regExp.multiline && flags.push('m');\n    regExp.sticky && flags.push('y');\n    regExp.unicode && flags.push('u');\n    return flags.join('');\n  }\n}\nexport { collectionClone as default };", "import * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, Input, Component, Injectable, InjectionToken, NgModuleFactory, Injector, Compiler, Inject, signal, computed, effect, Optional, isDevMode, SkipSelf, Directive, EventEmitter, Output, Self, Pipe, NgModule, LOCALE_ID, SecurityContext, APP_INITIALIZER, makeEnvironmentProviders, ElementRef, HostListener, ComponentFactoryResolver, ApplicationRef } from '@angular/core';\nimport { of, BehaviorSubject, Subject, throwError, firstValueFrom, lastValueFrom, Observable, timer, pipe, concat, ReplaySubject, map as map$1, Subscription, combineLatest, from, fromEvent } from 'rxjs';\nimport * as i1$1 from '@angular/router';\nimport { PRIMARY_OUTLET, NavigationStart, NavigationError, NavigationEnd, NavigationCancel, Router, TitleStrategy, ActivatedRoute, RouterModule } from '@angular/router';\nimport * as i1$2 from '@angular/common';\nimport { DOCUMENT, registerLocaleData, DatePipe, DATE_PIPE_DEFAULT_TIMEZONE, CommonModule } from '@angular/common';\nimport { map, distinctUntilChanged, filter, catchError, tap, take, switchMap, mapTo, takeUntil, delay, retryWhen, shareReplay, debounceTime, finalize } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient, HttpContextToken, HttpContext, HttpParams, HttpErrorResponse, provideHttpClient, withInterceptorsFromDi, withXsrfConfiguration, HttpHeaders } from '@angular/common/http';\nimport compare from 'just-compare';\nimport clone from 'just-clone';\nimport { toSignal } from '@angular/core/rxjs-interop';\nimport { Title, DomSanitizer } from '@angular/platform-browser';\nimport * as i1$3 from '@angular/forms';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n\n// Not an abstract class on purpose. Do not change!\nfunction DynamicLayoutComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngComponentOutlet\", ctx_r0.layout);\n  }\n}\nfunction ReplaceableRouteContainerComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nclass AbstractNgModelComponent {\n  constructor() {\n    this.cdRef = inject(ChangeDetectorRef);\n    this.valueFn = value => value;\n    this.valueLimitFn = value => false;\n  }\n  set value(value) {\n    value = this.valueFn(value, this._value);\n    if (this.valueLimitFn(value, this._value) !== false || this.readonly) return;\n    this._value = value;\n    this.notifyValueChange();\n  }\n  get value() {\n    return this._value || this.defaultValue;\n  }\n  get defaultValue() {\n    return this._value;\n  }\n  notifyValueChange() {\n    if (this.onChange) {\n      this.onChange(this.value);\n    }\n  }\n  writeValue(value) {\n    this._value = this.valueLimitFn(value, this._value) || value;\n    this.cdRef.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  static {\n    this.ɵfac = function AbstractNgModelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbstractNgModelComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AbstractNgModelComponent,\n      selectors: [[\"ng-component\"]],\n      inputs: {\n        disabled: \"disabled\",\n        readonly: \"readonly\",\n        valueFn: \"valueFn\",\n        valueLimitFn: \"valueLimitFn\",\n        value: \"value\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function AbstractNgModelComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractNgModelComponent, [{\n    type: Component,\n    args: [{\n      template: ''\n    }]\n  }], null, {\n    disabled: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    valueFn: [{\n      type: Input\n    }],\n    valueLimitFn: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * @deprecated Use `authGuard` *function* instead.\n */\nclass AuthGuard {\n  canActivate() {\n    console.error('You should add @abp/ng-oauth packages or create your own auth packages.');\n    return false;\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst authGuard = () => {\n  console.error('You should add @abp/ng-oauth packages or create your own auth packages.');\n  return false;\n};\n\n/**\n * Abstract service for Authentication.\n */\nclass AuthService {\n  warningMessage() {\n    console.error('You should add @abp/ng-oauth packages or create your own auth packages.');\n  }\n  get oidc() {\n    this.warningMessage();\n    return false;\n  }\n  set oidc(value) {\n    this.warningMessage();\n  }\n  init() {\n    this.warningMessage();\n    return Promise.resolve(undefined);\n  }\n  login(params) {\n    this.warningMessage();\n    return of(undefined);\n  }\n  logout(queryParams) {\n    this.warningMessage();\n    return of(undefined);\n  }\n  navigateToLogin(queryParams) {}\n  get isInternalAuth() {\n    throw new Error('not implemented');\n  }\n  get isAuthenticated() {\n    this.warningMessage();\n    return false;\n  }\n  loginUsingGrant(grantType, parameters, headers) {\n    console.log({\n      grantType,\n      parameters,\n      headers\n    });\n    return Promise.reject(new Error('not implemented'));\n  }\n  getAccessTokenExpiration() {\n    this.warningMessage();\n    return 0;\n  }\n  getRefreshToken() {\n    this.warningMessage();\n    return '';\n  }\n  getAccessToken() {\n    this.warningMessage();\n    return '';\n  }\n  refreshToken() {\n    this.warningMessage();\n    return Promise.resolve(undefined);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass AbstractAuthErrorFilter {}\nclass AuthErrorFilterService extends AbstractAuthErrorFilter {\n  warningMessage() {\n    console.error('You should add @abp/ng-oauth packages or create your own auth packages.');\n  }\n  get(id) {\n    this.warningMessage();\n    throw new Error('not implemented');\n  }\n  add(filter) {\n    this.warningMessage();\n  }\n  patch(item) {\n    this.warningMessage();\n  }\n  remove(id) {\n    this.warningMessage();\n  }\n  run(event) {\n    this.warningMessage();\n    throw new Error('not implemented');\n  }\n}\nconst LOCALIZATIONS = new InjectionToken('LOCALIZATIONS');\nfunction localizationContributor(localizations) {\n  if (localizations) {\n    localizations$.next([...localizations$.value, ...localizations]);\n  }\n}\nconst localizations$ = new BehaviorSubject([]);\nconst CORE_OPTIONS = new InjectionToken('CORE_OPTIONS');\nfunction coreOptionsFactory({\n  ...options\n}) {\n  return {\n    ...options\n  };\n}\n\n// This will not be necessary when only Angukar 9.1+ is supported\nfunction getLocaleDirection(locale) {\n  return /^(ar(-[A-Z]{2})?|ckb(-IR)?|fa(-AF)?|he|ks|lrc(-IQ)?|mzn|pa-Arab|ps(-PK)?|sd|ug|ur(-IN)?|uz-Arab|yi)$/.test(locale) ? 'rtl' : 'ltr';\n}\nfunction createLocalizer(localization) {\n  return (resourceName, key, defaultValue) => {\n    if (resourceName === '_') return key;\n    const resource = localization?.values?.[resourceName];\n    if (!resource) return defaultValue;\n    return resource[key] || defaultValue;\n  };\n}\nfunction createLocalizerWithFallback(localization) {\n  const findLocalization = createLocalizationFinder(localization);\n  return (resourceNames, keys, defaultValue) => {\n    const {\n      localized\n    } = findLocalization(resourceNames, keys);\n    return localized || defaultValue;\n  };\n}\nfunction createLocalizationPipeKeyGenerator(localization) {\n  const findLocalization = createLocalizationFinder(localization);\n  return (resourceNames, keys, defaultKey) => {\n    const {\n      resourceName,\n      key\n    } = findLocalization(resourceNames, keys);\n    return !resourceName ? defaultKey : resourceName === '_' ? key : `${resourceName}::${key}`;\n  };\n}\nfunction createLocalizationFinder(localization) {\n  const localize = createLocalizer(localization);\n  return (resourceNames, keys) => {\n    resourceNames = resourceNames.concat(localization.defaultResourceName || '').filter(Boolean);\n    const resourceCount = resourceNames.length;\n    const keyCount = keys.length;\n    for (let i = 0; i < resourceCount; i++) {\n      const resourceName = resourceNames[i];\n      for (let j = 0; j < keyCount; j++) {\n        const key = keys[j];\n        const localized = localize(resourceName, key, null);\n        if (localized) return {\n          resourceName,\n          key,\n          localized\n        };\n      }\n    }\n    return {\n      resourceName: undefined,\n      key: undefined,\n      localized: undefined\n    };\n  };\n}\nfunction createTokenParser(format) {\n  return str => {\n    const tokens = [];\n    const regex = format.replace(/\\./g, '\\\\.').replace(/\\{\\s?([0-9a-zA-Z]+)\\s?\\}/g, (_, token) => {\n      tokens.push(token);\n      return '(.+)';\n    });\n    const matches = (str.match(regex) || []).slice(1);\n    return matches.reduce((acc, v, i) => {\n      const key = tokens[i];\n      acc[key] = [...(acc[key] || []), v].filter(Boolean);\n      return acc;\n    }, {});\n  };\n}\nfunction interpolate(text, params) {\n  return text.replace(/(['\"]?\\{\\s*(\\d+)\\s*\\}['\"]?)/g, (_, match, digit) => params[digit] ?? match).replace(/\\s+/g, ' ');\n}\nfunction escapeHtmlChars(value) {\n  return value && typeof value === 'string' ? value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;') : value;\n}\nclass ContentProjectionService {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  projectContent(projectionStrategy, injector = this.injector) {\n    return projectionStrategy.injectContent(injector);\n  }\n  static {\n    this.ɵfac = function ContentProjectionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentProjectionService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentProjectionService,\n      factory: ContentProjectionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentProjectionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nfunction pushValueTo(array) {\n  return element => {\n    array.push(element);\n    return array;\n  };\n}\nfunction noop() {\n  const fn = function () {};\n  return fn;\n}\nfunction isUndefinedOrEmptyString(value) {\n  return value === undefined || value === '';\n}\nfunction isNullOrUndefined(obj) {\n  return obj === null || obj === undefined;\n}\nfunction isNullOrEmpty(obj) {\n  return obj === null || obj === undefined || obj === '';\n}\nfunction exists(obj) {\n  return !isNullOrUndefined(obj);\n}\nfunction isObject(obj) {\n  return obj instanceof Object;\n}\nfunction isArray(obj) {\n  return Array.isArray(obj);\n}\nfunction isObjectAndNotArray(obj) {\n  return isObject(obj) && !isArray(obj);\n}\nfunction isNode(obj) {\n  return obj instanceof Node;\n}\nfunction isObjectAndNotArrayNotNode(obj) {\n  return isObjectAndNotArray(obj) && !isNode(obj);\n}\nfunction checkHasProp(object, key) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\nfunction getShortDateFormat(configStateService) {\n  const dateTimeFormat = configStateService.getDeep('localization.currentCulture.dateTimeFormat');\n  return dateTimeFormat.shortDatePattern;\n}\nfunction getShortTimeFormat(configStateService) {\n  const dateTimeFormat = configStateService.getDeep('localization.currentCulture.dateTimeFormat');\n  return dateTimeFormat?.shortTimePattern?.replace('tt', 'a');\n}\nfunction getShortDateShortTimeFormat(configStateService) {\n  const dateTimeFormat = configStateService.getDeep('localization.currentCulture.dateTimeFormat');\n  return `${dateTimeFormat.shortDatePattern} ${dateTimeFormat?.shortTimePattern?.replace('tt', 'a')}`;\n}\nfunction deepMerge(target, source) {\n  if (isObjectAndNotArrayNotNode(target) && isObjectAndNotArrayNotNode(source)) {\n    return deepMergeRecursively(target, source);\n  } else if (isNullOrUndefined(target) && isNullOrUndefined(source)) {\n    return {};\n  } else {\n    return exists(source) ? source : target;\n  }\n}\nfunction deepMergeRecursively(target, source) {\n  const shouldNotRecurse = isNullOrUndefined(target) || isNullOrUndefined(source) ||\n  // at least one not defined\n  isArray(target) || isArray(source) ||\n  // at least one array\n  !isObject(target) || !isObject(source) ||\n  // at least one not an object\n  isNode(target) || isNode(source); // at least one node\n  /**\n   * if we will not recurse any further,\n   * we will prioritize source if it is a defined value.\n   */\n  if (shouldNotRecurse) {\n    return exists(source) ? source : target;\n  }\n  const keysOfTarget = Object.keys(target);\n  const keysOfSource = Object.keys(source);\n  const uniqueKeys = new Set(keysOfTarget.concat(keysOfSource));\n  return [...uniqueKeys].reduce((retVal, key) => {\n    retVal[key] = deepMergeRecursively(target[key], source[key]);\n    return retVal;\n  }, {});\n}\nclass InternalStore {\n  get state() {\n    return this.state$.value;\n  }\n  constructor(initialState) {\n    this.initialState = initialState;\n    this.state$ = new BehaviorSubject(this.initialState);\n    this.update$ = new Subject();\n    this.sliceState = (selector, compareFn = compare) => this.state$.pipe(map(selector), distinctUntilChanged(compareFn));\n    this.sliceUpdate = (selector, filterFn = x => x !== undefined) => this.update$.pipe(map(selector), filter(filterFn));\n  }\n  patch(state) {\n    let patchedState = state;\n    if (typeof state === 'object' && !Array.isArray(state)) {\n      patchedState = {\n        ...this.state,\n        ...state\n      };\n    }\n    this.state$.next(patchedState);\n    this.update$.next(patchedState);\n  }\n  deepPatch(state) {\n    // TODO: Strict improve deepMerge\n    this.state$.next(deepMerge(this.state, state));\n    this.update$.next(state);\n  }\n  set(state) {\n    this.state$.next(state);\n    this.update$.next(state);\n  }\n  reset() {\n    this.set(this.initialState);\n  }\n}\nconst mapToApiUrl = key => apis => (key && apis[key] || apis.default).url || apis.default.url;\nconst mapToIssuer = issuer => {\n  if (!issuer) {\n    return issuer;\n  }\n  return issuer.endsWith('/') ? issuer : issuer + '/';\n};\nclass EnvironmentService {\n  constructor() {\n    this.store = new InternalStore({});\n  }\n  get createOnUpdateStream() {\n    return this.store.sliceUpdate;\n  }\n  getEnvironment$() {\n    return this.store.sliceState(state => state);\n  }\n  getEnvironment() {\n    return this.store.state;\n  }\n  getApiUrl(key) {\n    return mapToApiUrl(key)(this.store.state?.apis);\n  }\n  getApiUrl$(key) {\n    return this.store.sliceState(state => state.apis).pipe(map(mapToApiUrl(key)));\n  }\n  setState(environment) {\n    this.store.set(environment);\n  }\n  getIssuer() {\n    const issuer = this.store.state?.oAuthConfig?.issuer;\n    return mapToIssuer(issuer);\n  }\n  getIssuer$() {\n    return this.store.sliceState(state => state?.oAuthConfig?.issuer).pipe(map(mapToIssuer));\n  }\n  getImpersonation() {\n    return this.store.state?.oAuthConfig?.impersonation || {};\n  }\n  getImpersonation$() {\n    return this.store.sliceState(state => state?.oAuthConfig?.impersonation || {});\n  }\n  static {\n    this.ɵfac = function EnvironmentService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EnvironmentService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EnvironmentService,\n      factory: EnvironmentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EnvironmentService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass HttpErrorReporterService {\n  constructor() {\n    this._reporter$ = new Subject();\n    this._errors$ = new BehaviorSubject([]);\n  }\n  get reporter$() {\n    return this._reporter$.asObservable();\n  }\n  get errors$() {\n    return this._errors$.asObservable();\n  }\n  get errors() {\n    return this._errors$.value;\n  }\n  reportError(error) {\n    this._reporter$.next(error);\n    this._errors$.next([...this.errors, error]);\n  }\n  static {\n    this.ɵfac = function HttpErrorReporterService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpErrorReporterService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpErrorReporterService,\n      factory: HttpErrorReporterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpErrorReporterService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction getRemoteEnv(injector, environment) {\n  const environmentService = injector.get(EnvironmentService);\n  const {\n    remoteEnv\n  } = environment;\n  const {\n    headers = {},\n    method = 'GET',\n    url\n  } = remoteEnv || {};\n  if (!url) return Promise.resolve();\n  const http = injector.get(HttpClient);\n  const httpErrorReporter = injector.get(HttpErrorReporterService);\n  return http.request(method, url, {\n    headers\n  }).pipe(catchError(err => {\n    httpErrorReporter.reportError(err);\n    return of(null);\n  }),\n  // TODO: Consider get handle function from a provider\n  tap(env => environmentService.setState(mergeEnvironments(environment, env || {}, remoteEnv)))).toPromise();\n}\nfunction mergeEnvironments(local, remote, config) {\n  switch (config.mergeStrategy) {\n    case 'deepmerge':\n      return deepMerge(local, remote);\n    case 'overwrite':\n    case null:\n    case undefined:\n      return remote;\n    default:\n      return config.mergeStrategy(local, remote);\n  }\n}\nclass LazyModuleFactory extends NgModuleFactory {\n  get moduleType() {\n    return this.moduleWithProviders.ngModule;\n  }\n  constructor(moduleWithProviders) {\n    super();\n    this.moduleWithProviders = moduleWithProviders;\n  }\n  create(parentInjector) {\n    const injector = Injector.create({\n      ...(parentInjector && {\n        parent: parentInjector\n      }),\n      providers: this.moduleWithProviders.providers\n    });\n    const compiler = injector.get(Compiler);\n    const factory = compiler.compileModuleSync(this.moduleType);\n    return factory.create(injector);\n  }\n}\nfunction featuresFactory(configState, featureKeys, mapFn = features => features) {\n  return configState.getFeatures$(featureKeys).pipe(filter(Boolean), map(mapFn));\n}\n\n/** @deprecated the method will change in v8.0 */\nfunction downloadBlob(blob, filename) {\n  const blobUrl = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = blobUrl;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.dispatchEvent(new MouseEvent('click', {\n    bubbles: true,\n    cancelable: true,\n    view: window\n  }));\n  document.body.removeChild(link);\n}\nfunction isNumber(value) {\n  return value == Number(value);\n}\nfunction mapEnumToOptions(_enum) {\n  const options = [];\n  for (const member in _enum) if (!isNumber(member)) options.push({\n    key: member,\n    value: _enum[member]\n  });\n  return options;\n}\nfunction uuid(a) {\n  return a ? (a ^ Math.random() * 16 >> a / 4).toString(16) : ('' + 1e7 + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, uuid);\n}\nfunction generateHash(value) {\n  let hashed = 0;\n  let charCode;\n  for (let i = 0; i < value.length; i++) {\n    charCode = value.charCodeAt(i);\n    hashed = (hashed << 5) - hashed + charCode;\n    hashed |= 0;\n  }\n  return hashed;\n}\nfunction generatePassword(injector, length = 8) {\n  if (injector) {\n    length = getRequiredPasswordLength(injector);\n  }\n  length = Math.min(Math.max(4, length), 128);\n  const lowers = 'abcdefghjkmnpqrstuvwxyz';\n  const uppers = 'ABCDEFGHJKMNPQRSTUVWXYZ';\n  const numbers = '23456789';\n  const specials = '!*_#/+-.';\n  const all = lowers + uppers + numbers + specials;\n  const getRandom = chrSet => chrSet[Math.floor(Math.random() * chrSet.length)];\n  const password = Array({\n    length\n  });\n  password[0] = getRandom(lowers);\n  password[1] = getRandom(uppers);\n  password[2] = getRandom(numbers);\n  password[3] = getRandom(specials);\n  for (let i = 4; i < length; i++) {\n    password[i] = getRandom(all);\n  }\n  return password.sort(() => 0.5 - Math.random()).join('');\n}\nfunction getRequiredPasswordLength(injector) {\n  const configState = injector.get(ConfigStateService);\n  const passwordRules = configState.getSettings('Identity.Password');\n  return Number(passwordRules['Abp.Identity.Password.RequiredLength']) || 8;\n}\nfunction getPathName(url) {\n  const {\n    pathname\n  } = new URL(url, window.location.origin);\n  return pathname;\n}\nclass WebHttpUrlEncodingCodec {\n  encodeKey(k) {\n    return encodeURIComponent(k);\n  }\n  encodeValue(v) {\n    return encodeURIComponent(v);\n  }\n  decodeKey(k) {\n    return decodeURIComponent(k);\n  }\n  decodeValue(v) {\n    return decodeURIComponent(v);\n  }\n}\nclass AbpLocalStorageService {\n  constructor() {}\n  get length() {\n    return localStorage.length;\n  }\n  clear() {\n    localStorage.clear();\n  }\n  getItem(key) {\n    return localStorage.getItem(key);\n  }\n  key(index) {\n    return localStorage.key(index);\n  }\n  removeItem(key) {\n    localStorage.removeItem(key);\n  }\n  setItem(key, value) {\n    localStorage.setItem(key, value);\n  }\n  static {\n    this.ɵfac = function AbpLocalStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpLocalStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpLocalStorageService,\n      factory: AbpLocalStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpLocalStorageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass SessionStateService {\n  constructor(configState, localStorageService) {\n    this.configState = configState;\n    this.localStorageService = localStorageService;\n    this.store = new InternalStore({});\n    this.document = inject(DOCUMENT);\n    this.updateLocalStorage = () => {\n      this.localStorageService.setItem('abpSession', JSON.stringify(this.store.state));\n    };\n    this.init();\n    this.setInitialLanguage();\n  }\n  init() {\n    const session = this.localStorageService.getItem('abpSession');\n    if (session) {\n      this.store.set(JSON.parse(session));\n    }\n    this.store.sliceUpdate(state => state).subscribe(this.updateLocalStorage);\n  }\n  setInitialLanguage() {\n    const appLanguage = this.getLanguage();\n    this.configState.getDeep$('localization.currentCulture.cultureName').pipe(filter(cultureName => !!cultureName), take(1)).subscribe(lang => {\n      if (lang.includes(';')) {\n        lang = lang.split(';')[0];\n      }\n      this.setLanguage(lang);\n    });\n  }\n  onLanguageChange$() {\n    return this.store.sliceUpdate(state => state.language);\n  }\n  onTenantChange$() {\n    return this.store.sliceUpdate(state => state.tenant);\n  }\n  getLanguage() {\n    return this.store.state.language;\n  }\n  getLanguage$() {\n    return this.store.sliceState(state => state.language);\n  }\n  getTenant() {\n    return this.store.state.tenant;\n  }\n  getTenant$() {\n    return this.store.sliceState(state => state.tenant);\n  }\n  setTenant(tenant) {\n    if (compare(tenant, this.store.state.tenant)) return;\n    this.store.set({\n      ...this.store.state,\n      tenant\n    });\n  }\n  setLanguage(language) {\n    const currentLanguage = this.store.state.language;\n    if (language !== currentLanguage) {\n      this.store.patch({\n        language\n      });\n    }\n    const currentAttribute = this.document.documentElement.getAttribute('lang');\n    if (language !== currentAttribute) {\n      this.document.documentElement.setAttribute('lang', language);\n    }\n  }\n  static {\n    this.ɵfac = function SessionStateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SessionStateService)(i0.ɵɵinject(ConfigStateService), i0.ɵɵinject(AbpLocalStorageService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SessionStateService,\n      factory: SessionStateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SessionStateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: ConfigStateService\n  }, {\n    type: AbpLocalStorageService\n  }], null);\n})();\nconst APP_INIT_ERROR_HANDLERS = new InjectionToken('APP_INIT_ERROR_HANDLERS');\nclass AbpTenantService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'abp';\n    this.findTenantById = (id, config) => this.restService.request({\n      method: 'GET',\n      url: `/api/abp/multi-tenancy/tenants/by-id/${id}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.findTenantByName = (name, config) => this.restService.request({\n      method: 'GET',\n      url: `/api/abp/multi-tenancy/tenants/by-name/${name}`\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function AbpTenantService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpTenantService)(i0.ɵɵinject(RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpTenantService,\n      factory: AbpTenantService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpTenantService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RestService\n  }], null);\n})();\nconst TENANT_KEY = new InjectionToken('TENANT_KEY');\nconst IS_EXTERNAL_REQUEST = new HttpContextToken(() => false);\n\n// source : https://github.com/armanozak/demo-angular-server-specific-interceptors\nclass ExternalHttpClient extends HttpClient {\n  request(first, url, options = {}) {\n    if (typeof first === 'string') {\n      this.#setPlaceholderContext(options);\n      return super.request(first, url || '', options);\n    }\n    this.#setPlaceholderContext(first);\n    return super.request(first);\n  }\n  #setPlaceholderContext(optionsOrRequest) {\n    optionsOrRequest.context ??= new HttpContext();\n    optionsOrRequest.context.set(IS_EXTERNAL_REQUEST, true);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵExternalHttpClient_BaseFactory;\n      return function ExternalHttpClient_Factory(__ngFactoryType__) {\n        return (ɵExternalHttpClient_BaseFactory || (ɵExternalHttpClient_BaseFactory = i0.ɵɵgetInheritedFactory(ExternalHttpClient)))(__ngFactoryType__ || ExternalHttpClient);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ExternalHttpClient,\n      factory: ExternalHttpClient.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ExternalHttpClient, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RestService {\n  constructor(options, http, externalHttp, environment, httpErrorReporter) {\n    this.options = options;\n    this.http = http;\n    this.externalHttp = externalHttp;\n    this.environment = environment;\n    this.httpErrorReporter = httpErrorReporter;\n  }\n  getApiFromStore(apiName) {\n    return this.environment.getApiUrl(apiName);\n  }\n  handleError(err) {\n    this.httpErrorReporter.reportError(err);\n    return throwError(() => err);\n  }\n  request(request, config, api) {\n    config = config || {};\n    api = api || this.getApiFromStore(config.apiName);\n    const {\n      method,\n      params,\n      ...options\n    } = request;\n    const {\n      observe = \"body\" /* Rest.Observe.Body */,\n      skipHandleError\n    } = config;\n    const url = this.removeDuplicateSlashes(api + request.url);\n    const httpClient = this.getHttpClient(config.skipAddingHeader);\n    return httpClient.request(method, url, {\n      observe,\n      ...(params && {\n        params: this.getParams(params, config.httpParamEncoder)\n      }),\n      ...options\n    }).pipe(catchError(err => skipHandleError ? throwError(() => err) : this.handleError(err)));\n  }\n  getHttpClient(isExternal) {\n    return isExternal ? this.externalHttp : this.http;\n  }\n  getParams(params, encoder) {\n    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {\n      if (isUndefinedOrEmptyString(value)) return acc;\n      if (value === null && !this.options.sendNullsAsQueryParam) return acc;\n      acc[key] = value;\n      return acc;\n    }, {});\n    return encoder ? new HttpParams({\n      encoder,\n      fromObject: filteredParams\n    }) : new HttpParams({\n      fromObject: filteredParams\n    });\n  }\n  removeDuplicateSlashes(url) {\n    return url.replace(/([^:]\\/)\\/+/g, '$1');\n  }\n  static {\n    this.ɵfac = function RestService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RestService)(i0.ɵɵinject(CORE_OPTIONS), i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(ExternalHttpClient), i0.ɵɵinject(EnvironmentService), i0.ɵɵinject(HttpErrorReporterService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RestService,\n      factory: RestService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RestService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CORE_OPTIONS]\n    }]\n  }, {\n    type: i1.HttpClient\n  }, {\n    type: ExternalHttpClient\n  }, {\n    type: EnvironmentService\n  }, {\n    type: HttpErrorReporterService\n  }], null);\n})();\nclass MultiTenancyService {\n  constructor(restService, sessionState, tenantService, configStateService, tenantKey) {\n    this.restService = restService;\n    this.sessionState = sessionState;\n    this.tenantService = tenantService;\n    this.configStateService = configStateService;\n    this.tenantKey = tenantKey;\n    this.domainTenant = null;\n    this.isTenantBoxVisible = true;\n    this.apiName = 'abp';\n    this.setTenantToState = tenant => {\n      this.sessionState.setTenant({\n        id: tenant.tenantId,\n        name: tenant.name,\n        isAvailable: true\n      });\n      return this.configStateService.refreshAppState().pipe(map(_ => tenant));\n    };\n  }\n  setTenantByName(tenantName) {\n    return this.tenantService.findTenantByName(tenantName).pipe(switchMap(this.setTenantToState));\n  }\n  setTenantById(tenantId) {\n    return this.tenantService.findTenantById(tenantId).pipe(switchMap(this.setTenantToState));\n  }\n  static {\n    this.ɵfac = function MultiTenancyService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MultiTenancyService)(i0.ɵɵinject(RestService), i0.ɵɵinject(SessionStateService), i0.ɵɵinject(AbpTenantService), i0.ɵɵinject(ConfigStateService), i0.ɵɵinject(TENANT_KEY));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MultiTenancyService,\n      factory: MultiTenancyService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MultiTenancyService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RestService\n  }, {\n    type: SessionStateService\n  }, {\n    type: AbpTenantService\n  }, {\n    type: ConfigStateService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TENANT_KEY]\n    }]\n  }], null);\n})();\nconst TENANT_NOT_FOUND_BY_NAME = new InjectionToken('TENANT_NOT_FOUND_BY_NAME');\nconst tenancyPlaceholder = '{0}';\nfunction getCurrentTenancyName(appBaseUrl) {\n  if (appBaseUrl.charAt(appBaseUrl.length - 1) !== '/') appBaseUrl += '/';\n  const parseTokens = createTokenParser(appBaseUrl);\n  const token = tenancyPlaceholder.replace(/[}{]/g, '');\n  return parseTokens(window.location.href)[token]?.[0];\n}\nfunction getCurrentTenancyNameFromUrl(tenantKey) {\n  const urlParams = new URLSearchParams(window.location.search);\n  return urlParams.get(tenantKey);\n}\nasync function parseTenantFromUrl(injector) {\n  const environmentService = injector.get(EnvironmentService);\n  const multiTenancyService = injector.get(MultiTenancyService);\n  const tenantNotFoundHandler = injector.get(TENANT_NOT_FOUND_BY_NAME, null);\n  const baseUrl = environmentService.getEnvironment()?.application?.baseUrl || '';\n  const tenancyName = getCurrentTenancyName(baseUrl);\n  const hideTenantBox = () => {\n    multiTenancyService.isTenantBoxVisible = false;\n  };\n  const setDomainTenant = tenant => {\n    multiTenancyService.domainTenant = {\n      id: tenant.tenantId,\n      name: tenant.name,\n      isAvailable: true\n    };\n  };\n  const setEnvironmentWithDomainTenant = tenant => {\n    hideTenantBox();\n    setDomainTenant(tenant);\n  };\n  if (tenancyName) {\n    /**\n     * We have to replace tenant name within the urls from environment,\n     * because the code below will make a http request to find information about the domain tenant.\n     * Before this request takes place, we need to replace placeholders aka \"{0}\".\n     */\n    replaceTenantNameWithinEnvironment(injector, tenancyName);\n    const tenant$ = multiTenancyService.setTenantByName(tenancyName);\n    try {\n      const result = await firstValueFrom(tenant$);\n      setEnvironmentWithDomainTenant(result);\n      return Promise.resolve(result);\n    } catch (httpError) {\n      if (httpError instanceof HttpErrorResponse && httpError.status === 404 && tenantNotFoundHandler) {\n        tenantNotFoundHandler(httpError);\n      }\n      return Promise.reject();\n    }\n  }\n  /**\n   * If there is no tenant, we still have to clean up {0}. from baseUrl to avoid incorrect http requests.\n   */\n  replaceTenantNameWithinEnvironment(injector, '', tenancyPlaceholder + '.');\n  const tenantIdFromQueryParams = getCurrentTenancyNameFromUrl(multiTenancyService.tenantKey);\n  if (tenantIdFromQueryParams) {\n    const tenantById$ = multiTenancyService.setTenantById(tenantIdFromQueryParams);\n    return firstValueFrom(tenantById$);\n  }\n  return Promise.resolve();\n}\nfunction replaceTenantNameWithinEnvironment(injector, tenancyName, placeholder = tenancyPlaceholder) {\n  const environmentService = injector.get(EnvironmentService);\n  const environment = clone(environmentService.getEnvironment());\n  if (environment.application.baseUrl) {\n    environment.application.baseUrl = environment.application.baseUrl.replace(placeholder, tenancyName);\n  }\n  if (environment.oAuthConfig?.redirectUri) {\n    environment.oAuthConfig.redirectUri = environment.oAuthConfig.redirectUri.replace(placeholder, tenancyName);\n  }\n  if (!environment.oAuthConfig) {\n    environment.oAuthConfig = {};\n  }\n  environment.oAuthConfig.issuer = (environment.oAuthConfig.issuer || '').replace(placeholder, tenancyName);\n  Object.keys(environment.apis).forEach(api => {\n    Object.keys(environment.apis[api]).forEach(key => {\n      environment.apis[api][key] = (environment.apis[api][key] || '').replace(placeholder, tenancyName);\n    });\n  });\n  return environmentService.setState(environment);\n}\nconst CHECK_AUTHENTICATION_STATE_FN_KEY = new InjectionToken('CHECK_AUTHENTICATION_STATE_FN_KEY');\nfunction getInitialData(injector) {\n  const fn = async () => {\n    const environmentService = injector.get(EnvironmentService);\n    const configState = injector.get(ConfigStateService);\n    const options = injector.get(CORE_OPTIONS);\n    environmentService.setState(options.environment);\n    await getRemoteEnv(injector, options.environment);\n    await parseTenantFromUrl(injector);\n    const authService = injector.get(AuthService, undefined, {\n      optional: true\n    });\n    const checkAuthenticationState = injector.get(CHECK_AUTHENTICATION_STATE_FN_KEY, noop, {\n      optional: true\n    });\n    if (!options.skipInitAuthService && authService) {\n      await authService.init();\n    }\n    if (options.skipGetAppConfiguration) return;\n    const result$ = configState.refreshAppState().pipe(tap(() => checkAuthenticationState(injector)), tap(() => {\n      const currentTenant = configState.getOne('currentTenant');\n      injector.get(SessionStateService).setTenant(currentTenant);\n    }), catchError(error => {\n      const appInitErrorHandlers = injector.get(APP_INIT_ERROR_HANDLERS, null);\n      if (appInitErrorHandlers && appInitErrorHandlers.length) {\n        appInitErrorHandlers.forEach(func => func(error));\n      }\n      return throwError(error);\n    }));\n    await lastValueFrom(result$);\n  };\n  return fn;\n}\nfunction localeInitializer(injector) {\n  const fn = () => {\n    const sessionState = injector.get(SessionStateService);\n    const {\n      registerLocaleFn\n    } = injector.get(CORE_OPTIONS);\n    const lang = sessionState.getLanguage() || 'en';\n    return new Promise((resolve, reject) => {\n      registerLocaleFn(lang).then(module => {\n        if (module?.default) registerLocaleData(module.default);\n        return resolve('resolved');\n      }, reject);\n    });\n  };\n  return fn;\n}\nclass CrossOriginStrategy {\n  constructor(crossorigin, integrity) {\n    this.crossorigin = crossorigin;\n    this.integrity = integrity;\n  }\n  setCrossOrigin(element) {\n    if (this.integrity) element.setAttribute('integrity', this.integrity);\n    if (this.crossorigin) {\n      element.setAttribute('crossorigin', this.crossorigin);\n    }\n  }\n}\nclass NoCrossOriginStrategy extends CrossOriginStrategy {\n  setCrossOrigin() {}\n}\nconst CROSS_ORIGIN_STRATEGY = {\n  Anonymous(integrity) {\n    return new CrossOriginStrategy('anonymous', integrity);\n  },\n  UseCredentials(integrity) {\n    return new CrossOriginStrategy('use-credentials', integrity);\n  },\n  None() {\n    return new NoCrossOriginStrategy(null);\n  }\n};\nclass DomStrategy {\n  constructor(target = document.head, position = 'beforeend') {\n    this.target = target;\n    this.position = position;\n  }\n  insertElement(element) {\n    this.target.insertAdjacentElement(this.position, element);\n  }\n}\nconst DOM_STRATEGY = {\n  AfterElement(element) {\n    return new DomStrategy(element, 'afterend');\n  },\n  AppendToBody() {\n    return new DomStrategy(document.body, 'beforeend');\n  },\n  AppendToHead() {\n    return new DomStrategy(document.head, 'beforeend');\n  },\n  BeforeElement(element) {\n    return new DomStrategy(element, 'beforebegin');\n  },\n  PrependToHead() {\n    return new DomStrategy(document.head, 'afterbegin');\n  }\n};\nfunction fromLazyLoad(element, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {\n  crossOriginStrategy.setCrossOrigin(element);\n  domStrategy.insertElement(element);\n  return new Observable(observer => {\n    element.onload = event => {\n      clearCallbacks(element);\n      observer.next(event);\n      observer.complete();\n    };\n    const handleError = createErrorHandler(observer, element);\n    element.onerror = handleError;\n    element.onabort = handleError;\n    element.onemptied = handleError;\n    element.onstalled = handleError;\n    element.onsuspend = handleError;\n    return () => {\n      clearCallbacks(element);\n      observer.complete();\n    };\n  });\n}\nfunction createErrorHandler(observer, element) {\n  return function (event) {\n    clearCallbacks(element);\n    element.parentNode?.removeChild(element);\n    observer.error(event);\n  };\n}\nfunction clearCallbacks(element) {\n  element.onload = null;\n  element.onerror = null;\n  element.onabort = null;\n  element.onemptied = null;\n  element.onstalled = null;\n  element.onsuspend = null;\n}\nclass DefaultQueueManager {\n  constructor() {\n    this.queue = [];\n    this.isRunning = false;\n    this.stack = 0;\n    this.interval = 0;\n    this.stackSize = 100;\n  }\n  init(interval, stackSize) {\n    this.interval = interval;\n    this.stackSize = stackSize;\n  }\n  add(fn) {\n    this.queue.push(fn);\n    this.run();\n  }\n  run() {\n    if (this.isRunning) return;\n    this.stack++;\n    this.isRunning = true;\n    const fn = this.queue.shift();\n    if (!fn) {\n      this.isRunning = false;\n      return;\n    }\n    fn();\n    if (this.stack > this.stackSize) {\n      setTimeout(() => {\n        this.isRunning = false;\n        this.run();\n        this.stack = 0;\n      }, this.interval);\n    } else {\n      this.isRunning = false;\n      this.run();\n    }\n  }\n}\nfunction findRoute(routesService, path) {\n  const node = routesService.find(route => route.path === path);\n  return node || path === '/' ? node : findRoute(routesService, path.split('/').slice(0, -1).join('/') || '/');\n}\nfunction getRoutePath(router, url = router.url) {\n  const emptyGroup = {\n    segments: []\n  };\n  const primaryGroup = router.parseUrl(url).root.children[PRIMARY_OUTLET];\n  return '/' + (primaryGroup || emptyGroup).segments.map(({\n    path\n  }) => path).join('/');\n}\nfunction reloadRoute(router, ngZone) {\n  const {\n    shouldReuseRoute\n  } = router.routeReuseStrategy;\n  const setRouteReuse = reuse => {\n    router.routeReuseStrategy.shouldReuseRoute = reuse;\n  };\n  setRouteReuse(() => false);\n  router.navigated = false;\n  ngZone.run(async () => {\n    await router.navigateByUrl(router.url).catch(noop);\n    setRouteReuse(shouldReuseRoute);\n  });\n}\n\n/* eslint-disable @typescript-eslint/ban-types */\nclass BaseTreeNode {\n  constructor(props) {\n    this.children = [];\n    this.isLeaf = true;\n    Object.assign(this, props);\n  }\n  static create(props) {\n    return new BaseTreeNode(props);\n  }\n}\nfunction createTreeFromList(list, keySelector, parentKeySelector, valueMapper) {\n  const map = createMapFromList(list, keySelector, valueMapper);\n  const tree = [];\n  list.forEach(row => {\n    const id = keySelector(row);\n    const parentId = parentKeySelector(row);\n    const node = map.get(id);\n    if (!node) return;\n    if (parentId) {\n      const parent = map.get(parentId);\n      if (!parent) return;\n      parent.children.push(node);\n      parent.isLeaf = false;\n      node.parent = parent;\n    } else {\n      tree.push(node);\n    }\n  });\n  return tree;\n}\nfunction createMapFromList(list, keySelector, valueMapper) {\n  const map = new Map();\n  list.forEach(row => map.set(keySelector(row), valueMapper(row)));\n  return map;\n}\nfunction createTreeNodeFilterCreator(key, mapperFn) {\n  return search => {\n    const regex = new RegExp('.*' + search + '.*', 'i');\n    return function collectNodes(nodes, matches = []) {\n      for (const node of nodes) {\n        if (regex.test(mapperFn(node[key]))) matches.push(node);\n        if (node.children.length) collectNodes(node.children, matches);\n      }\n      return matches;\n    };\n  };\n}\nfunction createGroupMap(list, othersGroupKey) {\n  if (!isArray(list) || !list.some(node => Boolean(node.group))) return undefined;\n  const mapGroup = new Map();\n  for (const node of list) {\n    const group = node?.group || othersGroupKey;\n    if (typeof group !== 'string') {\n      throw new Error(`Invalid group: ${group}`);\n    }\n    const items = mapGroup.get(group) || [];\n    items.push(node);\n    mapGroup.set(group, items);\n  }\n  return mapGroup;\n}\nclass DomInsertionService {\n  constructor() {\n    this.inserted = new Set();\n  }\n  insertContent(contentStrategy) {\n    const hash = generateHash(contentStrategy.content);\n    if (this.inserted.has(hash)) return;\n    const element = contentStrategy.insertElement();\n    this.inserted.add(hash);\n    return element;\n  }\n  removeContent(element) {\n    if (element.textContent) {\n      const hash = generateHash(element.textContent);\n      this.inserted.delete(hash);\n      element.parentNode?.removeChild(element);\n    }\n  }\n  has(content) {\n    const hash = generateHash(content);\n    return this.inserted.has(hash);\n  }\n  static {\n    this.ɵfac = function DomInsertionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomInsertionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomInsertionService,\n      factory: DomInsertionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomInsertionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst LOADER_DELAY = new InjectionToken('LOADER_DELAY');\nclass HttpWaitService {\n  constructor(injector) {\n    this.store = new InternalStore({\n      requests: [],\n      filteredRequests: []\n    });\n    this.destroy$ = new Subject();\n    this.delay = injector.get(LOADER_DELAY, 500);\n  }\n  getLoading() {\n    return !!this.applyFilter(this.store.state.requests).length;\n  }\n  getLoading$() {\n    return this.store.sliceState(({\n      requests\n    }) => requests).pipe(map(requests => !!this.applyFilter(requests).length), switchMap(condition => condition ? this.delay === 0 ? of(true) : timer(this.delay).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next()));\n  }\n  updateLoading$() {\n    return this.store.sliceUpdate(({\n      requests\n    }) => !!this.applyFilter(requests).length);\n  }\n  clearLoading() {\n    this.store.patch({\n      requests: []\n    });\n  }\n  addRequest(request) {\n    this.store.patch({\n      requests: [...this.store.state.requests, request]\n    });\n  }\n  deleteRequest(request) {\n    const requests = this.store.state.requests.filter(r => r !== request);\n    this.store.patch({\n      requests\n    });\n  }\n  addFilter(request) {\n    const requests = Array.isArray(request) ? request : [request];\n    const filteredRequests = [...this.store.state.filteredRequests.filter(f => !requests.some(r => this.isSameRequest(f, r))), ...requests];\n    this.store.patch({\n      filteredRequests\n    });\n  }\n  removeFilter(request) {\n    const requests = Array.isArray(request) ? request : [request];\n    const filteredRequests = this.store.state.filteredRequests.filter(f => !requests.some(r => this.isSameRequest(f, r)));\n    this.store.patch({\n      filteredRequests\n    });\n  }\n  applyFilter(requests) {\n    if (!requests) {\n      return [];\n    }\n    const {\n      filteredRequests\n    } = this.store.state;\n    return requests.filter(({\n      method,\n      url\n    }) => !filteredRequests.find(filteredRequest => this.isSameRequest(filteredRequest, {\n      method,\n      endpoint: getPathName(url)\n    })));\n  }\n  isSameRequest(filteredRequest, request) {\n    const {\n      method,\n      endpoint\n    } = filteredRequest;\n    return endpoint === request.endpoint && method === request.method;\n  }\n  static {\n    this.ɵfac = function HttpWaitService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpWaitService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpWaitService,\n      factory: HttpWaitService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpWaitService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nclass ResourceWaitService {\n  constructor() {\n    this.store = new InternalStore({\n      resources: new Set()\n    });\n  }\n  getLoading() {\n    return !!this.store.state.resources.size;\n  }\n  getLoading$() {\n    return this.store.sliceState(({\n      resources\n    }) => !!resources.size);\n  }\n  updateLoading$() {\n    return this.store.sliceUpdate(({\n      resources\n    }) => !!resources?.size);\n  }\n  clearLoading() {\n    this.store.patch({\n      resources: new Set()\n    });\n  }\n  addResource(resource) {\n    const resources = this.store.state.resources;\n    resources.add(resource);\n    this.store.patch({\n      resources\n    });\n  }\n  deleteResource(resource) {\n    const resources = this.store.state.resources;\n    resources.delete(resource);\n    this.store.patch({\n      resources\n    });\n  }\n  static {\n    this.ɵfac = function ResourceWaitService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResourceWaitService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResourceWaitService,\n      factory: ResourceWaitService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceWaitService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass LazyLoadService {\n  constructor(resourceWaitService) {\n    this.resourceWaitService = resourceWaitService;\n    this.loaded = new Map();\n  }\n  load(strategy, retryTimes, retryDelay) {\n    if (this.loaded.has(strategy.path)) return of(new CustomEvent('load'));\n    this.resourceWaitService.addResource(strategy.path);\n    const delayOperator = retryDelay ? pipe(delay(retryDelay)) : pipe();\n    const takeOp = retryTimes ? pipe(take(retryTimes)) : pipe();\n    return strategy.createStream().pipe(retryWhen(error$ => concat(error$.pipe(delayOperator, takeOp), throwError(() => new CustomEvent('error')))), tap(() => {\n      this.loaded.set(strategy.path, strategy.element);\n      this.resourceWaitService.deleteResource(strategy.path);\n    }), delay(100), shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n  }\n  remove(path) {\n    const element = this.loaded.get(path);\n    if (!element) return false;\n    element.parentNode?.removeChild(element);\n    this.loaded.delete(path);\n    return true;\n  }\n  static {\n    this.ɵfac = function LazyLoadService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LazyLoadService)(i0.ɵɵinject(ResourceWaitService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LazyLoadService,\n      factory: LazyLoadService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LazyLoadService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: ResourceWaitService\n  }], null);\n})();\nconst LIST_QUERY_DEBOUNCE_TIME = new InjectionToken('LIST_QUERY_DEBOUNCE_TIME');\nclass ListService {\n  set filter(value) {\n    this._filter = value;\n    this.get();\n  }\n  get filter() {\n    return this._filter;\n  }\n  set maxResultCount(value) {\n    this._maxResultCount = value;\n    this.get();\n  }\n  get maxResultCount() {\n    return this._maxResultCount;\n  }\n  set page(value) {\n    if (value === this._page) return;\n    this._page = value;\n    this.get();\n  }\n  get page() {\n    return this._page;\n  }\n  set totalCount(value) {\n    if (value === this._totalCount) return;\n    this._totalCount = value;\n    this.get();\n  }\n  get totalCount() {\n    return this._totalCount;\n  }\n  set sortKey(value) {\n    this._sortKey = value;\n    this.get();\n  }\n  get sortKey() {\n    return this._sortKey;\n  }\n  set sortOrder(value) {\n    this._sortOrder = value;\n    this.get();\n  }\n  get sortOrder() {\n    return this._sortOrder;\n  }\n  get query$() {\n    return this._query$.asObservable().pipe(this.delay, shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }));\n  }\n  get isLoading$() {\n    return this._isLoading$.asObservable();\n  }\n  constructor(injector) {\n    this._filter = '';\n    this._maxResultCount = 10;\n    this._skipCount = 0;\n    this._page = 0;\n    this._totalCount = 0;\n    this._sortKey = '';\n    this._sortOrder = '';\n    this._query$ = new ReplaySubject(1);\n    this._isLoading$ = new BehaviorSubject(false);\n    this.destroy$ = new Subject();\n    this.get = () => {\n      this.resetPageWhenUnchanged();\n      this.next();\n    };\n    this.getWithoutPageReset = () => {\n      this.next();\n    };\n    const delay = injector.get(LIST_QUERY_DEBOUNCE_TIME, 300);\n    this.delay = delay ? debounceTime(delay) : tap();\n    this.get();\n  }\n  hookToQuery(streamCreatorCallback) {\n    return this.query$.pipe(tap(() => this._isLoading$.next(true)), switchMap(query => streamCreatorCallback(query).pipe(catchError(() => of(null)))), filter(Boolean), tap(() => this._isLoading$.next(false)), shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }), takeUntil(this.destroy$));\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  resetPageWhenUnchanged() {\n    const maxPage = Number(Number(this.totalCount / this._maxResultCount).toFixed());\n    const skipCount = this._page * this._maxResultCount;\n    if (skipCount !== this._totalCount) {\n      this._skipCount = skipCount;\n      return;\n    }\n    if (this.page === maxPage && this.page > 0) {\n      this._skipCount = skipCount - this._maxResultCount;\n      this.page = this.page - 1;\n    }\n  }\n  next() {\n    this._query$.next({\n      filter: this._filter || undefined,\n      maxResultCount: this._maxResultCount,\n      skipCount: this._page * this._maxResultCount,\n      sorting: this._sortOrder ? `${this._sortKey} ${this._sortOrder}` : undefined\n    });\n  }\n  static {\n    this.ɵfac = function ListService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ListService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ListService,\n      factory: ListService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ListService, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nclass PermissionService {\n  constructor(configState) {\n    this.configState = configState;\n  }\n  getGrantedPolicy$(key) {\n    return this.getStream().pipe(map(grantedPolicies => this.isPolicyGranted(key, grantedPolicies)));\n  }\n  getGrantedPolicy(key) {\n    const policies = this.getSnapshot();\n    return this.isPolicyGranted(key, policies);\n  }\n  filterItemsByPolicy(items) {\n    const policies = this.getSnapshot();\n    return items.filter(item => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies));\n  }\n  filterItemsByPolicy$(items) {\n    return this.getStream().pipe(map(policies => items.filter(item => !item.requiredPolicy || this.isPolicyGranted(item.requiredPolicy, policies))));\n  }\n  isPolicyGranted(key, grantedPolicies) {\n    if (!key) return true;\n    const orRegexp = /\\|\\|/g;\n    const andRegexp = /&&/g;\n    // TODO: Allow combination of ANDs & ORs\n    if (orRegexp.test(key)) {\n      const keys = key.split('||').filter(Boolean);\n      if (keys.length < 2) return false;\n      return keys.some(k => this.getPolicy(k.trim(), grantedPolicies));\n    } else if (andRegexp.test(key)) {\n      const keys = key.split('&&').filter(Boolean);\n      if (keys.length < 2) return false;\n      return keys.every(k => this.getPolicy(k.trim(), grantedPolicies));\n    }\n    return this.getPolicy(key, grantedPolicies);\n  }\n  getStream() {\n    return this.configState.getAll$().pipe(map(this.mapToPolicies));\n  }\n  getSnapshot() {\n    return this.mapToPolicies(this.configState.getAll());\n  }\n  mapToPolicies(applicationConfiguration) {\n    return applicationConfiguration?.auth?.grantedPolicies || {};\n  }\n  getPolicy(key, grantedPolicies) {\n    return grantedPolicies[key] || false;\n  }\n  static {\n    this.ɵfac = function PermissionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermissionService)(i0.ɵɵinject(ConfigStateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PermissionService,\n      factory: PermissionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: ConfigStateService\n  }], null);\n})();\nclass ReplaceableComponentsService {\n  get replaceableComponents$() {\n    return this.store.sliceState(state => state);\n  }\n  get replaceableComponents() {\n    return this.store.state;\n  }\n  get onUpdate$() {\n    return this.store.sliceUpdate(state => state);\n  }\n  constructor(ngZone, router) {\n    this.ngZone = ngZone;\n    this.router = router;\n    this.store = new InternalStore([]);\n  }\n  add(replaceableComponent, reload) {\n    const replaceableComponents = [...this.store.state];\n    const index = replaceableComponents.findIndex(component => component.key === replaceableComponent.key);\n    if (index > -1) {\n      replaceableComponents[index] = replaceableComponent;\n    } else {\n      replaceableComponents.push(replaceableComponent);\n    }\n    this.store.set(replaceableComponents);\n    if (reload) reloadRoute(this.router, this.ngZone);\n  }\n  get(replaceableComponentKey) {\n    return this.replaceableComponents.find(component => component.key === replaceableComponentKey);\n  }\n  get$(replaceableComponentKey) {\n    return this.replaceableComponents$.pipe(map(components => components.find(component => component.key === replaceableComponentKey)));\n  }\n  static {\n    this.ɵfac = function ReplaceableComponentsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReplaceableComponentsService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1$1.Router));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ReplaceableComponentsService,\n      factory: ReplaceableComponentsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReplaceableComponentsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1$1.Router\n  }], null);\n})();\nconst NavigationEvent = {\n  Cancel: NavigationCancel,\n  End: NavigationEnd,\n  Error: NavigationError,\n  Start: NavigationStart\n};\nclass RouterEvents {\n  #previousNavigation;\n  #currentNavigation;\n  constructor() {\n    this.router = inject(Router);\n    this.#previousNavigation = signal(undefined);\n    this.previousNavigation = this.#previousNavigation.asReadonly();\n    this.#currentNavigation = signal(undefined);\n    this.currentNavigation = this.#currentNavigation.asReadonly();\n    this.listenToNavigation();\n  }\n  listenToNavigation() {\n    const routerEvent$ = this.router.events.pipe(filter(e => e instanceof NavigationEvent.End && !e.url.includes('error')));\n    routerEvent$.subscribe(event => {\n      this.#previousNavigation.set(this.currentNavigation());\n      this.#currentNavigation.set(event.url);\n    });\n  }\n  getEvents(...eventTypes) {\n    const filterRouterEvents = event => eventTypes.some(type => event instanceof type);\n    return this.router.events.pipe(filter(filterRouterEvents));\n  }\n  getNavigationEvents(...navigationEventKeys) {\n    const filterNavigationEvents = event => navigationEventKeys.some(key => event instanceof NavigationEvent[key]);\n    return this.router.events.pipe(filter(filterNavigationEvents));\n  }\n  getAllEvents() {\n    return this.router.events;\n  }\n  getAllNavigationEvents() {\n    const keys = Object.keys(NavigationEvent);\n    return this.getNavigationEvents(...keys);\n  }\n  static {\n    this.ɵfac = function RouterEvents_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterEvents)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterEvents,\n      factory: RouterEvents.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterEvents, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass RouterWaitService {\n  constructor(routerEvents, injector) {\n    this.routerEvents = routerEvents;\n    this.store = new InternalStore({\n      loading: false\n    });\n    this.destroy$ = new Subject();\n    this.delay = injector.get(LOADER_DELAY, 500);\n    this.updateLoadingStatusOnNavigationEvents();\n  }\n  updateLoadingStatusOnNavigationEvents() {\n    this.routerEvents.getAllNavigationEvents().pipe(map(event => event instanceof NavigationStart), switchMap(condition => condition ? this.delay === 0 ? of(true) : timer(this.delay || 0).pipe(mapTo(true), takeUntil(this.destroy$)) : of(false)), tap(() => this.destroy$.next())).subscribe(status => {\n      this.setLoading(status);\n    });\n  }\n  getLoading() {\n    return this.store.state.loading;\n  }\n  getLoading$() {\n    return this.store.sliceState(({\n      loading\n    }) => loading);\n  }\n  updateLoading$() {\n    return this.store.sliceUpdate(({\n      loading\n    }) => loading);\n  }\n  setLoading(loading) {\n    this.store.patch({\n      loading\n    });\n  }\n  static {\n    this.ɵfac = function RouterWaitService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterWaitService)(i0.ɵɵinject(RouterEvents), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterWaitService,\n      factory: RouterWaitService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterWaitService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RouterEvents\n  }, {\n    type: i0.Injector\n  }], null);\n})();\nconst COOKIE_LANGUAGE_KEY = new InjectionToken('COOKIE_LANGUAGE_KEY', {\n  factory: () => '.AspNetCore.Culture'\n});\nconst NAVIGATE_TO_MANAGE_PROFILE = new InjectionToken('NAVIGATE_TO_MANAGE_PROFILE');\nconst QUEUE_MANAGER = new InjectionToken(\"QUEUE_MANAGER\");\nconst INCUDE_LOCALIZATION_RESOURCES_TOKEN = new InjectionToken('INCUDE_LOCALIZATION_RESOURCES_TOKEN');\nconst PIPE_TO_LOGIN_FN_KEY = new InjectionToken('PIPE_TO_LOGIN_FN_KEY');\n\n/**\n * @deprecated The token should not be used anymore.\n */\nconst SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY = new InjectionToken('SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY');\nconst OTHERS_GROUP = new InjectionToken('OTHERS_GROUP');\nconst SORT_COMPARE_FUNC = new InjectionToken('SORT_COMPARE_FUNC');\nfunction compareFuncFactory() {\n  const localizationService = inject(LocalizationService);\n  const fn = (a, b) => {\n    const aNumber = a.order;\n    const bNumber = b.order;\n    if (aNumber > bNumber) return 1;\n    if (aNumber < bNumber) return -1;\n    if (a.id > b.id) return 1;\n    if (a.id < b.id) return -1;\n    if (!Number.isInteger(aNumber)) return 1;\n    if (!Number.isInteger(bNumber)) return -1;\n    const aName = localizationService.instant(a.name);\n    const bName = localizationService.instant(b.name);\n    if (aName > bName) return 1;\n    if (aName < bName) return -1;\n    return 0;\n  };\n  return fn;\n}\nconst DYNAMIC_LAYOUTS_TOKEN = new InjectionToken('DYNAMIC_LAYOUTS');\nconst DISABLE_PROJECT_NAME = new InjectionToken('DISABLE_APP_NAME');\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nclass AbstractTreeService {\n  constructor() {\n    this._flat$ = new BehaviorSubject([]);\n    this._tree$ = new BehaviorSubject([]);\n    this._visible$ = new BehaviorSubject([]);\n    this.shouldSingularizeRoutes = true;\n  }\n  get flat() {\n    return this._flat$.value;\n  }\n  get flat$() {\n    return this._flat$.asObservable();\n  }\n  get tree() {\n    return this._tree$.value;\n  }\n  get tree$() {\n    return this._tree$.asObservable();\n  }\n  get visible() {\n    return this._visible$.value;\n  }\n  get visible$() {\n    return this._visible$.asObservable();\n  }\n  filterWith(setOrMap) {\n    return this._flat$.value.filter(item => !setOrMap.has(item[this.id]));\n  }\n  findItemsToRemove(set) {\n    return this._flat$.value.reduce((acc, item) => {\n      if (!acc.has(item[this.parentId])) {\n        return acc;\n      }\n      const childSet = new Set([item[this.id]]);\n      const children = this.findItemsToRemove(childSet);\n      return new Set([...acc, ...children]);\n    }, set);\n  }\n  publish(flatItems) {\n    this._flat$.next(flatItems);\n    this._tree$.next(this.createTree(flatItems));\n    this._visible$.next(this.createTree(flatItems.filter(item => !this.hide(item))));\n    return flatItems;\n  }\n  createTree(items) {\n    return createTreeFromList(items, item => item[this.id], item => item[this.parentId], item => BaseTreeNode.create(item));\n  }\n  createGroupedTree(list) {\n    const map = createGroupMap(list, this.othersGroup);\n    if (!map) {\n      return undefined;\n    }\n    return Array.from(map, ([key, items]) => ({\n      group: key,\n      items\n    }));\n  }\n  add(items) {\n    let flatItems = [];\n    if (!this.shouldSingularizeRoutes) {\n      flatItems = [...this.flat, ...items];\n    }\n    if (this.shouldSingularizeRoutes) {\n      const map = new Map();\n      items.forEach(item => map.set(item[this.id], item));\n      flatItems = this.filterWith(map);\n      map.forEach(pushValueTo(flatItems));\n    }\n    flatItems.sort(this.sort);\n    return this.publish(flatItems);\n  }\n  find(predicate, tree = this.tree) {\n    return tree.reduce((acc, node) => {\n      if (acc) {\n        return acc;\n      }\n      if (predicate(node)) {\n        return node;\n      }\n      return this.find(predicate, node.children);\n    }, null);\n  }\n  patch(identifier, props) {\n    const flatItems = this._flat$.value;\n    const index = flatItems.findIndex(item => item[this.id] === identifier);\n    if (index < 0) {\n      return false;\n    }\n    flatItems[index] = {\n      ...flatItems[index],\n      ...props\n    };\n    flatItems.sort(this.sort);\n    return this.publish(flatItems);\n  }\n  refresh() {\n    return this.add([]);\n  }\n  remove(identifiers) {\n    const set = new Set();\n    identifiers.forEach(id => set.add(id));\n    const setToRemove = this.findItemsToRemove(set);\n    const flatItems = this.filterWith(setToRemove);\n    return this.publish(flatItems);\n  }\n  removeByParam(params) {\n    if (!params) {\n      return null;\n    }\n    const keys = Object.keys(params);\n    if (keys.length === 0) {\n      return null;\n    }\n    const excludedList = this.flat.filter(item => keys.every(key => item[key] === params[key]));\n    if (!excludedList?.length) {\n      return null;\n    }\n    for (const item of excludedList) {\n      this.removeByParam({\n        [this.parentId]: item[this.id]\n      });\n    }\n    const flatItems = this.flat.filter(item => !excludedList.includes(item));\n    return this.publish(flatItems);\n  }\n  search(params, tree = this.tree) {\n    const searchKeys = Object.keys(params);\n    return tree.reduce((acc, node) => {\n      if (acc) {\n        return acc;\n      }\n      if (searchKeys.every(key => node[key] === params[key])) {\n        return node;\n      }\n      return this.search(params, node.children);\n    }, null);\n  }\n  setSingularizeStatus(singularize = true) {\n    this.shouldSingularizeRoutes = singularize;\n  }\n}\nclass AbstractNavTreeService extends AbstractTreeService {\n  constructor(injector) {\n    super();\n    this.injector = injector;\n    this.id = 'name';\n    this.parentId = 'parentName';\n    this.hide = item => item.invisible || !this.isGranted(item);\n    this.sort = (a, b) => {\n      return this.compareFunc(a, b);\n    };\n    const configState = this.injector.get(ConfigStateService);\n    this.subscription = configState.createOnUpdateStream(state => state).subscribe(() => this.refresh());\n    this.permissionService = injector.get(PermissionService);\n    this.othersGroup = injector.get(OTHERS_GROUP);\n    this.compareFunc = injector.get(SORT_COMPARE_FUNC);\n  }\n  isGranted({\n    requiredPolicy\n  }) {\n    return this.permissionService.getGrantedPolicy(requiredPolicy);\n  }\n  hasChildren(identifier) {\n    const node = this.find(item => item[this.id] === identifier);\n    return Boolean(node?.children?.length);\n  }\n  hasInvisibleChild(identifier) {\n    const node = this.find(item => item[this.id] === identifier);\n    return node?.children?.some(child => child.invisible) || false;\n  }\n  /* istanbul ignore next */\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function AbstractNavTreeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbstractNavTreeService)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbstractNavTreeService,\n      factory: AbstractNavTreeService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractNavTreeService, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nclass RoutesService extends AbstractNavTreeService {\n  hasPathOrChild(item) {\n    return Boolean(item.path) || this.hasChildren(item.name);\n  }\n  get groupedVisible() {\n    return this.createGroupedTree(this.visible.filter(item => this.hasPathOrChild(item)));\n  }\n  get groupedVisible$() {\n    return this.visible$.pipe(map$1(items => items.filter(item => this.hasPathOrChild(item))), map$1(visible => this.createGroupedTree(visible)));\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵRoutesService_BaseFactory;\n      return function RoutesService_Factory(__ngFactoryType__) {\n        return (ɵRoutesService_BaseFactory || (ɵRoutesService_BaseFactory = i0.ɵɵgetInheritedFactory(RoutesService)))(__ngFactoryType__ || RoutesService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RoutesService,\n      factory: RoutesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutesService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass SubscriptionService {\n  constructor() {\n    this.subscription = new Subscription();\n  }\n  get isClosed() {\n    return this.subscription.closed;\n  }\n  addOne(source$, nextOrObserver, error) {\n    const subscription = source$.subscribe(nextOrObserver, error);\n    this.subscription.add(subscription);\n    return subscription;\n  }\n  closeAll() {\n    this.subscription.unsubscribe();\n  }\n  closeOne(subscription) {\n    this.removeOne(subscription);\n    if (subscription) {\n      subscription.unsubscribe();\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  removeOne(subscription) {\n    if (!subscription) return;\n    this.subscription.remove(subscription);\n  }\n  reset() {\n    this.subscription.unsubscribe();\n    this.subscription = new Subscription();\n  }\n  static {\n    this.ɵfac = function SubscriptionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SubscriptionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SubscriptionService,\n      factory: SubscriptionService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SubscriptionService, [{\n    type: Injectable\n  }], null, null);\n})();\nconst trackBy = key => (_, item) => item[key];\nconst trackByDeep = (\n// eslint-disable-next-line @typescript-eslint/ban-types\n...keys) => (_, item) => keys.reduce((acc, key) => acc[key], item);\nclass TrackByService {\n  constructor() {\n    this.by = trackBy;\n    this.byDeep = trackByDeep;\n  }\n  static {\n    this.ɵfac = function TrackByService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackByService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TrackByService,\n      factory: TrackByService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackByService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass AbpWindowService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.window = this.document.defaultView;\n    this.navigator = this.window.navigator;\n  }\n  copyToClipboard(text) {\n    return this.navigator.clipboard.writeText(text);\n  }\n  open(url, target, features) {\n    return this.window.open(url, target, features);\n  }\n  reloadPage() {\n    this.window.location.reload();\n  }\n  downloadBlob(blob, fileName) {\n    const blobUrl = this.window.URL.createObjectURL(blob);\n    const a = this.document.createElement('a');\n    a.style.display = 'none';\n    a.href = blobUrl;\n    a.download = fileName;\n    this.document.body.appendChild(a);\n    a.dispatchEvent(new MouseEvent('click', {\n      bubbles: true,\n      cancelable: true,\n      view: this.window\n    }));\n    this.window.URL.revokeObjectURL(blobUrl);\n    this.document.body.removeChild(a);\n  }\n  static {\n    this.ɵfac = function AbpWindowService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpWindowService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpWindowService,\n      factory: AbpWindowService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpWindowService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass InternetConnectionService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.window = this.document.defaultView;\n    this.navigator = this.window.navigator;\n    this.status$ = new BehaviorSubject(this.navigator.onLine);\n    this.status = signal(this.navigator.onLine);\n    this.networkStatus = computed(() => this.status());\n    this.window.addEventListener('offline', () => this.setStatus(false));\n    this.window.addEventListener('online', () => this.setStatus(true));\n  }\n  setStatus(val) {\n    this.status.set(val);\n    this.status$.next(val);\n  }\n  get networkStatus$() {\n    return this.status$.asObservable();\n  }\n  static {\n    this.ɵfac = function InternetConnectionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InternetConnectionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InternetConnectionService,\n      factory: InternetConnectionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InternetConnectionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass LocalStorageListenerService {\n  constructor() {\n    this.window = inject(DOCUMENT).defaultView;\n    this.window.addEventListener('storage', event => {\n      if (event.key === 'access_token' && event.newValue === null) {\n        this.window.location.reload();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LocalStorageListenerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalStorageListenerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LocalStorageListenerService,\n      factory: LocalStorageListenerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocalStorageListenerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass AbpTitleStrategy extends TitleStrategy {\n  constructor() {\n    super();\n    this.title = inject(Title);\n    this.localizationService = inject(LocalizationService);\n    this.disableProjectName = inject(DISABLE_PROJECT_NAME, {\n      optional: true\n    });\n    this.langugageChange = toSignal(this.localizationService.languageChange$);\n    effect(() => {\n      if (this.langugageChange()) {\n        this.updateTitle(this.routerState);\n      }\n    });\n  }\n  updateTitle(routerState) {\n    this.routerState = routerState;\n    const title = this.buildTitle(routerState);\n    const projectName = this.localizationService.instant({\n      key: '::AppName',\n      defaultValue: 'MyProjectName'\n    });\n    if (!title) {\n      return this.title.setTitle(projectName);\n    }\n    let localizedText = this.localizationService.instant({\n      key: title,\n      defaultValue: title\n    });\n    if (!this.disableProjectName) {\n      localizedText += ` | ${projectName}`;\n    }\n    this.title.setTitle(localizedText);\n  }\n  static {\n    this.ɵfac = function AbpTitleStrategy_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpTitleStrategy)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpTitleStrategy,\n      factory: AbpTitleStrategy.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpTitleStrategy, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass AbpApplicationConfigurationService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'abp';\n    this.get = (options, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/abp/application-configuration',\n      params: {\n        includeLocalizationResources: options.includeLocalizationResources\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function AbpApplicationConfigurationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpApplicationConfigurationService)(i0.ɵɵinject(RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpApplicationConfigurationService,\n      factory: AbpApplicationConfigurationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpApplicationConfigurationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RestService\n  }], null);\n})();\nclass AbpApplicationLocalizationService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'abp';\n    this.get = (input, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/abp/application-localization',\n      params: {\n        cultureName: input.cultureName,\n        onlyDynamics: input.onlyDynamics\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function AbpApplicationLocalizationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpApplicationLocalizationService)(i0.ɵɵinject(RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpApplicationLocalizationService,\n      factory: AbpApplicationLocalizationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpApplicationLocalizationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RestService\n  }], null);\n})();\nclass ConfigStateService {\n  setState(config) {\n    this.store.set(config);\n  }\n  get createOnUpdateStream() {\n    return this.store.sliceUpdate;\n  }\n  constructor(abpConfigService, abpApplicationLocalizationService, includeLocalizationResources) {\n    this.abpConfigService = abpConfigService;\n    this.abpApplicationLocalizationService = abpApplicationLocalizationService;\n    this.includeLocalizationResources = includeLocalizationResources;\n    this.updateSubject = new Subject();\n    this.store = new InternalStore({});\n    this.initUpdateStream();\n  }\n  initUpdateStream() {\n    this.updateSubject.pipe(switchMap(() => this.abpConfigService.get({\n      includeLocalizationResources: !!this.includeLocalizationResources\n    }))).pipe(switchMap(appState => this.getLocalizationAndCombineWithAppState(appState))).subscribe(res => this.store.set(res));\n  }\n  getLocalizationAndCombineWithAppState(appState) {\n    if (!appState.localization.currentCulture.cultureName) {\n      throw new Error('culture name should defined');\n    }\n    return this.getlocalizationResource(appState.localization.currentCulture.cultureName).pipe(map(result => ({\n      ...appState,\n      localization: {\n        ...appState.localization,\n        ...result\n      }\n    })));\n  }\n  getlocalizationResource(cultureName) {\n    return this.abpApplicationLocalizationService.get({\n      cultureName: cultureName,\n      onlyDynamics: false\n    });\n  }\n  refreshAppState() {\n    this.updateSubject.next();\n    return this.createOnUpdateStream(state => state).pipe(take(1));\n  }\n  refreshLocalization(lang) {\n    if (this.includeLocalizationResources) {\n      return this.refreshAppState().pipe(map(() => null));\n    }\n    return this.getlocalizationResource(lang).pipe(tap(result => this.store.patch({\n      localization: {\n        ...this.store.state.localization,\n        ...result\n      }\n    }))).pipe(map(() => null));\n  }\n  getOne$(key) {\n    return this.store.sliceState(state => state[key]);\n  }\n  getOne(key) {\n    return this.store.state[key];\n  }\n  getAll$() {\n    return this.store.sliceState(state => state);\n  }\n  getAll() {\n    return this.store.state;\n  }\n  getDeep$(keys) {\n    keys = splitKeys(keys);\n    return this.store.sliceState(state => state).pipe(map(state => {\n      return keys.reduce((acc, val) => {\n        if (acc) {\n          return acc[val];\n        }\n        return undefined;\n      }, state);\n    }));\n  }\n  getDeep(keys) {\n    keys = splitKeys(keys);\n    return keys.reduce((acc, val) => {\n      if (acc) {\n        return acc[val];\n      }\n      return undefined;\n    }, this.store.state);\n  }\n  getFeature(key) {\n    return this.store.state.features?.values?.[key];\n  }\n  getFeature$(key) {\n    return this.store.sliceState(state => state.features?.values?.[key]);\n  }\n  getFeatures(keys) {\n    const {\n      features\n    } = this.store.state;\n    if (!features) return;\n    return keys.reduce((acc, key) => ({\n      ...acc,\n      [key]: features.values[key]\n    }), {});\n  }\n  getFeatures$(keys) {\n    return this.store.sliceState(({\n      features\n    }) => {\n      if (!features?.values) return;\n      return keys.reduce((acc, key) => ({\n        ...acc,\n        [key]: features.values[key]\n      }), {});\n    });\n  }\n  getSetting(key) {\n    return this.store.state.setting?.values?.[key];\n  }\n  getSetting$(key) {\n    return this.store.sliceState(state => state.setting?.values?.[key]);\n  }\n  getSettings(keyword) {\n    const settings = this.store.state.setting?.values || {};\n    if (!keyword) return settings;\n    const keysFound = Object.keys(settings).filter(key => key.indexOf(keyword) > -1);\n    return keysFound.reduce((acc, key) => {\n      acc[key] = settings[key];\n      return acc;\n    }, {});\n  }\n  getSettings$(keyword) {\n    return this.store.sliceState(state => state.setting?.values).pipe(map((settings = {}) => {\n      if (!keyword) return settings;\n      const keysFound = Object.keys(settings).filter(key => key.indexOf(keyword) > -1);\n      return keysFound.reduce((acc, key) => {\n        acc[key] = settings[key];\n        return acc;\n      }, {});\n    }));\n  }\n  getGlobalFeatures() {\n    return this.store.state.globalFeatures;\n  }\n  getGlobalFeatures$() {\n    return this.store.sliceState(state => state.globalFeatures);\n  }\n  isGlobalFeatureEnabled(key, globalFeatures) {\n    const features = globalFeatures.enabledFeatures || [];\n    return features.some(f => key === f);\n  }\n  getGlobalFeatureIsEnabled(key) {\n    return this.isGlobalFeatureEnabled(key, this.store.state.globalFeatures);\n  }\n  getGlobalFeatureIsEnabled$(key) {\n    return this.store.sliceState(state => this.isGlobalFeatureEnabled(key, state.globalFeatures));\n  }\n  static {\n    this.ɵfac = function ConfigStateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigStateService)(i0.ɵɵinject(AbpApplicationConfigurationService), i0.ɵɵinject(AbpApplicationLocalizationService), i0.ɵɵinject(INCUDE_LOCALIZATION_RESOURCES_TOKEN, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigStateService,\n      factory: ConfigStateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigStateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: AbpApplicationConfigurationService\n  }, {\n    type: AbpApplicationLocalizationService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [INCUDE_LOCALIZATION_RESOURCES_TOKEN]\n    }]\n  }], null);\n})();\nfunction splitKeys(keys) {\n  if (typeof keys === 'string') {\n    keys = keys.split('.');\n  }\n  if (!Array.isArray(keys)) {\n    throw new Error('The argument must be a dot string or an string array.');\n  }\n  return keys;\n}\nclass LocalizationService {\n  /**\n   * Returns currently selected language\n   * Even though this looks like it's redundant to return the same value as `getLanguage()`,\n   * it's actually not. This could be invoked any time, and the latestLang could be different from the\n   * sessionState.getLanguage() value.\n   */\n  get currentLang() {\n    return this.latestLang || this.sessionState.getLanguage();\n  }\n  get currentLang$() {\n    return this.sessionState.getLanguage$();\n  }\n  get languageChange$() {\n    return this._languageChange$.asObservable();\n  }\n  constructor(sessionState, injector, otherInstance, configState) {\n    this.sessionState = sessionState;\n    this.injector = injector;\n    this.configState = configState;\n    this.latestLang = this.sessionState.getLanguage();\n    this._languageChange$ = new Subject();\n    this.uiLocalizations$ = new BehaviorSubject(new Map());\n    this.localizations$ = new BehaviorSubject(new Map());\n    if (otherInstance) throw new Error('LocalizationService should have only one instance.');\n    this.listenToSetLanguage();\n    this.initLocalizationValues();\n  }\n  initLocalizationValues() {\n    localizations$.subscribe(val => this.addLocalization(val));\n    const legacyResources$ = this.configState.getDeep$('localization.values');\n    const remoteLocalizations$ = this.configState.getDeep$('localization.resources');\n    const currentLanguage$ = this.sessionState.getLanguage$();\n    const uiLocalizations$ = combineLatest([currentLanguage$, this.uiLocalizations$]).pipe(map(([currentLang, localizations]) => localizations.get(currentLang)));\n    combineLatest([legacyResources$, remoteLocalizations$, uiLocalizations$]).pipe(map(([legacy, resource, local]) => {\n      if (!resource) {\n        return;\n      }\n      const remote = combineLegacyandNewResources(legacy || {}, resource);\n      if (remote) {\n        if (!local) {\n          local = new Map();\n        }\n        Object.entries(remote).forEach(entry => {\n          const resourceName = entry[0];\n          const remoteTexts = entry[1];\n          let resource = local?.get(resourceName) || {};\n          resource = {\n            ...resource,\n            ...remoteTexts\n          };\n          local?.set(resourceName, resource);\n        });\n      }\n      return local;\n    }), filter(Boolean)).subscribe(val => this.localizations$.next(val));\n  }\n  addLocalization(localizations) {\n    if (!localizations) return;\n    const localizationMap = this.uiLocalizations$.value;\n    localizations.forEach(loc => {\n      const cultureMap = localizationMap.get(loc.culture) || new Map();\n      loc.resources.forEach(res => {\n        let resource = cultureMap.get(res.resourceName) || {};\n        resource = {\n          ...resource,\n          ...res.texts\n        };\n        cultureMap.set(res.resourceName, resource);\n      });\n      localizationMap.set(loc.culture, cultureMap);\n    });\n    this.uiLocalizations$.next(localizationMap);\n  }\n  listenToSetLanguage() {\n    this.sessionState.onLanguageChange$().pipe(filter(lang => this.configState.getDeep('localization.currentCulture.cultureName') !== lang), switchMap(lang => this.configState.refreshLocalization(lang).pipe(map(() => lang))), filter(Boolean), switchMap(lang => from(this.registerLocale(lang).then(() => lang)))).subscribe(lang => this._languageChange$.next(lang));\n  }\n  registerLocale(locale) {\n    const {\n      registerLocaleFn\n    } = this.injector.get(CORE_OPTIONS);\n    return registerLocaleFn(locale).then(module => {\n      if (module?.default) registerLocaleData(module.default);\n      this.latestLang = locale;\n    });\n  }\n  /**\n   * Returns an observable localized text with the given interpolation parameters in current language.\n   * @param key Localizaton key to replace with localized text\n   * @param interpolateParams Values to interpolate\n   */\n  get(key, ...interpolateParams) {\n    return this.configState.getAll$().pipe(map(state => this.getLocalization(state, key, ...interpolateParams)));\n  }\n  getResource(resourceName) {\n    return this.localizations$.value.get(resourceName);\n  }\n  getResource$(resourceName) {\n    return this.localizations$.pipe(map(res => res.get(resourceName)));\n  }\n  /**\n   * Returns localized text with the given interpolation parameters in current language.\n   * @param key Localization key to replace with localized text\n   * @param interpolateParams Values to intepolate.\n   */\n  instant(key, ...interpolateParams) {\n    return this.getLocalization(this.configState.getAll(), key, ...interpolateParams);\n  }\n  localize(resourceName, key, defaultValue) {\n    return this.configState.getOne$('localization').pipe(map(createLocalizer), map(localize => localize(resourceName, key, defaultValue)));\n  }\n  localizeSync(resourceName, key, defaultValue) {\n    const localization = this.configState.getOne('localization');\n    return createLocalizer(localization)(resourceName, key, defaultValue);\n  }\n  localizeWithFallback(resourceNames, keys, defaultValue) {\n    return this.configState.getOne$('localization').pipe(map(createLocalizerWithFallback), map(localizeWithFallback => localizeWithFallback(resourceNames, keys, defaultValue)));\n  }\n  localizeWithFallbackSync(resourceNames, keys, defaultValue) {\n    const localization = this.configState.getOne('localization');\n    return createLocalizerWithFallback(localization)(resourceNames, keys, defaultValue);\n  }\n  getLocalization(state, key, ...interpolateParams) {\n    let defaultValue = '';\n    if (!key) {\n      return defaultValue;\n    }\n    if (typeof key !== 'string') {\n      defaultValue = key.defaultValue;\n      key = key.key;\n    }\n    const keys = key.split('::');\n    const warn = message => {\n      if (isDevMode()) console.warn(message);\n    };\n    if (keys.length < 2) {\n      warn('The localization source separator (::) not found.');\n      return defaultValue || key;\n    }\n    if (!state.localization) return defaultValue || keys[1];\n    const sourceName = keys[0] || state.localization.defaultResourceName;\n    const sourceKey = keys[1];\n    if (sourceName === '_') {\n      return defaultValue || sourceKey;\n    }\n    if (!sourceName) {\n      warn('Localization source name is not specified and the defaultResourceName was not defined!');\n      return defaultValue || sourceKey;\n    }\n    const source = this.localizations$.value.get(sourceName);\n    if (!source) {\n      warn('Could not find localization source: ' + sourceName);\n      return defaultValue || sourceKey;\n    }\n    let localization = source[sourceKey];\n    if (typeof localization === 'undefined') {\n      return defaultValue || sourceKey;\n    }\n    interpolateParams = interpolateParams.filter(params => params != null);\n    if (localization) localization = interpolate(localization, interpolateParams);\n    if (typeof localization !== 'string') localization = '';\n    return localization || defaultValue || key;\n  }\n  static {\n    this.ɵfac = function LocalizationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalizationService)(i0.ɵɵinject(SessionStateService), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(LocalizationService, 12), i0.ɵɵinject(ConfigStateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LocalizationService,\n      factory: LocalizationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocalizationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: SessionStateService\n  }, {\n    type: i0.Injector\n  }, {\n    type: LocalizationService,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: ConfigStateService\n  }], null);\n})();\nfunction recursivelyMergeBaseResources(baseResourceName, source) {\n  const item = source[baseResourceName];\n  if (item.baseResources.length === 0) {\n    return item;\n  }\n  return item.baseResources.reduce((acc, baseResource) => {\n    const baseItem = recursivelyMergeBaseResources(baseResource, source);\n    const texts = {\n      ...baseItem.texts,\n      ...item.texts\n    };\n    return {\n      ...acc,\n      texts\n    };\n  }, item);\n}\nfunction mergeResourcesWithBaseResource(resource) {\n  const entities = Object.keys(resource).map(key => {\n    const newValue = recursivelyMergeBaseResources(key, resource);\n    return [key, newValue];\n  });\n  return entities.reduce((acc, [key, value]) => ({\n    ...acc,\n    [key]: value\n  }), {});\n}\nfunction combineLegacyandNewResources(legacy, resource) {\n  const mergedResource = mergeResourcesWithBaseResource(resource);\n  return Object.entries(mergedResource).reduce((acc, [key, value]) => {\n    return {\n      ...acc,\n      [key]: value.texts\n    };\n  }, legacy);\n}\nclass DynamicLayoutComponent {\n  constructor(dynamicLayoutComponent) {\n    this.layouts = inject(DYNAMIC_LAYOUTS_TOKEN);\n    this.isLayoutVisible = true;\n    this.router = inject(Router);\n    this.route = inject(ActivatedRoute);\n    this.routes = inject(RoutesService);\n    this.localizationService = inject(LocalizationService);\n    this.replaceableComponents = inject(ReplaceableComponentsService);\n    this.subscription = inject(SubscriptionService);\n    this.routerEvents = inject(RouterEvents);\n    this.environment = inject(EnvironmentService);\n    if (dynamicLayoutComponent) {\n      if (isDevMode()) console.warn('DynamicLayoutComponent must be used only in AppComponent.');\n      return;\n    }\n    this.checkLayoutOnNavigationEnd();\n    this.listenToLanguageChange();\n  }\n  ngOnInit() {\n    if (this.layout) {\n      return;\n    }\n    const {\n      oAuthConfig\n    } = this.environment.getEnvironment();\n    if (oAuthConfig.responseType === 'code') {\n      this.getLayout();\n    }\n  }\n  checkLayoutOnNavigationEnd() {\n    const navigationEnd$ = this.routerEvents.getNavigationEvents('End');\n    this.subscription.addOne(navigationEnd$, () => this.getLayout());\n  }\n  getLayout() {\n    let expectedLayout = this.getExtractedLayout();\n    if (!expectedLayout) expectedLayout = \"empty\" /* eLayoutType.empty */;\n    if (this.layoutKey === expectedLayout) return;\n    const key = this.layouts.get(expectedLayout);\n    if (key) {\n      this.layout = this.getComponent(key)?.component;\n      this.layoutKey = expectedLayout;\n    }\n    if (!this.layout) {\n      this.showLayoutNotFoundError(expectedLayout);\n    }\n  }\n  getExtractedLayout() {\n    const routeData = this.route.snapshot.data || {};\n    let expectedLayout = routeData['layout'];\n    let node = findRoute(this.routes, getRoutePath(this.router));\n    node = {\n      parent: node\n    };\n    while (node.parent) {\n      node = node.parent;\n      if (node.layout) {\n        expectedLayout = node.layout;\n        break;\n      }\n    }\n    return expectedLayout;\n  }\n  showLayoutNotFoundError(layoutName) {\n    let message = `Layout ${layoutName} not found.`;\n    if (layoutName === 'account') {\n      message = 'Account layout not found. Please check your configuration. If you are using LeptonX, please make sure you have added \"AccountLayoutModule.forRoot()\" to your app.module configuration.';\n    }\n    console.warn(message);\n  }\n  listenToLanguageChange() {\n    this.subscription.addOne(this.localizationService.languageChange$, () => {\n      this.isLayoutVisible = false;\n      setTimeout(() => this.isLayoutVisible = true, 0);\n    });\n  }\n  getComponent(key) {\n    return this.replaceableComponents.get(key);\n  }\n  static {\n    this.ɵfac = function DynamicLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DynamicLayoutComponent)(i0.ɵɵdirectiveInject(DynamicLayoutComponent, 12));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: DynamicLayoutComponent,\n      selectors: [[\"abp-dynamic-layout\"]],\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngComponentOutlet\"]],\n      template: function DynamicLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DynamicLayoutComponent_Conditional_0_Template, 1, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.isLayoutVisible ? 0 : -1);\n        }\n      },\n      dependencies: [i1$2.NgComponentOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DynamicLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-dynamic-layout',\n      template: `\n    @if (isLayoutVisible) {\n      <ng-container [ngComponentOutlet]=\"layout\"></ng-container>\n    }\n  `,\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: DynamicLayoutComponent,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], null);\n})();\nclass ReplaceableRouteContainerComponent {\n  constructor(route, replaceableComponents, subscription) {\n    this.route = route;\n    this.replaceableComponents = replaceableComponents;\n    this.subscription = subscription;\n  }\n  ngOnInit() {\n    this.defaultComponent = this.route.snapshot.data.replaceableComponent.defaultComponent;\n    this.componentKey = this.route.snapshot.data.replaceableComponent.key;\n    const component$ = this.replaceableComponents.get$(this.componentKey).pipe(distinctUntilChanged());\n    this.subscription.addOne(component$, (res = {}) => {\n      this.externalComponent = res.component;\n    });\n  }\n  static {\n    this.ɵfac = function ReplaceableRouteContainerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReplaceableRouteContainerComponent)(i0.ɵɵdirectiveInject(i1$1.ActivatedRoute), i0.ɵɵdirectiveInject(ReplaceableComponentsService), i0.ɵɵdirectiveInject(SubscriptionService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ReplaceableRouteContainerComponent,\n      selectors: [[\"abp-replaceable-route-container\"]],\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngComponentOutlet\"]],\n      template: function ReplaceableRouteContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ReplaceableRouteContainerComponent_ng_container_0_Template, 1, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngComponentOutlet\", ctx.externalComponent || ctx.defaultComponent);\n        }\n      },\n      dependencies: [i1$2.NgComponentOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReplaceableRouteContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-replaceable-route-container',\n      template: `\n    <ng-container *ngComponentOutlet=\"externalComponent || defaultComponent\"></ng-container>\n  `,\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: i1$1.ActivatedRoute\n  }, {\n    type: ReplaceableComponentsService\n  }, {\n    type: SubscriptionService\n  }], null);\n})();\nclass RouterOutletComponent {\n  static {\n    this.ɵfac = function RouterOutletComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterOutletComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: RouterOutletComponent,\n      selectors: [[\"abp-router-outlet\"]],\n      decls: 1,\n      vars: 0,\n      template: function RouterOutletComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1$1.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterOutletComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-router-outlet',\n      template: ` <router-outlet></router-outlet> `\n    }]\n  }], null, null);\n})();\n\n// Different locales from .NET\n// Key is .NET locale, value is Angular locale\nconst differentLocales = {\n  aa: 'en',\n  'aa-DJ': 'en',\n  'aa-ER': 'en',\n  'aa-ET': 'en',\n  'af-ZA': 'af',\n  'agq-CM': 'agq',\n  'ak-GH': 'ak',\n  'am-ET': 'am',\n  'ar-001': 'ar',\n  arn: 'en',\n  'arn-CL': 'en',\n  'as-IN': 'as',\n  'asa-TZ': 'asa',\n  'ast-ES': 'ast',\n  'az-Cyrl-AZ': 'az-Cyrl',\n  'az-Latn-AZ': 'az-Latn',\n  ba: 'ru',\n  'ba-RU': 'ru',\n  'bas-CM': 'bas',\n  'be-BY': 'be',\n  'bem-ZM': 'bem',\n  'bez-TZ': 'bez',\n  'bg-BG': 'bg',\n  bin: 'en',\n  'bin-NG': 'en',\n  'bm-Latn': 'bm',\n  'bm-Latn-ML': 'bm',\n  'bn-BD': 'bn',\n  'bo-CN': 'bo',\n  'br-FR': 'br',\n  'brx-IN': 'brx',\n  'bs-Cyrl-BA': 'bs-Cyrl',\n  'bs-Latn-BA': 'bs-Latn',\n  byn: 'en',\n  'byn-ER': 'en',\n  'ca-ES': 'ca',\n  'ca-ES-valencia': 'ca-ES-VALENCIA',\n  'ce-RU': 'ce',\n  'cgg-UG': 'cgg',\n  'chr-Cher': 'chr',\n  'chr-Cher-US': 'chr',\n  co: 'en',\n  'co-FR': 'fr',\n  'cs-CZ': 'cs',\n  'cu-RU': 'cu',\n  'cy-GB': 'cy',\n  'da-DK': 'da',\n  'dav-KE': 'dav',\n  'de-DE': 'de',\n  'dje-NE': 'dje',\n  'dsb-DE': 'dsb',\n  'dua-CM': 'dua',\n  dv: 'en',\n  'dv-MV': 'en',\n  'dyo-SN': 'dyo',\n  'dz-BT': 'dz',\n  'ebu-KE': 'ebu',\n  'ee-GH': 'ee',\n  'el-GR': 'el',\n  'en-029': 'en',\n  'en-ID': 'en',\n  'en-US': 'en',\n  'eo-001': 'en',\n  'es-ES': 'es',\n  'et-EE': 'et',\n  'eu-ES': 'eu',\n  'ewo-CM': 'ewo',\n  'fa-IR': 'fa',\n  'ff-Latn-SN': 'ff-Latn',\n  'ff-NG': 'ff',\n  'fi-FI': 'fi',\n  'fil-PH': 'fil',\n  'fo-FO': 'fo',\n  'fr-029': 'fr',\n  'fr-FR': 'fr',\n  'fur-IT': 'fur',\n  'fy-NL': 'fy',\n  'ga-IE': 'ga',\n  'gd-GB': 'gd',\n  'gl-ES': 'gl',\n  gn: 'en',\n  'gn-PY': 'en',\n  'gsw-CH': 'gsw',\n  'gu-IN': 'gu',\n  'guz-KE': 'guz',\n  'gv-IM': 'gv',\n  'ha-Latn': 'ha',\n  'ha-Latn-GH': 'ha-GH',\n  'ha-Latn-NE': 'ha-NE',\n  'ha-Latn-NG': 'ha',\n  'haw-US': 'haw',\n  'he-IL': 'he',\n  'hi-IN': 'hi',\n  'hr-HR': 'hr',\n  'hsb-DE': 'hsb',\n  'hu-HU': 'hu',\n  'hy-AM': 'hy',\n  'ia-001': 'ia',\n  'ia-FR': 'ia',\n  ibb: 'en',\n  'ibb-NG': 'en',\n  'id-ID': 'id',\n  'ig-NG': 'ig',\n  'ii-CN': 'ii',\n  'is-IS': 'is',\n  'it-IT': 'it',\n  iu: 'en',\n  'iu-Cans': 'en',\n  'iu-Cans-CA': 'en',\n  'iu-Latn': 'en',\n  'iu-Latn-CA': 'en',\n  'ja-JP': 'ja',\n  'jgo-CM': 'jgo',\n  'jmc-TZ': 'jmc',\n  'jv-Java': 'jv',\n  'jv-Java-ID': 'jv',\n  'jv-Latn': 'jv',\n  'jv-Latn-ID': 'jv',\n  'ka-GE': 'ka',\n  'kab-DZ': 'kab',\n  'kam-KE': 'kam',\n  'kde-TZ': 'kde',\n  'kea-CV': 'kea',\n  'khq-ML': 'khq',\n  'ki-KE': 'ki',\n  'kk-KZ': 'kk',\n  'kkj-CM': 'kkj',\n  'kl-GL': 'kl',\n  'kln-KE': 'kln',\n  'km-KH': 'km',\n  'kn-IN': 'kn',\n  'ko-KR': 'ko',\n  'kok-IN': 'kok',\n  kr: 'en',\n  'kr-NG': 'en',\n  'ks-Arab': 'ks',\n  'ks-Arab-IN': 'ks',\n  'ks-Deva': 'ks',\n  'ks-Deva-IN': 'ks',\n  'ksb-TZ': 'ksb',\n  'ksf-CM': 'ksf',\n  'ksh-DE': 'ksh',\n  'ku-Arab': 'ku',\n  'ku-Arab-IQ': 'ku',\n  'ku-Arab-IR': 'ku',\n  'kw-GB': 'kw',\n  'ky-KG': 'ky',\n  la: 'en',\n  'la-001': 'en',\n  'lag-TZ': 'lag',\n  'lb-LU': 'lb',\n  'lg-UG': 'lg',\n  'lkt-US': 'lkt',\n  'ln-CD': 'ln',\n  'lo-LA': 'lo',\n  'lrc-IR': 'lrc',\n  'lt-LT': 'lt',\n  'lu-CD': 'lu',\n  'luo-KE': 'luo',\n  'luy-KE': 'luy',\n  'lv-LV': 'lv',\n  'mas-KE': 'mas',\n  'mer-KE': 'mer',\n  'mfe-MU': 'mfe',\n  'mg-MG': 'mg',\n  'mgh-MZ': 'mgh',\n  'mgo-CM': 'mgo',\n  'mi-NZ': 'mi',\n  'mk-MK': 'mk',\n  'ml-IN': 'ml',\n  'mn-Cyrl': 'mn',\n  'mn-MN': 'mn',\n  'mn-Mong': 'mn',\n  'mn-Mong-CN': 'mn',\n  'mn-Mong-MN': 'mn',\n  mni: 'en',\n  'mni-IN': 'en',\n  moh: 'en',\n  'moh-CA': 'en',\n  'mr-IN': 'mr',\n  'ms-MY': 'ms',\n  'mt-MT': 'mt',\n  'mua-CM': 'mua',\n  'my-MM': 'my',\n  'mzn-IR': 'mzn',\n  'naq-NA': 'naq',\n  'nb-NO': 'nb',\n  'nd-ZW': 'nd',\n  'ne-NP': 'ne',\n  'nl-NL': 'nl',\n  'nmg-CM': 'ngm',\n  'nn-NO': 'nn',\n  'nnh-CM': 'nnh',\n  no: 'en',\n  nqo: 'en',\n  'nqo-GN': 'en',\n  nr: 'en',\n  'nr-ZA': 'en',\n  nso: 'en',\n  'nso-ZA': 'en',\n  'nus-SS': 'nus',\n  'nyn-UG': 'nyn',\n  oc: 'en',\n  'oc-FR': 'fr',\n  'om-ET': 'om',\n  'or-IN': 'or',\n  'os-GE': 'os',\n  'pa-Arab-PK': 'pa-Arab',\n  'pa-IN': 'pa',\n  pap: 'en',\n  'pap-029': 'en',\n  'pl-PL': 'pl',\n  'prg-001': 'prg',\n  prs: 'en',\n  'prs-AF': 'en',\n  'ps-AF': 'ps',\n  'pt-BR': 'pt',\n  quc: 'en',\n  'quc-Latn': 'en',\n  'quc-Latn-GT': 'en',\n  quz: 'en',\n  'quz-BO': 'en',\n  'quz-EC': 'en',\n  'quz-PE': 'en',\n  'rm-CH': 'rm',\n  'rn-BI': 'rn',\n  'ro-RO': 'ro',\n  'rof-TZ': 'rof',\n  'ru-RU': 'ru',\n  'rw-RW': 'rw',\n  'rwk-TZ': 'rwk',\n  sa: 'en',\n  'sa-IN': 'en',\n  'sah-RU': 'sah',\n  'saq-KE': 'saq',\n  'sbp-TZ': 'en',\n  'sd-Arab': 'sd',\n  'sd-Arab-PK': 'sd',\n  'sd-Deva': 'sd',\n  'sd-Deva-IN': 'sd',\n  'se-NO': 'se',\n  'seh-MZ': 'seh',\n  'ses-ML': 'ses',\n  'sg-CF': 'sg',\n  'shi-Latn-MA': 'shi-Latn',\n  'shi-Tfng-MA': 'shi-Tfng',\n  'si-LK': 'si',\n  'sk-SK': 'sk',\n  'sl-SI': 'sl',\n  sma: 'en',\n  'sma-NO': 'en',\n  'sma-SE': 'en',\n  smj: 'en',\n  'smj-NO': 'en',\n  'smj-SE': 'en',\n  'smn-FI': 'en',\n  sms: 'en',\n  'sms-FI': 'en',\n  'sn-Latn': 'sn',\n  'sn-Latn-ZW': 'sn',\n  'so-SO': 'so',\n  'sq-AL': 'so',\n  'sr-Cyrl-RS': 'sr-Cryl',\n  'sr-Latn-RS': 'sr-Latn',\n  ss: 'en',\n  'ss-SZ': 'en',\n  'ss-ZA': 'en',\n  ssy: 'en',\n  'ssy-ER': 'en',\n  st: 'en',\n  'st-LS': 'en',\n  'st-ZA': 'en',\n  'sv-SE': 'sv',\n  'sw-TZ': 'sw',\n  syr: 'en',\n  'syr-SY': 'en',\n  'ta-IN': 'ta',\n  'te-IN': 'te',\n  'teo-UG': 'teo',\n  'tg-Cyrl': 'tg',\n  'tg-Cyrl-TJ': 'tg',\n  'th-TH': 'th',\n  'ti-ET': 'ti',\n  tig: 'en',\n  'tig-ER': 'en',\n  'tk-TM': 'tk',\n  tn: 'en',\n  'tn-BW': 'en',\n  'tn-ZA': 'en',\n  'to-TO': 'to',\n  'tr-TR': 'tr',\n  ts: 'en',\n  'ts-ZA': 'en',\n  'tt-RU': 'tt',\n  'twq-NE': 'twq',\n  'tzm-Arab': 'tzm',\n  'tzm-Arab-MA': 'tzm',\n  'tzm-Latn': 'tzm',\n  'tzm-Latn-DZ': 'tzm',\n  'tzm-Latn-MA': 'tzm',\n  'tzm-Tfng': 'tzm',\n  'tzm-Tfng-MA': 'tzm',\n  'ug-CN': 'ug',\n  'uk-UA': 'uk',\n  'ur-PK': 'ur',\n  'uz-Arab-AF': 'uz-Arab',\n  'uz-Cyrl-UZ': 'uz-Cyrl',\n  'uz-Latn-UZ': 'uz-Latn',\n  'vai-Latn-LR': 'vai-Latn',\n  'vai-Vaii-LR': 'vai-Vaii',\n  ve: 'en',\n  've-ZA': 'en',\n  'vi-VN': 'vi',\n  'vo-001': 'vo',\n  'vun-TZ': 'vun',\n  'wae-CH': 'wae',\n  wal: 'en',\n  'wal-ET': 'en',\n  'wo-SN': 'wo',\n  'xh-ZA': 'xh',\n  'xog-UG': 'xog',\n  'yav-CM': 'yav',\n  'yi-001': 'yi',\n  'yo-NG': 'yo',\n  'zgh-Tfng': 'zgh',\n  'zgh-Tfng-MA': 'zgh',\n  'zh-CN': 'zh',\n  'zh-HK': 'zh',\n  'zh-MO': 'zh',\n  'zh-SG': 'zh',\n  'zh-TW': 'zh',\n  'zu-ZA': 'zu'\n};\nconst DEFAULT_DYNAMIC_LAYOUTS = new Map([[\"application\" /* eLayoutType.application */, \"Theme.ApplicationLayoutComponent\" /* eThemeSharedComponents.ApplicationLayoutComponent */], [\"account\" /* eLayoutType.account */, \"Theme.AccountLayoutComponent\" /* eThemeSharedComponents.AccountLayoutComponent */], [\"empty\" /* eLayoutType.empty */, \"Theme.EmptyLayoutComponent\" /* eThemeSharedComponents.EmptyLayoutComponent */]]);\nclass AutofocusDirective {\n  set delay(val) {\n    this._delay = Number(val) || 0;\n  }\n  get delay() {\n    return this._delay;\n  }\n  constructor(elRef) {\n    this.elRef = elRef;\n    this._delay = 0;\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.elRef.nativeElement.focus(), this.delay);\n  }\n  static {\n    this.ɵfac = function AutofocusDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutofocusDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AutofocusDirective,\n      selectors: [[\"\", \"autofocus\", \"\"]],\n      inputs: {\n        delay: [0, \"autofocus\", \"delay\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofocusDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[autofocus]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    delay: [{\n      type: Input,\n      args: ['autofocus']\n    }]\n  });\n})();\nclass InputEventDebounceDirective {\n  constructor(el, subscription) {\n    this.el = el;\n    this.subscription = subscription;\n    this.debounce = 300;\n    this.debounceEvent = new EventEmitter();\n  }\n  ngOnInit() {\n    const input$ = fromEvent(this.el.nativeElement, 'input').pipe(debounceTime(this.debounce));\n    this.subscription.addOne(input$, event => {\n      this.debounceEvent.emit(event);\n    });\n  }\n  static {\n    this.ɵfac = function InputEventDebounceDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InputEventDebounceDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SubscriptionService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InputEventDebounceDirective,\n      selectors: [[\"\", \"input.debounce\", \"\"]],\n      inputs: {\n        debounce: \"debounce\"\n      },\n      outputs: {\n        debounceEvent: \"input.debounce\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputEventDebounceDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[input.debounce]',\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: SubscriptionService\n  }], {\n    debounce: [{\n      type: Input\n    }],\n    debounceEvent: [{\n      type: Output,\n      args: ['input.debounce']\n    }]\n  });\n})();\nclass AbpForContext {\n  constructor($implicit, index, count, list) {\n    this.$implicit = $implicit;\n    this.index = index;\n    this.count = count;\n    this.list = list;\n  }\n}\nclass RecordView {\n  constructor(record, view) {\n    this.record = record;\n    this.view = view;\n  }\n}\nclass ForDirective {\n  get compareFn() {\n    return this.compareBy || compare;\n  }\n  get trackByFn() {\n    return this.trackBy || ((index, item) => item.id || index);\n  }\n  constructor(tempRef, vcRef, differs) {\n    this.tempRef = tempRef;\n    this.vcRef = vcRef;\n    this.differs = differs;\n  }\n  iterateOverAppliedOperations(changes) {\n    const rw = [];\n    changes.forEachOperation((record, previousIndex, currentIndex) => {\n      if (record.previousIndex == null) {\n        const view = this.vcRef.createEmbeddedView(this.tempRef, new AbpForContext(null, -1, -1, this.items), currentIndex || 0);\n        rw.push(new RecordView(record, view));\n      } else if (currentIndex == null && previousIndex !== null) {\n        this.vcRef.remove(previousIndex);\n      } else {\n        if (previousIndex !== null) {\n          const view = this.vcRef.get(previousIndex);\n          if (view && currentIndex !== null) {\n            this.vcRef.move(view, currentIndex);\n            rw.push(new RecordView(record, view));\n          }\n        }\n      }\n    });\n    for (let i = 0, l = rw.length; i < l; i++) {\n      rw[i].view.context.$implicit = rw[i].record.item;\n    }\n  }\n  iterateOverAttachedViews(changes) {\n    for (let i = 0, l = this.vcRef.length; i < l; i++) {\n      const viewRef = this.vcRef.get(i);\n      viewRef.context.index = i;\n      viewRef.context.count = l;\n      viewRef.context.list = this.items;\n    }\n    changes.forEachIdentityChange(record => {\n      if (record.currentIndex !== null) {\n        const viewRef = this.vcRef.get(record.currentIndex);\n        viewRef.context.$implicit = record.item;\n      }\n    });\n  }\n  projectItems(items) {\n    if (!items.length && this.emptyRef) {\n      this.vcRef.clear();\n      this.vcRef.createEmbeddedView(this.emptyRef).rootNodes;\n      this.isShowEmptyRef = true;\n      this.differ = null;\n      return;\n    }\n    if (this.emptyRef && this.isShowEmptyRef) {\n      this.vcRef.clear();\n      this.isShowEmptyRef = false;\n    }\n    if (!this.differ && items) {\n      this.differ = this.differs.find(items).create(this.trackByFn);\n    }\n    if (this.differ) {\n      const changes = this.differ.diff(items);\n      if (changes) {\n        this.iterateOverAppliedOperations(changes);\n        this.iterateOverAttachedViews(changes);\n      }\n    }\n  }\n  sortItems(items) {\n    const orderBy = this.orderBy;\n    if (orderBy) {\n      items.sort((a, b) => a[orderBy] > b[orderBy] ? 1 : a[orderBy] < b[orderBy] ? -1 : 0);\n    } else {\n      items.sort();\n    }\n  }\n  ngOnChanges() {\n    let items = clone(this.items);\n    if (!Array.isArray(items)) return;\n    const compareFn = this.compareFn;\n    const filterBy = this.filterBy;\n    if (typeof filterBy !== 'undefined' && typeof this.filterVal !== 'undefined' && this.filterVal !== '') {\n      items = items.filter(item => compareFn(item[filterBy], this.filterVal));\n    }\n    switch (this.orderDir) {\n      case 'ASC':\n        this.sortItems(items);\n        this.projectItems(items);\n        break;\n      case 'DESC':\n        this.sortItems(items);\n        items.reverse();\n        this.projectItems(items);\n        break;\n      default:\n        this.projectItems(items);\n    }\n  }\n  static {\n    this.ɵfac = function ForDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ForDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.IterableDiffers));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ForDirective,\n      selectors: [[\"\", \"abpFor\", \"\"]],\n      inputs: {\n        items: [0, \"abpForOf\", \"items\"],\n        orderBy: [0, \"abpForOrderBy\", \"orderBy\"],\n        orderDir: [0, \"abpForOrderDir\", \"orderDir\"],\n        filterBy: [0, \"abpForFilterBy\", \"filterBy\"],\n        filterVal: [0, \"abpForFilterVal\", \"filterVal\"],\n        trackBy: [0, \"abpForTrackBy\", \"trackBy\"],\n        compareBy: [0, \"abpForCompareBy\", \"compareBy\"],\n        emptyRef: [0, \"abpForEmptyRef\", \"emptyRef\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ForDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpFor]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.IterableDiffers\n  }], {\n    items: [{\n      type: Input,\n      args: ['abpForOf']\n    }],\n    orderBy: [{\n      type: Input,\n      args: ['abpForOrderBy']\n    }],\n    orderDir: [{\n      type: Input,\n      args: ['abpForOrderDir']\n    }],\n    filterBy: [{\n      type: Input,\n      args: ['abpForFilterBy']\n    }],\n    filterVal: [{\n      type: Input,\n      args: ['abpForFilterVal']\n    }],\n    trackBy: [{\n      type: Input,\n      args: ['abpForTrackBy']\n    }],\n    compareBy: [{\n      type: Input,\n      args: ['abpForCompareBy']\n    }],\n    emptyRef: [{\n      type: Input,\n      args: ['abpForEmptyRef']\n    }]\n  });\n})();\n\n/**\n * @deprecated FormSubmitDirective will be removed in V7.0.0. Use `ngSubmit` instead.\n */\nclass FormSubmitDirective {\n  constructor(formGroupDirective, host, cdRef, subscription) {\n    this.formGroupDirective = formGroupDirective;\n    this.host = host;\n    this.cdRef = cdRef;\n    this.subscription = subscription;\n    this.debounce = 200;\n    this.markAsDirtyWhenSubmit = true;\n    this.ngSubmit = new EventEmitter();\n    this.executedNgSubmit = false;\n  }\n  ngOnInit() {\n    this.subscription.addOne(this.formGroupDirective.ngSubmit, () => {\n      if (this.markAsDirtyWhenSubmit) {\n        this.markAsDirty();\n      }\n      this.executedNgSubmit = true;\n    });\n    const keyup$ = fromEvent(this.host.nativeElement, 'keyup').pipe(debounceTime(this.debounce), filter(event => !(event.target instanceof HTMLTextAreaElement)), filter(event => event && event.key === 'Enter'));\n    this.subscription.addOne(keyup$, () => {\n      if (!this.executedNgSubmit) {\n        this.host.nativeElement.dispatchEvent(new Event('submit', {\n          bubbles: true,\n          cancelable: true\n        }));\n      }\n      this.executedNgSubmit = false;\n    });\n  }\n  markAsDirty() {\n    const {\n      form\n    } = this.formGroupDirective;\n    setDirty(form.controls);\n    form.markAsDirty();\n    this.cdRef.detectChanges();\n  }\n  static {\n    this.ɵfac = function FormSubmitDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormSubmitDirective)(i0.ɵɵdirectiveInject(i1$3.FormGroupDirective, 2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(SubscriptionService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FormSubmitDirective,\n      selectors: [[\"form\", \"ngSubmit\", \"\", \"formGroup\", \"\"]],\n      inputs: {\n        debounce: \"debounce\",\n        notValidateOnSubmit: \"notValidateOnSubmit\",\n        markAsDirtyWhenSubmit: \"markAsDirtyWhenSubmit\"\n      },\n      outputs: {\n        ngSubmit: \"ngSubmit\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormSubmitDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'form[ngSubmit][formGroup]',\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: i1$3.FormGroupDirective,\n    decorators: [{\n      type: Self\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: SubscriptionService\n  }], {\n    debounce: [{\n      type: Input\n    }],\n    notValidateOnSubmit: [{\n      type: Input\n    }],\n    markAsDirtyWhenSubmit: [{\n      type: Input\n    }],\n    ngSubmit: [{\n      type: Output\n    }]\n  });\n})();\nfunction setDirty(controls) {\n  if (Array.isArray(controls)) {\n    controls.forEach(group => {\n      setDirty(group.controls);\n    });\n    return;\n  }\n  Object.keys(controls).forEach(key => {\n    controls[key].markAsDirty();\n    controls[key].updateValueAndValidity();\n  });\n}\nclass InitDirective {\n  constructor(elRef) {\n    this.elRef = elRef;\n    this.init = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    this.init.emit(this.elRef);\n  }\n  static {\n    this.ɵfac = function InitDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InitDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InitDirective,\n      selectors: [[\"\", \"abpInit\", \"\"]],\n      outputs: {\n        init: \"abpInit\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InitDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpInit]'\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    init: [{\n      type: Output,\n      args: ['abpInit']\n    }]\n  });\n})();\nclass PermissionDirective {\n  constructor(templateRef, vcRef, permissionService, cdRef, queue) {\n    this.templateRef = templateRef;\n    this.vcRef = vcRef;\n    this.permissionService = permissionService;\n    this.cdRef = cdRef;\n    this.queue = queue;\n    this.runChangeDetection = true;\n    this.cdrSubject = new ReplaySubject();\n    this.rendered = false;\n  }\n  check() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n    this.subscription = this.permissionService.getGrantedPolicy$(this.condition || '').pipe(distinctUntilChanged()).subscribe(isGranted => {\n      this.vcRef.clear();\n      if (isGranted) this.vcRef.createEmbeddedView(this.templateRef);\n      if (this.runChangeDetection) {\n        if (!this.rendered) {\n          this.cdrSubject.next();\n        } else {\n          this.cdRef.detectChanges();\n        }\n      } else {\n        this.cdRef.markForCheck();\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.subscription) this.subscription.unsubscribe();\n  }\n  ngOnChanges() {\n    this.check();\n  }\n  ngAfterViewInit() {\n    this.cdrSubject.pipe(take(1)).subscribe(() => this.queue.add(() => this.cdRef.detectChanges()));\n    this.rendered = true;\n  }\n  static {\n    this.ɵfac = function PermissionDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermissionDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef, 8), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(PermissionService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(QUEUE_MANAGER));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PermissionDirective,\n      selectors: [[\"\", \"abpPermission\", \"\"]],\n      inputs: {\n        condition: [0, \"abpPermission\", \"condition\"],\n        runChangeDetection: [0, \"abpPermissionRunChangeDetection\", \"runChangeDetection\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpPermission]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: PermissionService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [QUEUE_MANAGER]\n    }]\n  }], {\n    condition: [{\n      type: Input,\n      args: ['abpPermission']\n    }],\n    runChangeDetection: [{\n      type: Input,\n      args: ['abpPermissionRunChangeDetection']\n    }]\n  });\n})();\nclass ReplaceableTemplateDirective {\n  constructor(injector, templateRef, vcRef, replaceableComponents, subscription) {\n    this.injector = injector;\n    this.templateRef = templateRef;\n    this.vcRef = vcRef;\n    this.replaceableComponents = replaceableComponents;\n    this.subscription = subscription;\n    this.providedData = {\n      inputs: {},\n      outputs: {}\n    };\n    this.context = {};\n    this.defaultComponentSubscriptions = {};\n    this.initialized = false;\n    this.context = {\n      initTemplate: ref => {\n        this.resetDefaultComponent();\n        this.defaultComponentRef = ref;\n        this.setDefaultComponentInputs();\n      }\n    };\n  }\n  ngOnInit() {\n    const component$ = this.replaceableComponents.get$(this.data.componentKey).pipe(filter((res = {}) => !this.initialized || !compare(res.component, this.externalComponent)));\n    this.subscription.addOne(component$, (res = {}) => {\n      this.vcRef.clear();\n      this.externalComponent = res.component;\n      if (this.defaultComponentRef) {\n        this.resetDefaultComponent();\n      }\n      if (res.component) {\n        this.setProvidedData();\n        const customInjector = Injector.create({\n          providers: [{\n            provide: 'REPLACEABLE_DATA',\n            useValue: this.providedData\n          }],\n          parent: this.injector\n        });\n        const ref = this.vcRef.createComponent(res.component, {\n          index: 0,\n          injector: customInjector\n        });\n      } else {\n        this.vcRef.createEmbeddedView(this.templateRef, this.context);\n      }\n      this.initialized = true;\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes?.data?.currentValue?.inputs && this.defaultComponentRef) {\n      this.setDefaultComponentInputs();\n    }\n  }\n  setDefaultComponentInputs() {\n    if (!this.defaultComponentRef || !this.data.inputs && !this.data.outputs) return;\n    if (this.data.inputs) {\n      for (const key in this.data.inputs) {\n        if (Object.prototype.hasOwnProperty.call(this.data.inputs, key)) {\n          if (!compare(this.defaultComponentRef[key], this.data.inputs[key].value)) {\n            this.defaultComponentRef[key] = this.data.inputs[key].value;\n          }\n        }\n      }\n    }\n    if (this.data.outputs) {\n      for (const key in this.data.outputs) {\n        if (Object.prototype.hasOwnProperty.call(this.data.outputs, key)) {\n          if (!this.defaultComponentSubscriptions[key]) {\n            this.defaultComponentSubscriptions[key] = this.defaultComponentRef[key].subscribe(value => {\n              this.data.outputs?.[key](value);\n            });\n          }\n        }\n      }\n    }\n  }\n  setProvidedData() {\n    this.providedData = {\n      outputs: {},\n      ...this.data,\n      inputs: {}\n    };\n    if (!this.data.inputs) return;\n    Object.defineProperties(this.providedData.inputs, {\n      ...Object.keys(this.data.inputs).reduce((acc, key) => ({\n        ...acc,\n        [key]: {\n          enumerable: true,\n          configurable: true,\n          get: () => this.data.inputs?.[key]?.value,\n          ...(this.data.inputs?.[key]?.twoWay && {\n            set: newValue => {\n              if (this.data.inputs?.[key]) {\n                this.data.inputs[key].value = newValue;\n              }\n              if (this.data.outputs?.[`${key}Change`]) {\n                this.data.outputs[`${key}Change`](newValue);\n              }\n            }\n          })\n        }\n      }), {})\n    });\n  }\n  resetDefaultComponent() {\n    Object.keys(this.defaultComponentSubscriptions).forEach(key => {\n      this.defaultComponentSubscriptions[key].unsubscribe();\n    });\n    this.defaultComponentSubscriptions = {};\n    this.defaultComponentRef = null;\n  }\n  static {\n    this.ɵfac = function ReplaceableTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReplaceableTemplateDirective)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(ReplaceableComponentsService), i0.ɵɵdirectiveInject(SubscriptionService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ReplaceableTemplateDirective,\n      selectors: [[\"\", \"abpReplaceableTemplate\", \"\"]],\n      inputs: {\n        data: [0, \"abpReplaceableTemplate\", \"data\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([SubscriptionService]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReplaceableTemplateDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpReplaceableTemplate]',\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: ReplaceableComponentsService\n  }, {\n    type: SubscriptionService\n  }], {\n    data: [{\n      type: Input,\n      args: ['abpReplaceableTemplate']\n    }]\n  });\n})();\nclass StopPropagationDirective {\n  constructor(el, subscription) {\n    this.el = el;\n    this.subscription = subscription;\n    this.stopPropEvent = new EventEmitter();\n  }\n  ngOnInit() {\n    this.subscription.addOne(fromEvent(this.el.nativeElement, 'click'), event => {\n      event.stopPropagation();\n      this.stopPropEvent.emit(event);\n    });\n  }\n  static {\n    this.ɵfac = function StopPropagationDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StopPropagationDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SubscriptionService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: StopPropagationDirective,\n      selectors: [[\"\", \"click.stop\", \"\"]],\n      outputs: {\n        stopPropEvent: \"click.stop\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([SubscriptionService])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StopPropagationDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[click.stop]',\n      providers: [SubscriptionService]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: SubscriptionService\n  }], {\n    stopPropEvent: [{\n      type: Output,\n      args: ['click.stop']\n    }]\n  });\n})();\nclass LocalizationPipe {\n  constructor(localization) {\n    this.localization = localization;\n  }\n  transform(value = '', ...interpolateParams) {\n    const params = interpolateParams.reduce((acc, val) => {\n      if (!acc) {\n        return val;\n      }\n      if (!val) {\n        return acc;\n      }\n      return Array.isArray(val) ? [...acc, ...val] : [...acc, val];\n    }, []) || [];\n    return this.localization.instant(value, ...params);\n  }\n  static {\n    this.ɵfac = function LocalizationPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalizationPipe)(i0.ɵɵdirectiveInject(LocalizationService, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"abpLocalization\",\n      type: LocalizationPipe,\n      pure: true\n    });\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LocalizationPipe,\n      factory: LocalizationPipe.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocalizationPipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'abpLocalization'\n    }]\n  }], () => [{\n    type: LocalizationService\n  }], null);\n})();\nclass LocalizationModule {\n  static {\n    this.ɵfac = function LocalizationModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalizationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LocalizationModule,\n      declarations: [LocalizationPipe],\n      exports: [LocalizationPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LocalizationModule, [{\n    type: NgModule,\n    args: [{\n      exports: [LocalizationPipe],\n      declarations: [LocalizationPipe]\n    }]\n  }], null, null);\n})();\nclass SortPipe {\n  transform(value, sortOrder = 'asc', sortKey) {\n    sortOrder = sortOrder && sortOrder.toLowerCase();\n    if (!value || sortOrder !== 'asc' && sortOrder !== 'desc') return value;\n    let numberArray = [];\n    let stringArray = [];\n    if (!sortKey) {\n      numberArray = value.filter(item => typeof item === 'number').sort();\n      stringArray = value.filter(item => typeof item === 'string').sort();\n    } else {\n      numberArray = value.filter(item => typeof item[sortKey] === 'number').sort((a, b) => a[sortKey] - b[sortKey]);\n      stringArray = value.filter(item => typeof item[sortKey] === 'string').sort((a, b) => {\n        if (a[sortKey] < b[sortKey]) return -1;else if (a[sortKey] > b[sortKey]) return 1;else return 0;\n      });\n    }\n    const sorted = [...numberArray, ...stringArray, ...value.filter(item => typeof (sortKey ? item[sortKey] : item) !== 'number' && typeof (sortKey ? item[sortKey] : item) !== 'string')];\n    return sortOrder === 'asc' ? sorted : sorted.reverse();\n  }\n  static {\n    this.ɵfac = function SortPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SortPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"abpSort\",\n      type: SortPipe,\n      pure: true\n    });\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SortPipe,\n      factory: SortPipe.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SortPipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'abpSort'\n    }]\n  }], null, null);\n})();\nconst INJECTOR_PIPE_DATA_TOKEN = new InjectionToken('INJECTOR_PIPE_DATA_TOKEN');\nclass ToInjectorPipe {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  transform(value, token = INJECTOR_PIPE_DATA_TOKEN, name = 'ToInjectorPipe') {\n    return Injector.create({\n      providers: [{\n        provide: token,\n        useValue: value\n      }],\n      parent: this.injector,\n      name\n    });\n  }\n  static {\n    this.ɵfac = function ToInjectorPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ToInjectorPipe)(i0.ɵɵdirectiveInject(i0.Injector, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"toInjector\",\n      type: ToInjectorPipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToInjectorPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'toInjector'\n    }]\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nDate.prototype.toLocalISOString = function () {\n  const timezoneOffset = this.getTimezoneOffset();\n  return new Date(this.getTime() - timezoneOffset * 60000).toISOString();\n};\nclass ShortDateTimePipe extends DatePipe {\n  constructor(configStateService, locale, defaultTimezone) {\n    super(locale, defaultTimezone);\n    this.configStateService = configStateService;\n  }\n  transform(value, timezone, locale) {\n    const format = getShortDateShortTimeFormat(this.configStateService);\n    return super.transform(value, format, timezone, locale);\n  }\n  static {\n    this.ɵfac = function ShortDateTimePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShortDateTimePipe)(i0.ɵɵdirectiveInject(ConfigStateService, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"shortDateTime\",\n      type: ShortDateTimePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShortDateTimePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'shortDateTime',\n      pure: true\n    }]\n  }], () => [{\n    type: ConfigStateService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DATE_PIPE_DEFAULT_TIMEZONE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass ShortTimePipe extends DatePipe {\n  constructor(configStateService, locale, defaultTimezone) {\n    super(locale, defaultTimezone);\n    this.configStateService = configStateService;\n  }\n  transform(value, timezone, locale) {\n    const format = getShortTimeFormat(this.configStateService);\n    return super.transform(value, format, timezone, locale);\n  }\n  static {\n    this.ɵfac = function ShortTimePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShortTimePipe)(i0.ɵɵdirectiveInject(ConfigStateService, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"shortTime\",\n      type: ShortTimePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShortTimePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'shortTime',\n      pure: true\n    }]\n  }], () => [{\n    type: ConfigStateService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DATE_PIPE_DEFAULT_TIMEZONE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass ShortDatePipe extends DatePipe {\n  constructor(configStateService, locale, defaultTimezone) {\n    super(locale, defaultTimezone);\n    this.configStateService = configStateService;\n  }\n  transform(value, timezone, locale) {\n    const format = getShortDateFormat(this.configStateService);\n    return super.transform(value, format, timezone, locale);\n  }\n  static {\n    this.ɵfac = function ShortDatePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShortDatePipe)(i0.ɵɵdirectiveInject(ConfigStateService, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16), i0.ɵɵdirectiveInject(DATE_PIPE_DEFAULT_TIMEZONE, 24));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"shortDate\",\n      type: ShortDatePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShortDatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'shortDate',\n      pure: true\n    }]\n  }], () => [{\n    type: ConfigStateService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DATE_PIPE_DEFAULT_TIMEZONE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass SafeHtmlPipe {\n  constructor() {\n    this.sanitizer = inject(DomSanitizer);\n  }\n  transform(value) {\n    if (typeof value !== 'string') return '';\n    return this.sanitizer.sanitize(SecurityContext.HTML, value);\n  }\n  static {\n    this.ɵfac = function SafeHtmlPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SafeHtmlPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"abpSafeHtml\",\n      type: SafeHtmlPipe,\n      pure: true\n    });\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SafeHtmlPipe,\n      factory: SafeHtmlPipe.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Injectable\n  }, {\n    type: Pipe,\n    args: [{\n      name: 'abpSafeHtml'\n    }]\n  }], null, null);\n})();\nfunction setLanguageToCookie(injector) {\n  return () => {\n    const sessionState = injector.get(SessionStateService);\n    const document = injector.get(DOCUMENT);\n    const cookieLanguageKey = injector.get(COOKIE_LANGUAGE_KEY);\n    sessionState.getLanguage$().subscribe(language => {\n      const cookieValue = encodeURIComponent(`c=${language}|uic=${language}`);\n      document.cookie = `${cookieLanguageKey}=${cookieValue}`;\n    });\n  };\n}\nconst CookieLanguageProvider = {\n  provide: APP_INITIALIZER,\n  useFactory: setLanguageToCookie,\n  deps: [Injector],\n  multi: true\n};\nclass LocaleId extends String {\n  constructor(localizationService) {\n    super();\n    this.localizationService = localizationService;\n  }\n  toString() {\n    const {\n      currentLang\n    } = this.localizationService;\n    if (checkHasProp(differentLocales, currentLang)) {\n      return differentLocales[currentLang];\n    }\n    return currentLang;\n  }\n  valueOf() {\n    return this.toString();\n  }\n}\nconst LocaleProvider = {\n  provide: LOCALE_ID,\n  useClass: LocaleId,\n  deps: [LocalizationService]\n};\nconst IncludeLocalizationResourcesProvider = {\n  provide: INCUDE_LOCALIZATION_RESOURCES_TOKEN,\n  useValue: false\n};\nclass RoutesHandler {\n  constructor(routes, router) {\n    this.routes = routes;\n    this.router = router;\n    this.addRoutes();\n  }\n  addRoutes() {\n    this.router?.config?.forEach(({\n      path = '',\n      data\n    }) => {\n      const routes = data?.routes;\n      if (!routes) return;\n      if (Array.isArray(routes)) {\n        this.routes.add(routes);\n      } else {\n        const routesFlatten = flatRoutes([{\n          path,\n          ...routes\n        }], {\n          path: ''\n        });\n        this.routes.add(routesFlatten);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function RoutesHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoutesHandler)(i0.ɵɵinject(RoutesService), i0.ɵɵinject(i1$1.Router, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RoutesHandler,\n      factory: RoutesHandler.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutesHandler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RoutesService\n  }, {\n    type: i1$1.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nfunction flatRoutes(routes, parent) {\n  if (!routes) return [];\n  return routes.reduce((acc, route) => {\n    const {\n      children,\n      ...current\n    } = {\n      ...route,\n      parentName: parent.name,\n      path: (parent.path + '/' + route.path).replace(/\\/\\//g, '/')\n    };\n    acc.push(current, ...flatRoutes(children, current));\n    return acc;\n  }, []);\n}\nvar CoreFeatureKind;\n(function (CoreFeatureKind) {\n  CoreFeatureKind[CoreFeatureKind[\"Options\"] = 0] = \"Options\";\n  CoreFeatureKind[CoreFeatureKind[\"CompareFunctionFactory\"] = 1] = \"CompareFunctionFactory\";\n  CoreFeatureKind[CoreFeatureKind[\"TitleStrategy\"] = 2] = \"TitleStrategy\";\n})(CoreFeatureKind || (CoreFeatureKind = {}));\nfunction makeCoreFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\nfunction withOptions(options = {}) {\n  return makeCoreFeature(CoreFeatureKind.Options, [{\n    provide: 'CORE_OPTIONS',\n    useValue: options\n  }, {\n    provide: CORE_OPTIONS,\n    useFactory: coreOptionsFactory,\n    deps: ['CORE_OPTIONS']\n  }, {\n    provide: TENANT_KEY,\n    useValue: options.tenantKey || '__tenant'\n  }, {\n    provide: LOCALIZATIONS,\n    multi: true,\n    useValue: localizationContributor(options.localizations),\n    deps: [LocalizationService]\n  }, {\n    provide: OTHERS_GROUP,\n    useValue: options.othersGroup || 'AbpUi::OthersGroup'\n  }, {\n    provide: DYNAMIC_LAYOUTS_TOKEN,\n    useValue: options.dynamicLayouts || DEFAULT_DYNAMIC_LAYOUTS\n  }]);\n}\nfunction withTitleStrategy(strategy) {\n  return makeCoreFeature(CoreFeatureKind.TitleStrategy, [{\n    provide: TitleStrategy,\n    useExisting: strategy\n  }]);\n}\nfunction withCompareFuncFactory(factory) {\n  return makeCoreFeature(CoreFeatureKind.CompareFunctionFactory, [{\n    provide: SORT_COMPARE_FUNC,\n    useFactory: factory\n  }]);\n}\nfunction provideAbpCore(...features) {\n  const providers = [LocaleProvider, CookieLanguageProvider, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [Injector],\n    useFactory: getInitialData\n  }, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [Injector],\n    useFactory: localeInitializer\n  }, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [LocalizationService],\n    useFactory: noop\n  }, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [LocalStorageListenerService],\n    useFactory: noop\n  }, {\n    provide: APP_INITIALIZER,\n    multi: true,\n    deps: [RoutesHandler],\n    useFactory: noop\n  }, {\n    provide: SORT_COMPARE_FUNC,\n    useFactory: compareFuncFactory\n  }, {\n    provide: QUEUE_MANAGER,\n    useClass: DefaultQueueManager\n  }, AuthErrorFilterService, IncludeLocalizationResourcesProvider, {\n    provide: TitleStrategy,\n    useExisting: AbpTitleStrategy\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\nfunction provideAbpCoreChild(options = {}) {\n  return makeEnvironmentProviders([{\n    provide: LOCALIZATIONS,\n    multi: true,\n    useValue: localizationContributor(options.localizations),\n    deps: [LocalizationService]\n  }]);\n}\nconst standaloneDirectives = [AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective];\n/**\n * BaseCoreModule is the module that holds\n * all imports, declarations, exports, and entryComponents\n * but not the providers.\n * This module will be imported and exported by all others.\n */\nclass BaseCoreModule {\n  static {\n    this.ɵfac = function BaseCoreModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseCoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BaseCoreModule,\n      declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective],\n      exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, AutofocusDirective, InputEventDebounceDirective, ForDirective, FormSubmitDirective, InitDirective, PermissionDirective, ReplaceableTemplateDirective, StopPropagationDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())],\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseCoreModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe, ...standaloneDirectives],\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule, LocalizationModule, ...standaloneDirectives],\n      declarations: [AbstractNgModelComponent, DynamicLayoutComponent, ReplaceableRouteContainerComponent, RouterOutletComponent, SortPipe, SafeHtmlPipe, ToInjectorPipe, ShortDateTimePipe, ShortTimePipe, ShortDatePipe],\n      providers: [LocalizationPipe, provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * RootCoreModule is the module that will be used at root level\n * and it introduces imports useful at root level (e.g. NGXS)\n */\nclass RootCoreModule {\n  static {\n    this.ɵfac = function RootCoreModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RootCoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RootCoreModule,\n      imports: [BaseCoreModule, LocalizationModule],\n      exports: [BaseCoreModule, LocalizationModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideHttpClient(withXsrfConfiguration({\n        cookieName: 'XSRF-TOKEN',\n        headerName: 'RequestVerificationToken'\n      }))],\n      imports: [BaseCoreModule, LocalizationModule, BaseCoreModule, LocalizationModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootCoreModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BaseCoreModule, LocalizationModule],\n      imports: [BaseCoreModule, LocalizationModule],\n      providers: [provideHttpClient(withXsrfConfiguration({\n        cookieName: 'XSRF-TOKEN',\n        headerName: 'RequestVerificationToken'\n      }))]\n    }]\n  }], null, null);\n})();\n/**\n * CoreModule is the module that is publicly available\n */\nclass CoreModule {\n  /**\n   * @deprecated forRoot method is deprecated, use `provideAbpCore` *function* for config settings.\n   */\n  static forRoot(options = {}) {\n    return {\n      ngModule: RootCoreModule,\n      providers: [provideAbpCore(withOptions(options))]\n    };\n  }\n  /**\n   * @deprecated forChild method is deprecated, use `provideAbpCoreChild` *function* for config settings.\n   */\n  static forChild(options = {}) {\n    return {\n      ngModule: RootCoreModule,\n      providers: [provideAbpCoreChild(options)]\n    };\n  }\n  static {\n    this.ɵfac = function CoreModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CoreModule,\n      imports: [BaseCoreModule],\n      exports: [BaseCoreModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BaseCoreModule, BaseCoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CoreModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BaseCoreModule],\n      imports: [BaseCoreModule]\n    }]\n  }], null, null);\n})();\nclass ShowPasswordDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef);\n  }\n  set abpShowPassword(visible) {\n    const element = this.elementRef.nativeElement;\n    if (!element) return;\n    element.type = visible ? 'text' : 'password';\n  }\n  static {\n    this.ɵfac = function ShowPasswordDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShowPasswordDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ShowPasswordDirective,\n      selectors: [[\"\", \"abpShowPassword\", \"\"]],\n      inputs: {\n        abpShowPassword: \"abpShowPassword\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowPasswordDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpShowPassword]'\n    }]\n  }], null, {\n    abpShowPassword: [{\n      type: Input\n    }]\n  });\n})();\nclass TrackCapsLockDirective {\n  constructor() {\n    this.capsLock = new EventEmitter();\n  }\n  onKeyDown(event) {\n    this.capsLock.emit(this.isCapsLockOpen(event));\n  }\n  onKeyUp(event) {\n    this.capsLock.emit(this.isCapsLockOpen(event));\n  }\n  isCapsLockOpen(e) {\n    const s = String.fromCharCode(e.which);\n    if (s.toUpperCase() === s && s.toLowerCase() !== s && e.shiftKey || s.toUpperCase() !== s && s.toLowerCase() === s && e.shiftKey || e.getModifierState && e.getModifierState('CapsLock')) {\n      return true;\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function TrackCapsLockDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackCapsLockDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackCapsLockDirective,\n      selectors: [[\"\", \"abpCapsLock\", \"\"]],\n      hostBindings: function TrackCapsLockDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function TrackCapsLockDirective_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          }, false, i0.ɵɵresolveWindow)(\"keyup\", function TrackCapsLockDirective_keyup_HostBindingHandler($event) {\n            return ctx.onKeyUp($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      outputs: {\n        capsLock: \"abpCapsLock\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackCapsLockDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[abpCapsLock]'\n    }]\n  }], null, {\n    capsLock: [{\n      type: Output,\n      args: ['abpCapsLock']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['window:keydown', ['$event']]\n    }],\n    onKeyUp: [{\n      type: HostListener,\n      args: ['window:keyup', ['$event']]\n    }]\n  });\n})();\n\n/**\n * @deprecated Use `permissionGuard` *function* instead.\n */\nclass PermissionGuard {\n  constructor() {\n    this.router = inject(Router);\n    this.routesService = inject(RoutesService);\n    this.authService = inject(AuthService);\n    this.permissionService = inject(PermissionService);\n    this.httpErrorReporter = inject(HttpErrorReporterService);\n  }\n  canActivate(route, state) {\n    let {\n      requiredPolicy\n    } = route.data || {};\n    if (!requiredPolicy) {\n      const routeFound = findRoute(this.routesService, getRoutePath(this.router, state.url));\n      requiredPolicy = routeFound?.requiredPolicy;\n    }\n    if (!requiredPolicy) return of(true);\n    return this.permissionService.getGrantedPolicy$(requiredPolicy).pipe(tap(access => {\n      if (!access && this.authService.isAuthenticated) {\n        this.httpErrorReporter.reportError({\n          status: 403\n        });\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function PermissionGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermissionGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PermissionGuard,\n      factory: PermissionGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PermissionGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst permissionGuard = (route, state) => {\n  const router = inject(Router);\n  const routesService = inject(RoutesService);\n  const authService = inject(AuthService);\n  const permissionService = inject(PermissionService);\n  const httpErrorReporter = inject(HttpErrorReporterService);\n  let {\n    requiredPolicy\n  } = route.data || {};\n  if (!requiredPolicy) {\n    const routeFound = findRoute(routesService, getRoutePath(router, state.url));\n    requiredPolicy = routeFound?.requiredPolicy;\n  }\n  if (!requiredPolicy) return of(true);\n  return permissionService.getGrantedPolicy$(requiredPolicy).pipe(tap(access => {\n    if (!access && authService.isAuthenticated) {\n      httpErrorReporter.reportError({\n        status: 403\n      });\n    }\n  }));\n};\nclass ListResultDto {\n  constructor(initialValues = {}) {\n    for (const key in initialValues) {\n      if (checkHasProp(initialValues, key)) {\n        this[key] = initialValues[key];\n      }\n    }\n  }\n}\nclass PagedResultDto extends ListResultDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleObject {\n  constructor(initialValues = {}) {\n    for (const key in initialValues) {\n      if (checkHasProp(initialValues, key) && initialValues[key] !== undefined) {\n        this[key] = initialValues[key];\n      }\n    }\n  }\n}\nclass ExtensibleEntityDto extends ExtensibleObject {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass LimitedResultRequestDto {\n  constructor(initialValues = {}) {\n    this.maxResultCount = 10;\n    for (const key in initialValues) {\n      if (checkHasProp(initialValues, key) && initialValues[key] !== undefined) {\n        this[key] = initialValues[key];\n      }\n    }\n  }\n}\nclass ExtensibleLimitedResultRequestDto extends ExtensibleEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n    this.maxResultCount = 10;\n  }\n}\nclass PagedResultRequestDto extends LimitedResultRequestDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensiblePagedResultRequestDto extends ExtensibleLimitedResultRequestDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass PagedAndSortedResultRequestDto extends PagedResultRequestDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensiblePagedAndSortedResultRequestDto extends ExtensiblePagedResultRequestDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass EntityDto {\n  constructor(initialValues = {}) {\n    for (const key in initialValues) {\n      if (checkHasProp(initialValues, key)) {\n        this[key] = initialValues[key];\n      }\n    }\n  }\n}\nclass CreationAuditedEntityDto extends EntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass CreationAuditedEntityWithUserDto extends CreationAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass AuditedEntityDto extends CreationAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\n/** @deprecated the class signature will change in v8.0 */\nclass AuditedEntityWithUserDto extends AuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass FullAuditedEntityDto extends AuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\n/** @deprecated the class signature will change in v8.0 */\nclass FullAuditedEntityWithUserDto extends FullAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleCreationAuditedEntityDto extends ExtensibleEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleAuditedEntityDto extends ExtensibleCreationAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleAuditedEntityWithUserDto extends ExtensibleAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleCreationAuditedEntityWithUserDto extends ExtensibleCreationAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleFullAuditedEntityDto extends ExtensibleAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass ExtensibleFullAuditedEntityWithUserDto extends ExtensibleFullAuditedEntityDto {\n  constructor(initialValues = {}) {\n    super(initialValues);\n  }\n}\nclass AuthEvent {\n  constructor(type) {\n    this.type = type;\n    this.type = type;\n  }\n}\nclass AuthSuccessEvent extends AuthEvent {\n  constructor(type, info) {\n    super(type);\n    this.type = type;\n    this.info = info;\n  }\n}\nclass AuthInfoEvent extends AuthEvent {\n  constructor(type, info) {\n    super(type);\n    this.type = type;\n    this.info = info;\n  }\n}\nclass AuthErrorEvent extends AuthEvent {\n  constructor(type, reason, params) {\n    super(type);\n    this.type = type;\n    this.reason = reason;\n    this.params = params;\n  }\n}\nclass AbpApiDefinitionService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'abp';\n    this.getByModel = (model, config) => this.restService.request({\n      method: 'GET',\n      url: '/api/abp/api-definition',\n      params: {\n        includeTypes: model.includeTypes\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function AbpApiDefinitionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbpApiDefinitionService)(i0.ɵɵinject(RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbpApiDefinitionService,\n      factory: AbpApiDefinitionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbpApiDefinitionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: RestService\n  }], null);\n})();\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nclass ContainerStrategy {\n  constructor(containerRef) {\n    this.containerRef = containerRef;\n  }\n  prepare() {}\n}\nclass ClearContainerStrategy extends ContainerStrategy {\n  getIndex() {\n    return 0;\n  }\n  prepare() {\n    this.containerRef.clear();\n  }\n}\nclass InsertIntoContainerStrategy extends ContainerStrategy {\n  constructor(containerRef, index) {\n    super(containerRef);\n    this.index = index;\n  }\n  getIndex() {\n    return Math.min(Math.max(0, this.index), this.containerRef.length);\n  }\n}\nconst CONTAINER_STRATEGY = {\n  Clear(containerRef) {\n    return new ClearContainerStrategy(containerRef);\n  },\n  Append(containerRef) {\n    return new InsertIntoContainerStrategy(containerRef, containerRef.length);\n  },\n  Prepend(containerRef) {\n    return new InsertIntoContainerStrategy(containerRef, 0);\n  },\n  Insert(containerRef, index) {\n    return new InsertIntoContainerStrategy(containerRef, index);\n  }\n};\nclass ContentSecurityStrategy {\n  constructor(nonce) {\n    this.nonce = nonce;\n  }\n}\nclass LooseContentSecurityStrategy extends ContentSecurityStrategy {\n  constructor(nonce) {\n    super(nonce);\n  }\n  applyCSP(element) {\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n  }\n}\nclass NoContentSecurityStrategy extends ContentSecurityStrategy {\n  constructor() {\n    super();\n  }\n  applyCSP(_) {}\n}\nconst CONTENT_SECURITY_STRATEGY = {\n  Loose(nonce) {\n    return new LooseContentSecurityStrategy(nonce);\n  },\n  None() {\n    return new NoContentSecurityStrategy();\n  }\n};\nclass ContentStrategy {\n  constructor(content, domStrategy = DOM_STRATEGY.AppendToHead(), contentSecurityStrategy = CONTENT_SECURITY_STRATEGY.None(), options = {}) {\n    this.content = content;\n    this.domStrategy = domStrategy;\n    this.contentSecurityStrategy = contentSecurityStrategy;\n    this.options = options;\n  }\n  insertElement() {\n    const element = this.createElement();\n    if (this.options && Object.keys(this.options).length > 0) {\n      Object.keys(this.options).forEach(key => {\n        if (this.options[key]) {\n          element[key] = this.options[key];\n        }\n      });\n    }\n    this.contentSecurityStrategy.applyCSP(element);\n    this.domStrategy.insertElement(element);\n    return element;\n  }\n}\nclass StyleContentStrategy extends ContentStrategy {\n  createElement() {\n    const element = document.createElement('style');\n    element.textContent = this.content;\n    return element;\n  }\n}\nclass ScriptContentStrategy extends ContentStrategy {\n  createElement() {\n    const element = document.createElement('script');\n    element.textContent = this.content;\n    return element;\n  }\n}\nconst CONTENT_STRATEGY = {\n  AppendScriptToBody(content, options) {\n    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToBody(), undefined, options);\n  },\n  AppendScriptToHead(content, options) {\n    return new ScriptContentStrategy(content, DOM_STRATEGY.AppendToHead(), undefined, options);\n  },\n  AppendStyleToHead(content, options) {\n    return new StyleContentStrategy(content, DOM_STRATEGY.AppendToHead(), undefined, options);\n  },\n  PrependStyleToHead(content, options) {\n    return new StyleContentStrategy(content, DOM_STRATEGY.PrependToHead(), undefined, options);\n  }\n};\nclass ContextStrategy {\n  constructor(context) {\n    this.context = context;\n  }\n  setContext(componentRef) {\n    return this.context;\n  }\n}\nclass NoContextStrategy extends ContextStrategy {\n  constructor() {\n    super(undefined);\n  }\n}\nclass ComponentContextStrategy extends ContextStrategy {\n  setContext(componentRef) {\n    Object.keys(this.context).forEach(key => componentRef.instance[key] = this.context[key]);\n    componentRef.changeDetectorRef.detectChanges();\n    return this.context;\n  }\n}\nclass TemplateContextStrategy extends ContextStrategy {\n  setContext() {\n    return this.context;\n  }\n}\nconst CONTEXT_STRATEGY = {\n  None() {\n    return new NoContextStrategy();\n  },\n  Component(context) {\n    return new ComponentContextStrategy(context);\n  },\n  Template(context) {\n    return new TemplateContextStrategy(context);\n  }\n};\nclass LoadingStrategy {\n  constructor(path, domStrategy = DOM_STRATEGY.AppendToHead(), crossOriginStrategy = CROSS_ORIGIN_STRATEGY.Anonymous()) {\n    this.path = path;\n    this.domStrategy = domStrategy;\n    this.crossOriginStrategy = crossOriginStrategy;\n  }\n  createStream() {\n    this.element = this.createElement();\n    return of(null).pipe(switchMap(() => fromLazyLoad(this.element, this.domStrategy, this.crossOriginStrategy)));\n  }\n}\nclass ScriptLoadingStrategy extends LoadingStrategy {\n  constructor(src, domStrategy, crossOriginStrategy) {\n    super(src, domStrategy, crossOriginStrategy);\n  }\n  createElement() {\n    const element = document.createElement('script');\n    element.src = this.path;\n    return element;\n  }\n}\nclass StyleLoadingStrategy extends LoadingStrategy {\n  constructor(href, domStrategy, crossOriginStrategy) {\n    super(href, domStrategy, crossOriginStrategy);\n  }\n  createElement() {\n    const element = document.createElement('link');\n    element.rel = 'stylesheet';\n    element.href = this.path;\n    return element;\n  }\n}\nconst LOADING_STRATEGY = {\n  AppendScriptToBody(src) {\n    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.None());\n  },\n  AppendAnonymousScriptToBody(src, integrity) {\n    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToBody(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));\n  },\n  AppendAnonymousScriptToHead(src, integrity) {\n    return new ScriptLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));\n  },\n  AppendAnonymousStyleToHead(src, integrity) {\n    return new StyleLoadingStrategy(src, DOM_STRATEGY.AppendToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));\n  },\n  PrependAnonymousScriptToHead(src, integrity) {\n    return new ScriptLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));\n  },\n  PrependAnonymousStyleToHead(src, integrity) {\n    return new StyleLoadingStrategy(src, DOM_STRATEGY.PrependToHead(), CROSS_ORIGIN_STRATEGY.Anonymous(integrity));\n  }\n};\nclass ProjectionStrategy {\n  constructor(content) {\n    this.content = content;\n  }\n}\nclass ComponentProjectionStrategy extends ProjectionStrategy {\n  constructor(component, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {\n    super(component);\n    this.containerStrategy = containerStrategy;\n    this.contextStrategy = contextStrategy;\n  }\n  injectContent(injector) {\n    this.containerStrategy.prepare();\n    const resolver = injector.get(ComponentFactoryResolver);\n    const factory = resolver.resolveComponentFactory(this.content);\n    const componentRef = this.containerStrategy.containerRef.createComponent(factory, this.containerStrategy.getIndex(), injector);\n    this.contextStrategy.setContext(componentRef);\n    return componentRef;\n  }\n}\nclass RootComponentProjectionStrategy extends ProjectionStrategy {\n  constructor(component, contextStrategy = CONTEXT_STRATEGY.None(), domStrategy = DOM_STRATEGY.AppendToBody()) {\n    super(component);\n    this.contextStrategy = contextStrategy;\n    this.domStrategy = domStrategy;\n  }\n  injectContent(injector) {\n    const appRef = injector.get(ApplicationRef);\n    const resolver = injector.get(ComponentFactoryResolver);\n    const componentRef = resolver.resolveComponentFactory(this.content).create(injector);\n    this.contextStrategy.setContext(componentRef);\n    appRef.attachView(componentRef.hostView);\n    const element = componentRef.hostView.rootNodes[0];\n    this.domStrategy.insertElement(element);\n    return componentRef;\n  }\n}\nclass TemplateProjectionStrategy extends ProjectionStrategy {\n  constructor(templateRef, containerStrategy, contextStrategy = CONTEXT_STRATEGY.None()) {\n    super(templateRef);\n    this.containerStrategy = containerStrategy;\n    this.contextStrategy = contextStrategy;\n  }\n  injectContent() {\n    this.containerStrategy.prepare();\n    const embeddedViewRef = this.containerStrategy.containerRef.createEmbeddedView(this.content, this.contextStrategy.context, this.containerStrategy.getIndex());\n    embeddedViewRef.detectChanges();\n    return embeddedViewRef;\n  }\n}\nconst PROJECTION_STRATEGY = {\n  AppendComponentToBody(component, context) {\n    return new RootComponentProjectionStrategy(component, context && CONTEXT_STRATEGY.Component(context));\n  },\n  AppendComponentToContainer(component, containerRef, context) {\n    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Component(context));\n  },\n  AppendTemplateToContainer(templateRef, containerRef, context) {\n    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Append(containerRef), context && CONTEXT_STRATEGY.Template(context));\n  },\n  PrependComponentToContainer(component, containerRef, context) {\n    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Component(context));\n  },\n  PrependTemplateToContainer(templateRef, containerRef, context) {\n    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Prepend(containerRef), context && CONTEXT_STRATEGY.Template(context));\n  },\n  ProjectComponentToContainer(component, containerRef, context) {\n    return new ComponentProjectionStrategy(component, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Component(context));\n  },\n  ProjectTemplateToContainer(templateRef, containerRef, context) {\n    return new TemplateProjectionStrategy(templateRef, CONTAINER_STRATEGY.Clear(containerRef), context && CONTEXT_STRATEGY.Template(context));\n  }\n};\nfunction validateMinAge({\n  age = 18\n} = {}) {\n  return control => {\n    if (['', null, undefined].indexOf(control.value) > -1) return null;\n    return isValidMinAge(control.value, age) ? null : {\n      minAge: {\n        age\n      }\n    };\n  };\n}\nfunction isValidMinAge(value, minAge) {\n  const date = new Date();\n  date.setFullYear(date.getFullYear() - minAge);\n  date.setHours(23, 59, 59, 999);\n  return Number(new Date(value)) <= date.valueOf();\n}\nfunction validateCreditCard() {\n  return control => {\n    if (['', null, undefined].indexOf(control.value) > -1) return null;\n    return isValidCreditCard(String(control.value)) ? null : {\n      creditCard: true\n    };\n  };\n}\nfunction isValidCreditCard(value) {\n  value = value.replace(/[ -]/g, '');\n  if (!/^[0-9]{13,19}$/.test(value)) return false;\n  let checksum = 0;\n  let multiplier = 1;\n  for (let i = value.length; i > 0; i--) {\n    const digit = Number(value[i - 1]) * multiplier;\n    checksum += digit % 10 + ~~(digit / 10);\n    multiplier = multiplier * 2 % 3;\n  }\n  return checksum % 10 === 0;\n}\nfunction validateRange({\n  maximum = Infinity,\n  minimum = 0\n} = {}) {\n  return control => {\n    if (['', null, undefined].indexOf(control.value) > -1) return null;\n    const value = Number(control.value);\n    return getMinError(value, minimum, maximum) || getMaxError(value, maximum, minimum);\n  };\n}\nfunction getMaxError(value, max, min) {\n  return value > max ? {\n    range: {\n      max,\n      min\n    }\n  } : null;\n}\nfunction getMinError(value, min, max) {\n  return value < min ? {\n    range: {\n      min,\n      max\n    }\n  } : null;\n}\nfunction validateRequired({\n  allowEmptyStrings\n} = {}) {\n  // note: please do not remove name of the function, it is used in function compare with type 'RequiredError'\n  const required = control => {\n    return isValidRequired(control.value, allowEmptyStrings) ? null : {\n      required: true\n    };\n  };\n  return required;\n}\nfunction isValidRequired(value, allowEmptyStrings) {\n  if (value || value === 0 || value === false) return true;\n  if (allowEmptyStrings && value === '') return true;\n  return false;\n}\nfunction validateStringLength({\n  maximumLength = Infinity,\n  minimumLength = 0\n} = {}) {\n  return control => {\n    if (['', null, undefined].indexOf(control.value) > -1) return null;\n    const value = String(control.value);\n    return getMinLengthError(value, minimumLength) || getMaxLengthError(value, maximumLength);\n  };\n}\nfunction getMaxLengthError(value, requiredLength) {\n  return value.length > requiredLength ? {\n    maxlength: {\n      requiredLength\n    }\n  } : null;\n}\nfunction getMinLengthError(value, requiredLength) {\n  return value.length < requiredLength ? {\n    minlength: {\n      requiredLength\n    }\n  } : null;\n}\nfunction validateUniqueCharacter() {\n  return control => {\n    if (isNullOrEmpty(control.value)) return null;\n    return isUnqiueCharacter(control.value) ? null : {\n      uniqueCharacter: true\n    };\n  };\n}\nfunction isUnqiueCharacter(value) {\n  const set = new Set(value.split(''));\n  return set.size == value.length;\n}\nfunction validateUrl() {\n  return control => {\n    if (isNullOrEmpty(control.value)) return null;\n    return isValidUrl(control.value) ? null : {\n      url: true\n    };\n  };\n}\nfunction isValidUrl(value) {\n  if (/^http(s)?:\\/\\/[^/]/.test(value) || /^ftp:\\/\\/[^/]/.test(value)) {\n    const a = document.createElement('a');\n    a.href = value;\n    return !!a.host;\n  }\n  return false;\n}\nconst onlyLetterAndNumberRegex = /^[a-zA-Z0-9]+$/;\nfunction validateUsername({\n  pattern = /.*/\n} = {\n  pattern: onlyLetterAndNumberRegex\n}) {\n  return control => {\n    const isValid = isValidUserName(control.value, pattern);\n    return isValid ? null : {\n      usernamePattern: {\n        actualValue: control.value\n      }\n    };\n  };\n}\nfunction isValidUserName(value, pattern) {\n  if (isNullOrEmpty(value)) return true;\n  return pattern.test(value);\n}\nconst AbpValidators = {\n  creditCard: validateCreditCard,\n  emailAddress: () => Validators.email,\n  minAge: validateMinAge,\n  range: validateRange,\n  required: validateRequired,\n  stringLength: validateStringLength,\n  url: validateUrl,\n  username: validateUsername,\n  uniqueCharacter: validateUniqueCharacter\n};\nclass ApiInterceptor {\n  constructor(httpWaitService) {\n    this.httpWaitService = httpWaitService;\n  }\n  getAdditionalHeaders(existingHeaders) {\n    return existingHeaders || new HttpHeaders();\n  }\n  intercept(request, next) {\n    this.httpWaitService.addRequest(request);\n    return next.handle(request).pipe(finalize(() => this.httpWaitService.deleteRequest(request)));\n  }\n  static {\n    this.ɵfac = function ApiInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApiInterceptor)(i0.ɵɵinject(HttpWaitService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ApiInterceptor,\n      factory: ApiInterceptor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ApiInterceptor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: HttpWaitService\n  }], null);\n})();\n\n// export * from './lib/handlers';\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { APP_INIT_ERROR_HANDLERS, AbpApiDefinitionService, AbpApplicationConfigurationService, AbpApplicationLocalizationService, AbpLocalStorageService, AbpTenantService, AbpTitleStrategy, AbpValidators, AbpWindowService, AbstractAuthErrorFilter, AbstractNavTreeService, AbstractNgModelComponent, AbstractTreeService, ApiInterceptor, AuditedEntityDto, AuditedEntityWithUserDto, AuthErrorEvent, AuthErrorFilterService, AuthEvent, AuthGuard, AuthInfoEvent, AuthService, AuthSuccessEvent, AutofocusDirective, BaseCoreModule, BaseTreeNode, CHECK_AUTHENTICATION_STATE_FN_KEY, CONTAINER_STRATEGY, CONTENT_SECURITY_STRATEGY, CONTENT_STRATEGY, CONTEXT_STRATEGY, COOKIE_LANGUAGE_KEY, CORE_OPTIONS, CROSS_ORIGIN_STRATEGY, ClearContainerStrategy, ComponentContextStrategy, ComponentProjectionStrategy, ConfigStateService, ContainerStrategy, ContentProjectionService, ContentSecurityStrategy, ContentStrategy, ContextStrategy, CookieLanguageProvider, CoreFeatureKind, CoreModule, CreationAuditedEntityDto, CreationAuditedEntityWithUserDto, CrossOriginStrategy, DEFAULT_DYNAMIC_LAYOUTS, DISABLE_PROJECT_NAME, DOM_STRATEGY, DYNAMIC_LAYOUTS_TOKEN, DefaultQueueManager, DomInsertionService, DomStrategy, DynamicLayoutComponent, EntityDto, EnvironmentService, ExtensibleAuditedEntityDto, ExtensibleAuditedEntityWithUserDto, ExtensibleCreationAuditedEntityDto, ExtensibleCreationAuditedEntityWithUserDto, ExtensibleEntityDto, ExtensibleFullAuditedEntityDto, ExtensibleFullAuditedEntityWithUserDto, ExtensibleLimitedResultRequestDto, ExtensibleObject, ExtensiblePagedAndSortedResultRequestDto, ExtensiblePagedResultRequestDto, ExternalHttpClient, ForDirective, FormSubmitDirective, FullAuditedEntityDto, FullAuditedEntityWithUserDto, HttpErrorReporterService, HttpWaitService, INCUDE_LOCALIZATION_RESOURCES_TOKEN, INJECTOR_PIPE_DATA_TOKEN, IS_EXTERNAL_REQUEST, IncludeLocalizationResourcesProvider, InitDirective, InputEventDebounceDirective, InsertIntoContainerStrategy, InternalStore, InternetConnectionService, LIST_QUERY_DEBOUNCE_TIME, LOADER_DELAY, LOADING_STRATEGY, LOCALIZATIONS, LazyLoadService, LazyModuleFactory, LimitedResultRequestDto, ListResultDto, ListService, LoadingStrategy, LocalStorageListenerService, LocaleId, LocaleProvider, LocalizationModule, LocalizationPipe, LocalizationService, LooseContentSecurityStrategy, MultiTenancyService, NAVIGATE_TO_MANAGE_PROFILE, NavigationEvent, NoContentSecurityStrategy, NoContextStrategy, NoCrossOriginStrategy, OTHERS_GROUP, index as ObjectExtending, PIPE_TO_LOGIN_FN_KEY, PROJECTION_STRATEGY, PagedAndSortedResultRequestDto, PagedResultDto, PagedResultRequestDto, PermissionDirective, PermissionGuard, PermissionService, ProjectionStrategy, QUEUE_MANAGER, ReplaceableComponentsService, ReplaceableRouteContainerComponent, ReplaceableTemplateDirective, ResourceWaitService, RestService, RootComponentProjectionStrategy, RootCoreModule, RouterEvents, RouterOutletComponent, RouterWaitService, RoutesService, SET_TOKEN_RESPONSE_TO_STORAGE_FN_KEY, SORT_COMPARE_FUNC, SafeHtmlPipe, ScriptContentStrategy, ScriptLoadingStrategy, SessionStateService, ShortDatePipe, ShortDateTimePipe, ShortTimePipe, ShowPasswordDirective, SortPipe, StopPropagationDirective, StyleContentStrategy, StyleLoadingStrategy, SubscriptionService, TENANT_KEY, TENANT_NOT_FOUND_BY_NAME, TemplateContextStrategy, TemplateProjectionStrategy, ToInjectorPipe, TrackByService, TrackCapsLockDirective, WebHttpUrlEncodingCodec, authGuard, checkHasProp, compareFuncFactory, coreOptionsFactory, createGroupMap, createLocalizationPipeKeyGenerator, createLocalizer, createLocalizerWithFallback, createMapFromList, createTokenParser, createTreeFromList, createTreeNodeFilterCreator, deepMerge, differentLocales, downloadBlob, escapeHtmlChars, exists, featuresFactory, findRoute, fromLazyLoad, generateHash, generatePassword, getInitialData, getLocaleDirection, getPathName, getRemoteEnv, getRoutePath, getShortDateFormat, getShortDateShortTimeFormat, getShortTimeFormat, interpolate, isArray, isNode, isNullOrEmpty, isNullOrUndefined, isNumber, isObject, isObjectAndNotArray, isObjectAndNotArrayNotNode, isUndefinedOrEmptyString, localeInitializer, localizationContributor, localizations$, mapEnumToOptions, noop, parseTenantFromUrl, permissionGuard, provideAbpCore, provideAbpCoreChild, pushValueTo, reloadRoute, setLanguageToCookie, trackBy, trackByDeep, uuid, validateCreditCard, validateMinAge, validateRange, validateRequired, validateStringLength, validateUniqueCharacter, validateUrl, withCompareFuncFactory, withOptions, withTitleStrategy };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,oBAAoB;AAgBxB,SAAS,QAAQ,QAAQ,QAAQ;AAC/B,MAAI,WAAW,QAAQ;AACrB,WAAO;AAAA,EACT;AAIA,MAAI,WAAW,UAAU,WAAW,QAAQ;AAC1C,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,OAAO;AAAA,EAE5B,CAAC,EAAE,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,SAAS,KAAK,MAAM,GACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,WAAW,OAAO,MAAM,GAAG;AAE7B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,cAAc,QAAQ,MAAM;AAAA,EACrC;AACA,MAAI,CAAC,EAAE,SAAS,KAAK,MAAM,KAAK,gBAAgB;AAC9C,WAAO,cAAc,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,CAAC;AAAA,EAC7D;AACA,MAAI,CAAC,EAAE,SAAS,KAAK,MAAM,KAAK,mBAAmB;AACjD,WAAO,eAAe,QAAQ,MAAM;AAAA,EACtC;AACA,SAAO,sBAAsB,QAAQ,MAAM;AAC7C;AACA,SAAS,sBAAsB,QAAQ,QAAQ;AAE7C,SAAO,OAAO,SAAS,MAAM,OAAO,SAAS;AAC/C;AACA,SAAS,cAAc,QAAQ,QAAQ;AACrC,MAAI,MAAM,OAAO;AACjB,MAAI,OAAO,OAAO,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,CAAC,QAAQ,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,QAAQ;AACtC,MAAI,QAAQ,OAAO,KAAK,MAAM;AAC9B,MAAI,MAAM,MAAM;AAChB,MAAI,OAAO,OAAO,KAAK,MAAM,EAAE,QAAQ;AACrC,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,EAAE,OAAO,eAAe,IAAI,KAAK,QAAQ,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI;AACzE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AC/EA,IAAI,kBAAkB;AAetB,SAAS,MAAM,KAAK;AAClB,MAAI,SAAS;AACb,MAAI,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE;AAC5C,MAAI,QAAQ,OAAO;AACjB,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC,CAAC;AAAA,EACpD;AACA,MAAI,QAAQ,OAAO;AACjB,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,QAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACjE;AACA,MAAI,QAAQ,QAAQ;AAClB,WAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,EAC/B;AACA,MAAI,QAAQ,UAAU;AACpB,WAAO,OAAO,IAAI,QAAQ,eAAe,GAAG,CAAC;AAAA,EAC/C;AACA,MAAI,QAAQ,WAAW,QAAQ,UAAU;AACvC,aAAS,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AACpC,aAAS,OAAO,KAAK;AAEnB,aAAO,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF;AAEA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,OAAO,OAAO,OAAO,SAAS,UAAU;AAC1C,WAAO,OAAO,OAAO;AAAA,EACvB,OAAO;AACL,QAAI,QAAQ,CAAC;AACb,WAAO,UAAU,MAAM,KAAK,GAAG;AAC/B,WAAO,cAAc,MAAM,KAAK,GAAG;AACnC,WAAO,aAAa,MAAM,KAAK,GAAG;AAClC,WAAO,UAAU,MAAM,KAAK,GAAG;AAC/B,WAAO,WAAW,MAAM,KAAK,GAAG;AAChC,WAAO,MAAM,KAAK,EAAE;AAAA,EACtB;AACF;;;AClCA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,qBAAqB,OAAO,MAAM;AAAA,EAClD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,QAAQ,OAAO,iBAAiB;AACrC,SAAK,UAAU,WAAS;AACxB,SAAK,eAAe,WAAS;AAAA,EAC/B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,YAAQ,KAAK,QAAQ,OAAO,KAAK,MAAM;AACvC,QAAI,KAAK,aAAa,OAAO,KAAK,MAAM,MAAM,SAAS,KAAK,SAAU;AACtE,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,SAAS,KAAK,aAAa,OAAO,KAAK,MAAM,KAAK;AACvD,SAAK,MAAM,aAAa;AAAA,EAC1B;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAAA,MAAC;AAAA,MAC/D,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc;AACZ,YAAQ,MAAM,yEAAyE;AACvF,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,aAAO,KAAK,qBAAqB,YAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,WAAU;AAAA,MACnB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,YAAY,MAAM;AACtB,UAAQ,MAAM,yEAAyE;AACvF,SAAO;AACT;AAKA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,iBAAiB;AACf,YAAQ,MAAM,yEAAyE;AAAA,EACzF;AAAA,EACA,IAAI,OAAO;AACT,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,eAAe;AACpB,WAAO,QAAQ,QAAQ,MAAS;AAAA,EAClC;AAAA,EACA,MAAM,QAAQ;AACZ,SAAK,eAAe;AACpB,WAAO,GAAG,MAAS;AAAA,EACrB;AAAA,EACA,OAAO,aAAa;AAClB,SAAK,eAAe;AACpB,WAAO,GAAG,MAAS;AAAA,EACrB;AAAA,EACA,gBAAgB,aAAa;AAAA,EAAC;AAAA,EAC9B,IAAI,iBAAiB;AACnB,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AAAA,EACA,IAAI,kBAAkB;AACpB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,WAAW,YAAY,SAAS;AAC9C,YAAQ,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,EACpD;AAAA,EACA,2BAA2B;AACzB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AACf,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,eAAe;AACpB,WAAO,QAAQ,QAAQ,MAAS;AAAA,EAClC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,0BAAN,MAA8B;AAAC;AAC/B,IAAM,yBAAN,cAAqC,wBAAwB;AAAA,EAC3D,iBAAiB;AACf,YAAQ,MAAM,yEAAyE;AAAA,EACzF;AAAA,EACA,IAAI,IAAI;AACN,SAAK,eAAe;AACpB,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AAAA,EACA,IAAIA,SAAQ;AACV,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,MAAM,MAAM;AACV,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,OAAO,IAAI;AACT,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,OAAO;AACT,SAAK,eAAe;AACpB,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AACF;AACA,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,SAAS,wBAAwB,eAAe;AAC9C,MAAI,eAAe;AACjB,mBAAe,KAAK,CAAC,GAAG,eAAe,OAAO,GAAG,aAAa,CAAC;AAAA,EACjE;AACF;AACA,IAAM,iBAAiB,IAAI,gBAAgB,CAAC,CAAC;AAC7C,IAAM,eAAe,IAAI,eAAe,cAAc;AACtD,SAAS,mBAAmB,IAEzB;AAFyB,MACvB,oBADuB,IACvB;AAEH,SAAO,mBACF;AAEP;AAGA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,uGAAuG,KAAK,MAAM,IAAI,QAAQ;AACvI;AACA,SAAS,gBAAgB,cAAc;AACrC,SAAO,CAAC,cAAc,KAAK,iBAAiB;AAC1C,QAAI,iBAAiB,IAAK,QAAO;AACjC,UAAM,WAAW,cAAc,SAAS,YAAY;AACpD,QAAI,CAAC,SAAU,QAAO;AACtB,WAAO,SAAS,GAAG,KAAK;AAAA,EAC1B;AACF;AACA,SAAS,4BAA4B,cAAc;AACjD,QAAM,mBAAmB,yBAAyB,YAAY;AAC9D,SAAO,CAAC,eAAe,MAAM,iBAAiB;AAC5C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,iBAAiB,eAAe,IAAI;AACxC,WAAO,aAAa;AAAA,EACtB;AACF;AACA,SAAS,mCAAmC,cAAc;AACxD,QAAM,mBAAmB,yBAAyB,YAAY;AAC9D,SAAO,CAAC,eAAe,MAAM,eAAe;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB,eAAe,IAAI;AACxC,WAAO,CAAC,eAAe,aAAa,iBAAiB,MAAM,MAAM,GAAG,YAAY,KAAK,GAAG;AAAA,EAC1F;AACF;AACA,SAAS,yBAAyB,cAAc;AAC9C,QAAM,WAAW,gBAAgB,YAAY;AAC7C,SAAO,CAAC,eAAe,SAAS;AAC9B,oBAAgB,cAAc,OAAO,aAAa,uBAAuB,EAAE,EAAE,OAAO,OAAO;AAC3F,UAAM,gBAAgB,cAAc;AACpC,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,YAAM,eAAe,cAAc,CAAC;AACpC,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,YAAY,SAAS,cAAc,KAAK,IAAI;AAClD,YAAI,UAAW,QAAO;AAAA,UACpB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,cAAc;AAAA,MACd,KAAK;AAAA,MACL,WAAW;AAAA,IACb;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,SAAO;AACZ,UAAM,SAAS,CAAC;AAChB,UAAM,QAAQ,OAAO,QAAQ,OAAO,KAAK,EAAE,QAAQ,6BAA6B,CAAC,GAAG,UAAU;AAC5F,aAAO,KAAK,KAAK;AACjB,aAAO;AAAA,IACT,CAAC;AACD,UAAM,WAAW,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,MAAM,CAAC;AAChD,WAAO,QAAQ,OAAO,CAAC,KAAK,GAAG,MAAM;AACnC,YAAM,MAAM,OAAO,CAAC;AACpB,UAAI,GAAG,IAAI,CAAC,GAAI,IAAI,GAAG,KAAK,CAAC,GAAI,CAAC,EAAE,OAAO,OAAO;AAClD,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACF;AACA,SAAS,YAAY,MAAM,QAAQ;AACjC,SAAO,KAAK,QAAQ,gCAAgC,CAAC,GAAG,OAAO,UAAU,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ,QAAQ,GAAG;AACtH;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU,WAAW,MAAM,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,MAAM,QAAQ,IAAI;AACjJ;AACA,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,eAAe,oBAAoB,WAAW,KAAK,UAAU;AAC3D,WAAO,mBAAmB,cAAc,QAAQ;AAAA,EAClD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,SAAY,QAAQ,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,YAAY,OAAO;AAC1B,SAAO,aAAW;AAChB,UAAM,KAAK,OAAO;AAClB,WAAO;AAAA,EACT;AACF;AACA,SAAS,OAAO;AACd,QAAM,KAAK,WAAY;AAAA,EAAC;AACxB,SAAO;AACT;AACA,SAAS,yBAAyB,OAAO;AACvC,SAAO,UAAU,UAAa,UAAU;AAC1C;AACA,SAAS,kBAAkB,KAAK;AAC9B,SAAO,QAAQ,QAAQ,QAAQ;AACjC;AACA,SAAS,cAAc,KAAK;AAC1B,SAAO,QAAQ,QAAQ,QAAQ,UAAa,QAAQ;AACtD;AACA,SAAS,OAAO,KAAK;AACnB,SAAO,CAAC,kBAAkB,GAAG;AAC/B;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,eAAe;AACxB;AACA,SAAS,QAAQ,KAAK;AACpB,SAAO,MAAM,QAAQ,GAAG;AAC1B;AACA,SAAS,oBAAoB,KAAK;AAChC,SAAO,SAAS,GAAG,KAAK,CAAC,QAAQ,GAAG;AACtC;AACA,SAAS,OAAO,KAAK;AACnB,SAAO,eAAe;AACxB;AACA,SAAS,2BAA2B,KAAK;AACvC,SAAO,oBAAoB,GAAG,KAAK,CAAC,OAAO,GAAG;AAChD;AACA,SAAS,aAAa,QAAQ,KAAK;AACjC,SAAO,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG;AACzD;AACA,SAAS,mBAAmB,oBAAoB;AAC9C,QAAM,iBAAiB,mBAAmB,QAAQ,4CAA4C;AAC9F,SAAO,eAAe;AACxB;AACA,SAAS,mBAAmB,oBAAoB;AAC9C,QAAM,iBAAiB,mBAAmB,QAAQ,4CAA4C;AAC9F,SAAO,gBAAgB,kBAAkB,QAAQ,MAAM,GAAG;AAC5D;AACA,SAAS,4BAA4B,oBAAoB;AACvD,QAAM,iBAAiB,mBAAmB,QAAQ,4CAA4C;AAC9F,SAAO,GAAG,eAAe,gBAAgB,IAAI,gBAAgB,kBAAkB,QAAQ,MAAM,GAAG,CAAC;AACnG;AACA,SAAS,UAAU,QAAQ,QAAQ;AACjC,MAAI,2BAA2B,MAAM,KAAK,2BAA2B,MAAM,GAAG;AAC5E,WAAO,qBAAqB,QAAQ,MAAM;AAAA,EAC5C,WAAW,kBAAkB,MAAM,KAAK,kBAAkB,MAAM,GAAG;AACjE,WAAO,CAAC;AAAA,EACV,OAAO;AACL,WAAO,OAAO,MAAM,IAAI,SAAS;AAAA,EACnC;AACF;AACA,SAAS,qBAAqB,QAAQ,QAAQ;AAC5C,QAAM,mBAAmB,kBAAkB,MAAM,KAAK,kBAAkB,MAAM;AAAA,EAE9E,QAAQ,MAAM,KAAK,QAAQ,MAAM;AAAA,EAEjC,CAAC,SAAS,MAAM,KAAK,CAAC,SAAS,MAAM;AAAA,EAErC,OAAO,MAAM,KAAK,OAAO,MAAM;AAK/B,MAAI,kBAAkB;AACpB,WAAO,OAAO,MAAM,IAAI,SAAS;AAAA,EACnC;AACA,QAAM,eAAe,OAAO,KAAK,MAAM;AACvC,QAAM,eAAe,OAAO,KAAK,MAAM;AACvC,QAAM,aAAa,IAAI,IAAI,aAAa,OAAO,YAAY,CAAC;AAC5D,SAAO,CAAC,GAAG,UAAU,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC7C,WAAO,GAAG,IAAI,qBAAqB,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAC3D,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,YAAY,cAAc;AACxB,SAAK,eAAe;AACpB,SAAK,SAAS,IAAI,gBAAgB,KAAK,YAAY;AACnD,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,aAAa,CAAC,UAAU,YAAY,sBAAY,KAAK,OAAO,KAAK,IAAI,QAAQ,GAAG,qBAAqB,SAAS,CAAC;AACpH,SAAK,cAAc,CAAC,UAAU,WAAW,OAAK,MAAM,WAAc,KAAK,QAAQ,KAAK,IAAI,QAAQ,GAAG,OAAO,QAAQ,CAAC;AAAA,EACrH;AAAA,EACA,MAAM,OAAO;AACX,QAAI,eAAe;AACnB,QAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtD,qBAAe,kCACV,KAAK,QACL;AAAA,IAEP;AACA,SAAK,OAAO,KAAK,YAAY;AAC7B,SAAK,QAAQ,KAAK,YAAY;AAAA,EAChC;AAAA,EACA,UAAU,OAAO;AAEf,SAAK,OAAO,KAAK,UAAU,KAAK,OAAO,KAAK,CAAC;AAC7C,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,IAAI,OAAO;AACT,SAAK,OAAO,KAAK,KAAK;AACtB,SAAK,QAAQ,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,QAAQ;AACN,SAAK,IAAI,KAAK,YAAY;AAAA,EAC5B;AACF;AACA,IAAM,cAAc,SAAO,WAAS,OAAO,KAAK,GAAG,KAAK,KAAK,SAAS,OAAO,KAAK,QAAQ;AAC1F,IAAM,cAAc,YAAU;AAC5B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,SAAO,OAAO,SAAS,GAAG,IAAI,SAAS,SAAS;AAClD;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,QAAQ,IAAI,cAAc,CAAC,CAAC;AAAA,EACnC;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,MAAM,WAAW,WAAS,KAAK;AAAA,EAC7C;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,UAAU,KAAK;AACb,WAAO,YAAY,GAAG,EAAE,KAAK,MAAM,OAAO,IAAI;AAAA,EAChD;AAAA,EACA,WAAW,KAAK;AACd,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,IAAI,EAAE,KAAK,IAAI,YAAY,GAAG,CAAC,CAAC;AAAA,EAC9E;AAAA,EACA,SAAS,aAAa;AACpB,SAAK,MAAM,IAAI,WAAW;AAAA,EAC5B;AAAA,EACA,YAAY;AACV,UAAM,SAAS,KAAK,MAAM,OAAO,aAAa;AAC9C,WAAO,YAAY,MAAM;AAAA,EAC3B;AAAA,EACA,aAAa;AACX,WAAO,KAAK,MAAM,WAAW,WAAS,OAAO,aAAa,MAAM,EAAE,KAAK,IAAI,WAAW,CAAC;AAAA,EACzF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,MAAM,OAAO,aAAa,iBAAiB,CAAC;AAAA,EAC1D;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,MAAM,WAAW,WAAS,OAAO,aAAa,iBAAiB,CAAC,CAAC;AAAA,EAC/E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,SAAS,aAAa;AAAA,EACpC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,WAAW,KAAK,KAAK;AAC1B,SAAK,SAAS,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC5C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,aAAa,UAAU,aAAa;AAC3C,QAAM,qBAAqB,SAAS,IAAI,kBAAkB;AAC1D,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,UAAU,CAAC;AAAA,IACX,SAAS;AAAA,IACT;AAAA,EACF,IAAI,aAAa,CAAC;AAClB,MAAI,CAAC,IAAK,QAAO,QAAQ,QAAQ;AACjC,QAAM,OAAO,SAAS,IAAI,UAAU;AACpC,QAAM,oBAAoB,SAAS,IAAI,wBAAwB;AAC/D,SAAO,KAAK,QAAQ,QAAQ,KAAK;AAAA,IAC/B;AAAA,EACF,CAAC,EAAE;AAAA,IAAK,WAAW,SAAO;AACxB,wBAAkB,YAAY,GAAG;AACjC,aAAO,GAAG,IAAI;AAAA,IAChB,CAAC;AAAA;AAAA,IAED,IAAI,SAAO,mBAAmB,SAAS,kBAAkB,aAAa,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,EAAC,EAAE,UAAU;AAC3G;AACA,SAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAChD,UAAQ,OAAO,eAAe;AAAA,IAC5B,KAAK;AACH,aAAO,UAAU,OAAO,MAAM;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO,OAAO,cAAc,OAAO,MAAM;AAAA,EAC7C;AACF;AACA,IAAM,oBAAN,cAAgC,kBAAgB;AAAA,EAC9C,IAAI,aAAa;AACf,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,YAAY,qBAAqB;AAC/B,UAAM;AACN,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,OAAO,gBAAgB;AACrB,UAAM,WAAW,SAAS,OAAO,iCAC3B,kBAAkB;AAAA,MACpB,QAAQ;AAAA,IACV,IAH+B;AAAA,MAI/B,WAAW,KAAK,oBAAoB;AAAA,IACtC,EAAC;AACD,UAAM,WAAW,SAAS,IAAI,QAAQ;AACtC,UAAM,UAAU,SAAS,kBAAkB,KAAK,UAAU;AAC1D,WAAO,QAAQ,OAAO,QAAQ;AAAA,EAChC;AACF;AACA,SAAS,gBAAgB,aAAa,aAAa,QAAQ,cAAY,UAAU;AAC/E,SAAO,YAAY,aAAa,WAAW,EAAE,KAAK,OAAO,OAAO,GAAG,IAAI,KAAK,CAAC;AAC/E;AAGA,SAAS,aAAa,MAAM,UAAU;AACpC,QAAM,UAAU,IAAI,gBAAgB,IAAI;AACxC,QAAM,OAAO,SAAS,cAAc,GAAG;AACvC,OAAK,OAAO;AACZ,OAAK,WAAW;AAChB,WAAS,KAAK,YAAY,IAAI;AAC9B,OAAK,cAAc,IAAI,WAAW,SAAS;AAAA,IACzC,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM;AAAA,EACR,CAAC,CAAC;AACF,WAAS,KAAK,YAAY,IAAI;AAChC;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,OAAO,KAAK;AAC9B;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,UAAU,CAAC;AACjB,aAAW,UAAU,MAAO,KAAI,CAAC,SAAS,MAAM,EAAG,SAAQ,KAAK;AAAA,IAC9D,KAAK;AAAA,IACL,OAAO,MAAM,MAAM;AAAA,EACrB,CAAC;AACD,SAAO;AACT;AACA,SAAS,KAAK,GAAG;AACf,SAAO,KAAK,IAAI,KAAK,OAAO,IAAI,MAAM,IAAI,GAAG,SAAS,EAAE,KAAK,4BAAgC,OAAO,QAAQ,UAAU,IAAI;AAC5H;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,SAAS;AACb,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAW,MAAM,WAAW,CAAC;AAC7B,cAAU,UAAU,KAAK,SAAS;AAClC,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,UAAU,SAAS,GAAG;AAC9C,MAAI,UAAU;AACZ,aAAS,0BAA0B,QAAQ;AAAA,EAC7C;AACA,WAAS,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,GAAG;AAC1C,QAAM,SAAS;AACf,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,QAAM,WAAW;AACjB,QAAM,MAAM,SAAS,SAAS,UAAU;AACxC,QAAM,YAAY,YAAU,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM,CAAC;AAC5E,QAAM,WAAW,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AACD,WAAS,CAAC,IAAI,UAAU,MAAM;AAC9B,WAAS,CAAC,IAAI,UAAU,MAAM;AAC9B,WAAS,CAAC,IAAI,UAAU,OAAO;AAC/B,WAAS,CAAC,IAAI,UAAU,QAAQ;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,aAAS,CAAC,IAAI,UAAU,GAAG;AAAA,EAC7B;AACA,SAAO,SAAS,KAAK,MAAM,MAAM,KAAK,OAAO,CAAC,EAAE,KAAK,EAAE;AACzD;AACA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,cAAc,SAAS,IAAI,kBAAkB;AACnD,QAAM,gBAAgB,YAAY,YAAY,mBAAmB;AACjE,SAAO,OAAO,cAAc,sCAAsC,CAAC,KAAK;AAC1E;AACA,SAAS,YAAY,KAAK;AACxB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,IAAI,IAAI,KAAK,OAAO,SAAS,MAAM;AACvC,SAAO;AACT;AACA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,UAAU,GAAG;AACX,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,YAAY,GAAG;AACb,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,UAAU,GAAG;AACX,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,YAAY,GAAG;AACb,WAAO,mBAAmB,CAAC;AAAA,EAC7B;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,SAAS;AACX,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,iBAAa,MAAM;AAAA,EACrB;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,aAAa,QAAQ,GAAG;AAAA,EACjC;AAAA,EACA,IAAIC,QAAO;AACT,WAAO,aAAa,IAAIA,MAAK;AAAA,EAC/B;AAAA,EACA,WAAW,KAAK;AACd,iBAAa,WAAW,GAAG;AAAA,EAC7B;AAAA,EACA,QAAQ,KAAK,OAAO;AAClB,iBAAa,QAAQ,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,aAAa,qBAAqB;AAC5C,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAC3B,SAAK,QAAQ,IAAI,cAAc,CAAC,CAAC;AACjC,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,qBAAqB,MAAM;AAC9B,WAAK,oBAAoB,QAAQ,cAAc,KAAK,UAAU,KAAK,MAAM,KAAK,CAAC;AAAA,IACjF;AACA,SAAK,KAAK;AACV,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,UAAM,UAAU,KAAK,oBAAoB,QAAQ,YAAY;AAC7D,QAAI,SAAS;AACX,WAAK,MAAM,IAAI,KAAK,MAAM,OAAO,CAAC;AAAA,IACpC;AACA,SAAK,MAAM,YAAY,WAAS,KAAK,EAAE,UAAU,KAAK,kBAAkB;AAAA,EAC1E;AAAA,EACA,qBAAqB;AACnB,UAAM,cAAc,KAAK,YAAY;AACrC,SAAK,YAAY,SAAS,yCAAyC,EAAE,KAAK,OAAO,iBAAe,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,UAAQ;AACzI,UAAI,KAAK,SAAS,GAAG,GAAG;AACtB,eAAO,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,MAC1B;AACA,WAAK,YAAY,IAAI;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,MAAM,YAAY,WAAS,MAAM,QAAQ;AAAA,EACvD;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,MAAM,YAAY,WAAS,MAAM,MAAM;AAAA,EACrD;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,eAAe;AACb,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,QAAQ;AAAA,EACtD;AAAA,EACA,YAAY;AACV,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,aAAa;AACX,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,MAAM;AAAA,EACpD;AAAA,EACA,UAAU,QAAQ;AAChB,QAAI,kBAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,EAAG;AAC9C,SAAK,MAAM,IAAI,iCACV,KAAK,MAAM,QADD;AAAA,MAEb;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,YAAY,UAAU;AACpB,UAAM,kBAAkB,KAAK,MAAM,MAAM;AACzC,QAAI,aAAa,iBAAiB;AAChC,WAAK,MAAM,MAAM;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,mBAAmB,KAAK,SAAS,gBAAgB,aAAa,MAAM;AAC1E,QAAI,aAAa,kBAAkB;AACjC,WAAK,SAAS,gBAAgB,aAAa,QAAQ,QAAQ;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAS,kBAAkB,GAAM,SAAS,sBAAsB,CAAC;AAAA,IAC5H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAC5E,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,iBAAiB,CAAC,IAAI,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC7D,QAAQ;AAAA,MACR,KAAK,wCAAwC,EAAE;AAAA,IACjD,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,mBAAmB,CAAC,MAAM,WAAW,KAAK,YAAY,QAAQ;AAAA,MACjE,QAAQ;AAAA,MACR,KAAK,0CAA0C,IAAI;AAAA,IACrD,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAS,WAAW,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,aAAa,IAAI,eAAe,YAAY;AAClD,IAAM,sBAAsB,IAAI,iBAAiB,MAAM,KAAK;AAG5D,IAAM,qBAAN,MAAM,4BAA2B,WAAW;AAAA,EAC1C,QAAQ,OAAO,KAAK,UAAU,CAAC,GAAG;AAChC,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,uBAAuB,OAAO;AACnC,aAAO,MAAM,QAAQ,OAAO,OAAO,IAAI,OAAO;AAAA,IAChD;AACA,SAAK,uBAAuB,KAAK;AACjC,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AAAA,EACA,uBAAuB,kBAAkB;AACvC,qBAAiB,YAAY,IAAI,YAAY;AAC7C,qBAAiB,QAAQ,IAAI,qBAAqB,IAAI;AAAA,EACxD;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,2BAA2B,mBAAmB;AAC5D,gBAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,MACtK;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,SAAS,MAAM,cAAc,aAAa,mBAAmB;AACvE,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,gBAAgB,SAAS;AACvB,WAAO,KAAK,YAAY,UAAU,OAAO;AAAA,EAC3C;AAAA,EACA,YAAY,KAAK;AACf,SAAK,kBAAkB,YAAY,GAAG;AACtC,WAAO,WAAW,MAAM,GAAG;AAAA,EAC7B;AAAA,EACA,QAAQ,SAAS,QAAQ,KAAK;AAC5B,aAAS,UAAU,CAAC;AACpB,UAAM,OAAO,KAAK,gBAAgB,OAAO,OAAO;AAChD,UAII,cAHF;AAAA;AAAA,MACA;AAAA,IA/8BN,IAi9BQ,IADC,oBACD,IADC;AAAA,MAFH;AAAA,MACA;AAAA;AAGF,UAAM;AAAA,MACJ,UAAU;AAAA,MACV;AAAA,IACF,IAAI;AACJ,UAAM,MAAM,KAAK,uBAAuB,MAAM,QAAQ,GAAG;AACzD,UAAM,aAAa,KAAK,cAAc,OAAO,gBAAgB;AAC7D,WAAO,WAAW,QAAQ,QAAQ,KAAK;AAAA,MACrC;AAAA,OACI,UAAU;AAAA,MACZ,QAAQ,KAAK,UAAU,QAAQ,OAAO,gBAAgB;AAAA,IACxD,IACG,QACJ,EAAE,KAAK,WAAW,SAAO,kBAAkB,WAAW,MAAM,GAAG,IAAI,KAAK,YAAY,GAAG,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,cAAc,YAAY;AACxB,WAAO,aAAa,KAAK,eAAe,KAAK;AAAA,EAC/C;AAAA,EACA,UAAU,QAAQ,SAAS;AACzB,UAAM,iBAAiB,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC1E,UAAI,yBAAyB,KAAK,EAAG,QAAO;AAC5C,UAAI,UAAU,QAAQ,CAAC,KAAK,QAAQ,sBAAuB,QAAO;AAClE,UAAI,GAAG,IAAI;AACX,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,WAAO,UAAU,IAAI,WAAW;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,IACd,CAAC,IAAI,IAAI,WAAW;AAAA,MAClB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,KAAK;AAC1B,WAAO,IAAI,QAAQ,gBAAgB,IAAI;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,SAAS,YAAY,GAAM,SAAY,UAAU,GAAM,SAAS,kBAAkB,GAAM,SAAS,kBAAkB,GAAM,SAAS,wBAAwB,CAAC;AAAA,IAC9M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,aAAa,cAAc,eAAe,oBAAoB,WAAW;AACnF,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,mBAAmB,YAAU;AAChC,WAAK,aAAa,UAAU;AAAA,QAC1B,IAAI,OAAO;AAAA,QACX,MAAM,OAAO;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AACD,aAAO,KAAK,mBAAmB,gBAAgB,EAAE,KAAK,IAAI,OAAK,MAAM,CAAC;AAAA,IACxE;AAAA,EACF;AAAA,EACA,gBAAgB,YAAY;AAC1B,WAAO,KAAK,cAAc,iBAAiB,UAAU,EAAE,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAAA,EAC9F;AAAA,EACA,cAAc,UAAU;AACtB,WAAO,KAAK,cAAc,eAAe,QAAQ,EAAE,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAAA,EAC1F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAS,WAAW,GAAM,SAAS,mBAAmB,GAAM,SAAS,gBAAgB,GAAM,SAAS,kBAAkB,GAAM,SAAS,UAAU,CAAC;AAAA,IAC3M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAC9E,IAAM,qBAAqB;AAC3B,SAAS,sBAAsB,YAAY;AACzC,MAAI,WAAW,OAAO,WAAW,SAAS,CAAC,MAAM,IAAK,eAAc;AACpE,QAAM,cAAc,kBAAkB,UAAU;AAChD,QAAM,QAAQ,mBAAmB,QAAQ,SAAS,EAAE;AACpD,SAAO,YAAY,OAAO,SAAS,IAAI,EAAE,KAAK,IAAI,CAAC;AACrD;AACA,SAAS,6BAA6B,WAAW;AAC/C,QAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,SAAO,UAAU,IAAI,SAAS;AAChC;AACA,SAAe,mBAAmB,UAAU;AAAA;AAC1C,UAAM,qBAAqB,SAAS,IAAI,kBAAkB;AAC1D,UAAM,sBAAsB,SAAS,IAAI,mBAAmB;AAC5D,UAAM,wBAAwB,SAAS,IAAI,0BAA0B,IAAI;AACzE,UAAM,UAAU,mBAAmB,eAAe,GAAG,aAAa,WAAW;AAC7E,UAAM,cAAc,sBAAsB,OAAO;AACjD,UAAM,gBAAgB,MAAM;AAC1B,0BAAoB,qBAAqB;AAAA,IAC3C;AACA,UAAM,kBAAkB,YAAU;AAChC,0BAAoB,eAAe;AAAA,QACjC,IAAI,OAAO;AAAA,QACX,MAAM,OAAO;AAAA,QACb,aAAa;AAAA,MACf;AAAA,IACF;AACA,UAAM,iCAAiC,YAAU;AAC/C,oBAAc;AACd,sBAAgB,MAAM;AAAA,IACxB;AACA,QAAI,aAAa;AAMf,yCAAmC,UAAU,WAAW;AACxD,YAAM,UAAU,oBAAoB,gBAAgB,WAAW;AAC/D,UAAI;AACF,cAAM,SAAS,MAAM,eAAe,OAAO;AAC3C,uCAA+B,MAAM;AACrC,eAAO,QAAQ,QAAQ,MAAM;AAAA,MAC/B,SAAS,WAAW;AAClB,YAAI,qBAAqB,qBAAqB,UAAU,WAAW,OAAO,uBAAuB;AAC/F,gCAAsB,SAAS;AAAA,QACjC;AACA,eAAO,QAAQ,OAAO;AAAA,MACxB;AAAA,IACF;AAIA,uCAAmC,UAAU,IAAI,qBAAqB,GAAG;AACzE,UAAM,0BAA0B,6BAA6B,oBAAoB,SAAS;AAC1F,QAAI,yBAAyB;AAC3B,YAAM,cAAc,oBAAoB,cAAc,uBAAuB;AAC7E,aAAO,eAAe,WAAW;AAAA,IACnC;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA;AACA,SAAS,mCAAmC,UAAU,aAAa,cAAc,oBAAoB;AACnG,QAAM,qBAAqB,SAAS,IAAI,kBAAkB;AAC1D,QAAM,cAAc,gBAAM,mBAAmB,eAAe,CAAC;AAC7D,MAAI,YAAY,YAAY,SAAS;AACnC,gBAAY,YAAY,UAAU,YAAY,YAAY,QAAQ,QAAQ,aAAa,WAAW;AAAA,EACpG;AACA,MAAI,YAAY,aAAa,aAAa;AACxC,gBAAY,YAAY,cAAc,YAAY,YAAY,YAAY,QAAQ,aAAa,WAAW;AAAA,EAC5G;AACA,MAAI,CAAC,YAAY,aAAa;AAC5B,gBAAY,cAAc,CAAC;AAAA,EAC7B;AACA,cAAY,YAAY,UAAU,YAAY,YAAY,UAAU,IAAI,QAAQ,aAAa,WAAW;AACxG,SAAO,KAAK,YAAY,IAAI,EAAE,QAAQ,SAAO;AAC3C,WAAO,KAAK,YAAY,KAAK,GAAG,CAAC,EAAE,QAAQ,SAAO;AAChD,kBAAY,KAAK,GAAG,EAAE,GAAG,KAAK,YAAY,KAAK,GAAG,EAAE,GAAG,KAAK,IAAI,QAAQ,aAAa,WAAW;AAAA,IAClG,CAAC;AAAA,EACH,CAAC;AACD,SAAO,mBAAmB,SAAS,WAAW;AAChD;AACA,IAAM,oCAAoC,IAAI,eAAe,mCAAmC;AAChG,SAAS,eAAe,UAAU;AAChC,QAAM,KAAK,MAAY;AACrB,UAAM,qBAAqB,SAAS,IAAI,kBAAkB;AAC1D,UAAM,cAAc,SAAS,IAAI,kBAAkB;AACnD,UAAM,UAAU,SAAS,IAAI,YAAY;AACzC,uBAAmB,SAAS,QAAQ,WAAW;AAC/C,UAAM,aAAa,UAAU,QAAQ,WAAW;AAChD,UAAM,mBAAmB,QAAQ;AACjC,UAAM,cAAc,SAAS,IAAI,aAAa,QAAW;AAAA,MACvD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,2BAA2B,SAAS,IAAI,mCAAmC,MAAM;AAAA,MACrF,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,CAAC,QAAQ,uBAAuB,aAAa;AAC/C,YAAM,YAAY,KAAK;AAAA,IACzB;AACA,QAAI,QAAQ,wBAAyB;AACrC,UAAM,UAAU,YAAY,gBAAgB,EAAE,KAAK,IAAI,MAAM,yBAAyB,QAAQ,CAAC,GAAG,IAAI,MAAM;AAC1G,YAAM,gBAAgB,YAAY,OAAO,eAAe;AACxD,eAAS,IAAI,mBAAmB,EAAE,UAAU,aAAa;AAAA,IAC3D,CAAC,GAAG,WAAW,WAAS;AACtB,YAAM,uBAAuB,SAAS,IAAI,yBAAyB,IAAI;AACvE,UAAI,wBAAwB,qBAAqB,QAAQ;AACvD,6BAAqB,QAAQ,UAAQ,KAAK,KAAK,CAAC;AAAA,MAClD;AACA,aAAO,WAAW,KAAK;AAAA,IACzB,CAAC,CAAC;AACF,UAAM,cAAc,OAAO;AAAA,EAC7B;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,UAAU;AACnC,QAAM,KAAK,MAAM;AACf,UAAM,eAAe,SAAS,IAAI,mBAAmB;AACrD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,SAAS,IAAI,YAAY;AAC7B,UAAM,OAAO,aAAa,YAAY,KAAK;AAC3C,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,uBAAiB,IAAI,EAAE,KAAK,YAAU;AACpC,YAAI,QAAQ,QAAS,oBAAmB,OAAO,OAAO;AACtD,eAAO,QAAQ,UAAU;AAAA,MAC3B,GAAG,MAAM;AAAA,IACX,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,aAAa,WAAW;AAClC,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,eAAe,SAAS;AACtB,QAAI,KAAK,UAAW,SAAQ,aAAa,aAAa,KAAK,SAAS;AACpE,QAAI,KAAK,aAAa;AACpB,cAAQ,aAAa,eAAe,KAAK,WAAW;AAAA,IACtD;AAAA,EACF;AACF;AACA,IAAM,wBAAN,cAAoC,oBAAoB;AAAA,EACtD,iBAAiB;AAAA,EAAC;AACpB;AACA,IAAM,wBAAwB;AAAA,EAC5B,UAAU,WAAW;AACnB,WAAO,IAAI,oBAAoB,aAAa,SAAS;AAAA,EACvD;AAAA,EACA,eAAe,WAAW;AACxB,WAAO,IAAI,oBAAoB,mBAAmB,SAAS;AAAA,EAC7D;AAAA,EACA,OAAO;AACL,WAAO,IAAI,sBAAsB,IAAI;AAAA,EACvC;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,SAAS,SAAS,MAAM,WAAW,aAAa;AAC1D,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,OAAO,sBAAsB,KAAK,UAAU,OAAO;AAAA,EAC1D;AACF;AACA,IAAM,eAAe;AAAA,EACnB,aAAa,SAAS;AACpB,WAAO,IAAI,YAAY,SAAS,UAAU;AAAA,EAC5C;AAAA,EACA,eAAe;AACb,WAAO,IAAI,YAAY,SAAS,MAAM,WAAW;AAAA,EACnD;AAAA,EACA,eAAe;AACb,WAAO,IAAI,YAAY,SAAS,MAAM,WAAW;AAAA,EACnD;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,IAAI,YAAY,SAAS,aAAa;AAAA,EAC/C;AAAA,EACA,gBAAgB;AACd,WAAO,IAAI,YAAY,SAAS,MAAM,YAAY;AAAA,EACpD;AACF;AACA,SAAS,aAAa,SAAS,cAAc,aAAa,aAAa,GAAG,sBAAsB,sBAAsB,UAAU,GAAG;AACjI,sBAAoB,eAAe,OAAO;AAC1C,cAAY,cAAc,OAAO;AACjC,SAAO,IAAI,WAAW,cAAY;AAChC,YAAQ,SAAS,WAAS;AACxB,qBAAe,OAAO;AACtB,eAAS,KAAK,KAAK;AACnB,eAAS,SAAS;AAAA,IACpB;AACA,UAAM,cAAc,mBAAmB,UAAU,OAAO;AACxD,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,YAAQ,YAAY;AACpB,YAAQ,YAAY;AACpB,WAAO,MAAM;AACX,qBAAe,OAAO;AACtB,eAAS,SAAS;AAAA,IACpB;AAAA,EACF,CAAC;AACH;AACA,SAAS,mBAAmB,UAAU,SAAS;AAC7C,SAAO,SAAU,OAAO;AACtB,mBAAe,OAAO;AACtB,YAAQ,YAAY,YAAY,OAAO;AACvC,aAAS,MAAM,KAAK;AAAA,EACtB;AACF;AACA,SAAS,eAAe,SAAS;AAC/B,UAAQ,SAAS;AACjB,UAAQ,UAAU;AAClB,UAAQ,UAAU;AAClB,UAAQ,YAAY;AACpB,UAAQ,YAAY;AACpB,UAAQ,YAAY;AACtB;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,KAAK,UAAU,WAAW;AACxB,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,IAAI;AACN,SAAK,MAAM,KAAK,EAAE;AAClB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,MAAM;AACJ,QAAI,KAAK,UAAW;AACpB,SAAK;AACL,SAAK,YAAY;AACjB,UAAM,KAAK,KAAK,MAAM,MAAM;AAC5B,QAAI,CAAC,IAAI;AACP,WAAK,YAAY;AACjB;AAAA,IACF;AACA,OAAG;AACH,QAAI,KAAK,QAAQ,KAAK,WAAW;AAC/B,iBAAW,MAAM;AACf,aAAK,YAAY;AACjB,aAAK,IAAI;AACT,aAAK,QAAQ;AAAA,MACf,GAAG,KAAK,QAAQ;AAAA,IAClB,OAAO;AACL,WAAK,YAAY;AACjB,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,UAAU,eAAe,MAAM;AACtC,QAAM,OAAO,cAAc,KAAK,WAAS,MAAM,SAAS,IAAI;AAC5D,SAAO,QAAQ,SAAS,MAAM,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,KAAK,GAAG;AAC7G;AACA,SAAS,aAAa,QAAQ,MAAM,OAAO,KAAK;AAC9C,QAAM,aAAa;AAAA,IACjB,UAAU,CAAC;AAAA,EACb;AACA,QAAM,eAAe,OAAO,SAAS,GAAG,EAAE,KAAK,SAAS,cAAc;AACtE,SAAO,OAAO,gBAAgB,YAAY,SAAS,IAAI,CAAC;AAAA,IACtD;AAAA,EACF,MAAM,IAAI,EAAE,KAAK,GAAG;AACtB;AACA,SAAS,YAAY,QAAQ,QAAQ;AACnC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,OAAO;AACX,QAAM,gBAAgB,WAAS;AAC7B,WAAO,mBAAmB,mBAAmB;AAAA,EAC/C;AACA,gBAAc,MAAM,KAAK;AACzB,SAAO,YAAY;AACnB,SAAO,IAAI,MAAY;AACrB,UAAM,OAAO,cAAc,OAAO,GAAG,EAAE,MAAM,IAAI;AACjD,kBAAc,gBAAgB;AAAA,EAChC,EAAC;AACH;AAGA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,OAAO;AACjB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS;AACd,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO,OAAO,OAAO;AACnB,WAAO,IAAI,cAAa,KAAK;AAAA,EAC/B;AACF;AACA,SAAS,mBAAmB,MAAM,aAAa,mBAAmB,aAAa;AAC7E,QAAMC,OAAM,kBAAkB,MAAM,aAAa,WAAW;AAC5D,QAAM,OAAO,CAAC;AACd,OAAK,QAAQ,SAAO;AAClB,UAAM,KAAK,YAAY,GAAG;AAC1B,UAAM,WAAW,kBAAkB,GAAG;AACtC,UAAM,OAAOA,KAAI,IAAI,EAAE;AACvB,QAAI,CAAC,KAAM;AACX,QAAI,UAAU;AACZ,YAAM,SAASA,KAAI,IAAI,QAAQ;AAC/B,UAAI,CAAC,OAAQ;AACb,aAAO,SAAS,KAAK,IAAI;AACzB,aAAO,SAAS;AAChB,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,KAAK,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,kBAAkB,MAAM,aAAa,aAAa;AACzD,QAAMA,OAAM,oBAAI,IAAI;AACpB,OAAK,QAAQ,SAAOA,KAAI,IAAI,YAAY,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC;AAC/D,SAAOA;AACT;AACA,SAAS,4BAA4B,KAAK,UAAU;AAClD,SAAO,YAAU;AACf,UAAM,QAAQ,IAAI,OAAO,OAAO,SAAS,MAAM,GAAG;AAClD,WAAO,SAAS,aAAa,OAAO,UAAU,CAAC,GAAG;AAChD,iBAAW,QAAQ,OAAO;AACxB,YAAI,MAAM,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,EAAG,SAAQ,KAAK,IAAI;AACtD,YAAI,KAAK,SAAS,OAAQ,cAAa,KAAK,UAAU,OAAO;AAAA,MAC/D;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,eAAe,MAAM,gBAAgB;AAC5C,MAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,UAAQ,QAAQ,KAAK,KAAK,CAAC,EAAG,QAAO;AACtE,QAAM,WAAW,oBAAI,IAAI;AACzB,aAAW,QAAQ,MAAM;AACvB,UAAM,QAAQ,MAAM,SAAS;AAC7B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,kBAAkB,KAAK,EAAE;AAAA,IAC3C;AACA,UAAM,QAAQ,SAAS,IAAI,KAAK,KAAK,CAAC;AACtC,UAAM,KAAK,IAAI;AACf,aAAS,IAAI,OAAO,KAAK;AAAA,EAC3B;AACA,SAAO;AACT;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,WAAW,oBAAI,IAAI;AAAA,EAC1B;AAAA,EACA,cAAc,iBAAiB;AAC7B,UAAM,OAAO,aAAa,gBAAgB,OAAO;AACjD,QAAI,KAAK,SAAS,IAAI,IAAI,EAAG;AAC7B,UAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAK,SAAS,IAAI,IAAI;AACtB,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,QAAQ,aAAa;AACvB,YAAM,OAAO,aAAa,QAAQ,WAAW;AAC7C,WAAK,SAAS,OAAO,IAAI;AACzB,cAAQ,YAAY,YAAY,OAAO;AAAA,IACzC;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,UAAM,OAAO,aAAa,OAAO;AACjC,WAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAe,IAAI,eAAe,cAAc;AACtD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,UAAU;AACpB,SAAK,QAAQ,IAAI,cAAc;AAAA,MAC7B,UAAU,CAAC;AAAA,MACX,kBAAkB,CAAC;AAAA,IACrB,CAAC;AACD,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,QAAQ,SAAS,IAAI,cAAc,GAAG;AAAA,EAC7C;AAAA,EACA,aAAa;AACX,WAAO,CAAC,CAAC,KAAK,YAAY,KAAK,MAAM,MAAM,QAAQ,EAAE;AAAA,EACvD;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,WAAW,CAAC;AAAA,MAC5B;AAAA,IACF,MAAM,QAAQ,EAAE,KAAK,IAAI,cAAY,CAAC,CAAC,KAAK,YAAY,QAAQ,EAAE,MAAM,GAAG,UAAU,eAAa,YAAY,KAAK,UAAU,IAAI,GAAG,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE,KAAK,MAAM,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM,KAAK,SAAS,KAAK,CAAC,CAAC;AAAA,EACzP;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF,MAAM,CAAC,CAAC,KAAK,YAAY,QAAQ,EAAE,MAAM;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,SAAK,MAAM,MAAM;AAAA,MACf,UAAU,CAAC;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,MAAM,MAAM;AAAA,MACf,UAAU,CAAC,GAAG,KAAK,MAAM,MAAM,UAAU,OAAO;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,WAAW,KAAK,MAAM,MAAM,SAAS,OAAO,OAAK,MAAM,OAAO;AACpE,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC5D,UAAM,mBAAmB,CAAC,GAAG,KAAK,MAAM,MAAM,iBAAiB,OAAO,OAAK,CAAC,SAAS,KAAK,OAAK,KAAK,cAAc,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ;AACtI,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC5D,UAAM,mBAAmB,KAAK,MAAM,MAAM,iBAAiB,OAAO,OAAK,CAAC,SAAS,KAAK,OAAK,KAAK,cAAc,GAAG,CAAC,CAAC,CAAC;AACpH,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,CAAC,UAAU;AACb,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,MAAM;AACf,WAAO,SAAS,OAAO,CAAC;AAAA,MACtB;AAAA,MACA;AAAA,IACF,MAAM,CAAC,iBAAiB,KAAK,qBAAmB,KAAK,cAAc,iBAAiB;AAAA,MAClF;AAAA,MACA,UAAU,YAAY,GAAG;AAAA,IAC3B,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,cAAc,iBAAiB,SAAS;AACtC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,aAAa,QAAQ,YAAY,WAAW,QAAQ;AAAA,EAC7D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAY,QAAQ,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,QAAQ,IAAI,cAAc;AAAA,MAC7B,WAAW,oBAAI,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,WAAO,CAAC,CAAC,KAAK,MAAM,MAAM,UAAU;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,WAAW,CAAC;AAAA,MAC5B;AAAA,IACF,MAAM,CAAC,CAAC,UAAU,IAAI;AAAA,EACxB;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF,MAAM,CAAC,CAAC,WAAW,IAAI;AAAA,EACzB;AAAA,EACA,eAAe;AACb,SAAK,MAAM,MAAM;AAAA,MACf,WAAW,oBAAI,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,UAAU;AACpB,UAAM,YAAY,KAAK,MAAM,MAAM;AACnC,cAAU,IAAI,QAAQ;AACtB,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU;AACvB,UAAM,YAAY,KAAK,MAAM,MAAM;AACnC,cAAU,OAAO,QAAQ;AACzB,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,qBAAqB;AAC/B,SAAK,sBAAsB;AAC3B,SAAK,SAAS,oBAAI,IAAI;AAAA,EACxB;AAAA,EACA,KAAK,UAAU,YAAY,YAAY;AACrC,QAAI,KAAK,OAAO,IAAI,SAAS,IAAI,EAAG,QAAO,GAAG,IAAI,YAAY,MAAM,CAAC;AACrE,SAAK,oBAAoB,YAAY,SAAS,IAAI;AAClD,UAAM,gBAAgB,aAAa,KAAK,MAAM,UAAU,CAAC,IAAI,KAAK;AAClE,UAAM,SAAS,aAAa,KAAK,KAAK,UAAU,CAAC,IAAI,KAAK;AAC1D,WAAO,SAAS,aAAa,EAAE,KAAK,UAAU,YAAU,OAAO,OAAO,KAAK,eAAe,MAAM,GAAG,WAAW,MAAM,IAAI,YAAY,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM;AACzJ,WAAK,OAAO,IAAI,SAAS,MAAM,SAAS,OAAO;AAC/C,WAAK,oBAAoB,eAAe,SAAS,IAAI;AAAA,IACvD,CAAC,GAAG,MAAM,GAAG,GAAG,YAAY;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO,MAAM;AACX,UAAM,UAAU,KAAK,OAAO,IAAI,IAAI;AACpC,QAAI,CAAC,QAAS,QAAO;AACrB,YAAQ,YAAY,YAAY,OAAO;AACvC,SAAK,OAAO,OAAO,IAAI;AACvB,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAS,mBAAmB,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAC9E,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,kBAAkB;AACvB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,QAAI,UAAU,KAAK,MAAO;AAC1B,SAAK,QAAQ;AACb,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,QAAI,UAAU,KAAK,YAAa;AAChC,SAAK,cAAc;AACnB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa;AAClB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,aAAa,EAAE,KAAK,KAAK,OAAO,YAAY;AAAA,MAC9D,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU,IAAI,cAAc,CAAC;AAClC,SAAK,cAAc,IAAI,gBAAgB,KAAK;AAC5C,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,MAAM,MAAM;AACf,WAAK,uBAAuB;AAC5B,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,sBAAsB,MAAM;AAC/B,WAAK,KAAK;AAAA,IACZ;AACA,UAAMC,SAAQ,SAAS,IAAI,0BAA0B,GAAG;AACxD,SAAK,QAAQA,SAAQ,aAAaA,MAAK,IAAI,IAAI;AAC/C,SAAK,IAAI;AAAA,EACX;AAAA,EACA,YAAY,uBAAuB;AACjC,WAAO,KAAK,OAAO,KAAK,IAAI,MAAM,KAAK,YAAY,KAAK,IAAI,CAAC,GAAG,UAAU,WAAS,sBAAsB,KAAK,EAAE,KAAK,WAAW,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,OAAO,GAAG,IAAI,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC,GAAG,YAAY;AAAA,MACvN,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,yBAAyB;AACvB,UAAM,UAAU,OAAO,OAAO,KAAK,aAAa,KAAK,eAAe,EAAE,QAAQ,CAAC;AAC/E,UAAM,YAAY,KAAK,QAAQ,KAAK;AACpC,QAAI,cAAc,KAAK,aAAa;AAClC,WAAK,aAAa;AAClB;AAAA,IACF;AACA,QAAI,KAAK,SAAS,WAAW,KAAK,OAAO,GAAG;AAC1C,WAAK,aAAa,YAAY,KAAK;AACnC,WAAK,OAAO,KAAK,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAAQ,KAAK;AAAA,MAChB,QAAQ,KAAK,WAAW;AAAA,MACxB,gBAAgB,KAAK;AAAA,MACrB,WAAW,KAAK,QAAQ,KAAK;AAAA,MAC7B,SAAS,KAAK,aAAa,GAAG,KAAK,QAAQ,IAAI,KAAK,UAAU,KAAK;AAAA,IACrE,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,SAAY,QAAQ,CAAC;AAAA,IACxE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,kBAAkB,KAAK;AACrB,WAAO,KAAK,UAAU,EAAE,KAAK,IAAI,qBAAmB,KAAK,gBAAgB,KAAK,eAAe,CAAC,CAAC;AAAA,EACjG;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,YAAY;AAClC,WAAO,KAAK,gBAAgB,KAAK,QAAQ;AAAA,EAC3C;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,WAAW,KAAK,YAAY;AAClC,WAAO,MAAM,OAAO,UAAQ,CAAC,KAAK,kBAAkB,KAAK,gBAAgB,KAAK,gBAAgB,QAAQ,CAAC;AAAA,EACzG;AAAA,EACA,qBAAqB,OAAO;AAC1B,WAAO,KAAK,UAAU,EAAE,KAAK,IAAI,cAAY,MAAM,OAAO,UAAQ,CAAC,KAAK,kBAAkB,KAAK,gBAAgB,KAAK,gBAAgB,QAAQ,CAAC,CAAC,CAAC;AAAA,EACjJ;AAAA,EACA,gBAAgB,KAAK,iBAAiB;AACpC,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM,WAAW;AACjB,UAAM,YAAY;AAElB,QAAI,SAAS,KAAK,GAAG,GAAG;AACtB,YAAM,OAAO,IAAI,MAAM,IAAI,EAAE,OAAO,OAAO;AAC3C,UAAI,KAAK,SAAS,EAAG,QAAO;AAC5B,aAAO,KAAK,KAAK,OAAK,KAAK,UAAU,EAAE,KAAK,GAAG,eAAe,CAAC;AAAA,IACjE,WAAW,UAAU,KAAK,GAAG,GAAG;AAC9B,YAAM,OAAO,IAAI,MAAM,IAAI,EAAE,OAAO,OAAO;AAC3C,UAAI,KAAK,SAAS,EAAG,QAAO;AAC5B,aAAO,KAAK,MAAM,OAAK,KAAK,UAAU,EAAE,KAAK,GAAG,eAAe,CAAC;AAAA,IAClE;AACA,WAAO,KAAK,UAAU,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,YAAY;AACV,WAAO,KAAK,YAAY,QAAQ,EAAE,KAAK,IAAI,KAAK,aAAa,CAAC;AAAA,EAChE;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,cAAc,KAAK,YAAY,OAAO,CAAC;AAAA,EACrD;AAAA,EACA,cAAc,0BAA0B;AACtC,WAAO,0BAA0B,MAAM,mBAAmB,CAAC;AAAA,EAC7D;AAAA,EACA,UAAU,KAAK,iBAAiB;AAC9B,WAAO,gBAAgB,GAAG,KAAK;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAS,kBAAkB,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,IAAI,yBAAyB;AAC3B,WAAO,KAAK,MAAM,WAAW,WAAS,KAAK;AAAA,EAC7C;AAAA,EACA,IAAI,wBAAwB;AAC1B,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,MAAM,YAAY,WAAS,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY,QAAQ,QAAQ;AAC1B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQ,IAAI,cAAc,CAAC,CAAC;AAAA,EACnC;AAAA,EACA,IAAI,sBAAsB,QAAQ;AAChC,UAAM,wBAAwB,CAAC,GAAG,KAAK,MAAM,KAAK;AAClD,UAAMF,SAAQ,sBAAsB,UAAU,eAAa,UAAU,QAAQ,qBAAqB,GAAG;AACrG,QAAIA,SAAQ,IAAI;AACd,4BAAsBA,MAAK,IAAI;AAAA,IACjC,OAAO;AACL,4BAAsB,KAAK,oBAAoB;AAAA,IACjD;AACA,SAAK,MAAM,IAAI,qBAAqB;AACpC,QAAI,OAAQ,aAAY,KAAK,QAAQ,KAAK,MAAM;AAAA,EAClD;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,KAAK,sBAAsB,KAAK,eAAa,UAAU,QAAQ,uBAAuB;AAAA,EAC/F;AAAA,EACA,KAAK,yBAAyB;AAC5B,WAAO,KAAK,uBAAuB,KAAK,IAAI,gBAAc,WAAW,KAAK,eAAa,UAAU,QAAQ,uBAAuB,CAAC,CAAC;AAAA,EACpI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,SAAY,MAAM,GAAM,SAAc,MAAM,CAAC;AAAA,IACjH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,8BAA6B;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,sBAAsB,OAAO,MAAS;AAC3C,SAAK,qBAAqB,KAAK,oBAAoB,WAAW;AAC9D,SAAK,qBAAqB,OAAO,MAAS;AAC1C,SAAK,oBAAoB,KAAK,mBAAmB,WAAW;AAC5D,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe,KAAK,OAAO,OAAO,KAAK,OAAO,OAAK,aAAa,gBAAgB,OAAO,CAAC,EAAE,IAAI,SAAS,OAAO,CAAC,CAAC;AACtH,iBAAa,UAAU,WAAS;AAC9B,WAAK,oBAAoB,IAAI,KAAK,kBAAkB,CAAC;AACrD,WAAK,mBAAmB,IAAI,MAAM,GAAG;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,aAAa,YAAY;AACvB,UAAM,qBAAqB,WAAS,WAAW,KAAK,UAAQ,iBAAiB,IAAI;AACjF,WAAO,KAAK,OAAO,OAAO,KAAK,OAAO,kBAAkB,CAAC;AAAA,EAC3D;AAAA,EACA,uBAAuB,qBAAqB;AAC1C,UAAM,yBAAyB,WAAS,oBAAoB,KAAK,SAAO,iBAAiB,gBAAgB,GAAG,CAAC;AAC7G,WAAO,KAAK,OAAO,OAAO,KAAK,OAAO,sBAAsB,CAAC;AAAA,EAC/D;AAAA,EACA,eAAe;AACb,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,yBAAyB;AACvB,UAAM,OAAO,OAAO,KAAK,eAAe;AACxC,WAAO,KAAK,oBAAoB,GAAG,IAAI;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,cAAc,UAAU;AAClC,SAAK,eAAe;AACpB,SAAK,QAAQ,IAAI,cAAc;AAAA,MAC7B,SAAS;AAAA,IACX,CAAC;AACD,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,QAAQ,SAAS,IAAI,cAAc,GAAG;AAC3C,SAAK,sCAAsC;AAAA,EAC7C;AAAA,EACA,wCAAwC;AACtC,SAAK,aAAa,uBAAuB,EAAE,KAAK,IAAI,WAAS,iBAAiB,eAAe,GAAG,UAAU,eAAa,YAAY,KAAK,UAAU,IAAI,GAAG,IAAI,IAAI,MAAM,KAAK,SAAS,CAAC,EAAE,KAAK,MAAM,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,UAAU,YAAU;AACrS,WAAK,WAAW,MAAM;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,aAAa;AACX,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,WAAW,CAAC;AAAA,MAC5B;AAAA,IACF,MAAM,OAAO;AAAA,EACf;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,MAAM,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF,MAAM,OAAO;AAAA,EACf;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,MAAM,MAAM;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAS,YAAY,GAAM,SAAY,QAAQ,CAAC;AAAA,IACzG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAsB,IAAI,eAAe,uBAAuB;AAAA,EACpE,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,6BAA6B,IAAI,eAAe,4BAA4B;AAClF,IAAM,gBAAgB,IAAI,eAAe,eAAe;AACxD,IAAM,sCAAsC,IAAI,eAAe,qCAAqC;AACpG,IAAM,uBAAuB,IAAI,eAAe,sBAAsB;AAKtE,IAAM,uCAAuC,IAAI,eAAe,sCAAsC;AACtG,IAAM,eAAe,IAAI,eAAe,cAAc;AACtD,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAChE,SAAS,qBAAqB;AAC5B,QAAM,sBAAsB,OAAO,mBAAmB;AACtD,QAAM,KAAK,CAAC,GAAG,MAAM;AACnB,UAAM,UAAU,EAAE;AAClB,UAAM,UAAU,EAAE;AAClB,QAAI,UAAU,QAAS,QAAO;AAC9B,QAAI,UAAU,QAAS,QAAO;AAC9B,QAAI,EAAE,KAAK,EAAE,GAAI,QAAO;AACxB,QAAI,EAAE,KAAK,EAAE,GAAI,QAAO;AACxB,QAAI,CAAC,OAAO,UAAU,OAAO,EAAG,QAAO;AACvC,QAAI,CAAC,OAAO,UAAU,OAAO,EAAG,QAAO;AACvC,UAAM,QAAQ,oBAAoB,QAAQ,EAAE,IAAI;AAChD,UAAM,QAAQ,oBAAoB,QAAQ,EAAE,IAAI;AAChD,QAAI,QAAQ,MAAO,QAAO;AAC1B,QAAI,QAAQ,MAAO,QAAO;AAC1B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,wBAAwB,IAAI,eAAe,iBAAiB;AAClE,IAAM,uBAAuB,IAAI,eAAe,kBAAkB;AAGlE,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,SAAS,IAAI,gBAAgB,CAAC,CAAC;AACpC,SAAK,SAAS,IAAI,gBAAgB,CAAC,CAAC;AACpC,SAAK,YAAY,IAAI,gBAAgB,CAAC,CAAC;AACvC,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,aAAa;AAAA,EACrC;AAAA,EACA,WAAW,UAAU;AACnB,WAAO,KAAK,OAAO,MAAM,OAAO,UAAQ,CAAC,SAAS,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EACtE;AAAA,EACA,kBAAkB,KAAK;AACrB,WAAO,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AAC7C,UAAI,CAAC,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC,GAAG;AACjC,eAAO;AAAA,MACT;AACA,YAAM,WAAW,oBAAI,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;AACxC,YAAM,WAAW,KAAK,kBAAkB,QAAQ;AAChD,aAAO,oBAAI,IAAI,CAAC,GAAG,KAAK,GAAG,QAAQ,CAAC;AAAA,IACtC,GAAG,GAAG;AAAA,EACR;AAAA,EACA,QAAQ,WAAW;AACjB,SAAK,OAAO,KAAK,SAAS;AAC1B,SAAK,OAAO,KAAK,KAAK,WAAW,SAAS,CAAC;AAC3C,SAAK,UAAU,KAAK,KAAK,WAAW,UAAU,OAAO,UAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AAC/E,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,mBAAmB,OAAO,UAAQ,KAAK,KAAK,EAAE,GAAG,UAAQ,KAAK,KAAK,QAAQ,GAAG,UAAQ,aAAa,OAAO,IAAI,CAAC;AAAA,EACxH;AAAA,EACA,kBAAkB,MAAM;AACtB,UAAMC,OAAM,eAAe,MAAM,KAAK,WAAW;AACjD,QAAI,CAACA,MAAK;AACR,aAAO;AAAA,IACT;AACA,WAAO,MAAM,KAAKA,MAAK,CAAC,CAAC,KAAK,KAAK,OAAO;AAAA,MACxC,OAAO;AAAA,MACP;AAAA,IACF,EAAE;AAAA,EACJ;AAAA,EACA,IAAI,OAAO;AACT,QAAI,YAAY,CAAC;AACjB,QAAI,CAAC,KAAK,yBAAyB;AACjC,kBAAY,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK;AAAA,IACrC;AACA,QAAI,KAAK,yBAAyB;AAChC,YAAMA,OAAM,oBAAI,IAAI;AACpB,YAAM,QAAQ,UAAQA,KAAI,IAAI,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC;AAClD,kBAAY,KAAK,WAAWA,IAAG;AAC/B,MAAAA,KAAI,QAAQ,YAAY,SAAS,CAAC;AAAA,IACpC;AACA,cAAU,KAAK,KAAK,IAAI;AACxB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA,EACA,KAAK,WAAW,OAAO,KAAK,MAAM;AAChC,WAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAChC,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,UAAI,UAAU,IAAI,GAAG;AACnB,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK,WAAW,KAAK,QAAQ;AAAA,IAC3C,GAAG,IAAI;AAAA,EACT;AAAA,EACA,MAAM,YAAY,OAAO;AACvB,UAAM,YAAY,KAAK,OAAO;AAC9B,UAAMD,SAAQ,UAAU,UAAU,UAAQ,KAAK,KAAK,EAAE,MAAM,UAAU;AACtE,QAAIA,SAAQ,GAAG;AACb,aAAO;AAAA,IACT;AACA,cAAUA,MAAK,IAAI,kCACd,UAAUA,MAAK,IACf;AAEL,cAAU,KAAK,KAAK,IAAI;AACxB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA,EACA,UAAU;AACR,WAAO,KAAK,IAAI,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,aAAa;AAClB,UAAM,MAAM,oBAAI,IAAI;AACpB,gBAAY,QAAQ,QAAM,IAAI,IAAI,EAAE,CAAC;AACrC,UAAM,cAAc,KAAK,kBAAkB,GAAG;AAC9C,UAAM,YAAY,KAAK,WAAW,WAAW;AAC7C,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,UAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,KAAK,OAAO,UAAQ,KAAK,MAAM,SAAO,KAAK,GAAG,MAAM,OAAO,GAAG,CAAC,CAAC;AAC1F,QAAI,CAAC,cAAc,QAAQ;AACzB,aAAO;AAAA,IACT;AACA,eAAW,QAAQ,cAAc;AAC/B,WAAK,cAAc;AAAA,QACjB,CAAC,KAAK,QAAQ,GAAG,KAAK,KAAK,EAAE;AAAA,MAC/B,CAAC;AAAA,IACH;AACA,UAAM,YAAY,KAAK,KAAK,OAAO,UAAQ,CAAC,aAAa,SAAS,IAAI,CAAC;AACvE,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA,EACA,OAAO,QAAQ,OAAO,KAAK,MAAM;AAC/B,UAAM,aAAa,OAAO,KAAK,MAAM;AACrC,WAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAChC,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,UAAI,WAAW,MAAM,SAAO,KAAK,GAAG,MAAM,OAAO,GAAG,CAAC,GAAG;AACtD,eAAO;AAAA,MACT;AACA,aAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAAA,IAC1C,GAAG,IAAI;AAAA,EACT;AAAA,EACA,qBAAqB,cAAc,MAAM;AACvC,SAAK,0BAA0B;AAAA,EACjC;AACF;AACA,IAAM,yBAAN,MAAM,gCAA+B,oBAAoB;AAAA,EACvD,YAAY,UAAU;AACpB,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,OAAO,UAAQ,KAAK,aAAa,CAAC,KAAK,UAAU,IAAI;AAC1D,SAAK,OAAO,CAAC,GAAG,MAAM;AACpB,aAAO,KAAK,YAAY,GAAG,CAAC;AAAA,IAC9B;AACA,UAAM,cAAc,KAAK,SAAS,IAAI,kBAAkB;AACxD,SAAK,eAAe,YAAY,qBAAqB,WAAS,KAAK,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AACnG,SAAK,oBAAoB,SAAS,IAAI,iBAAiB;AACvD,SAAK,cAAc,SAAS,IAAI,YAAY;AAC5C,SAAK,cAAc,SAAS,IAAI,iBAAiB;AAAA,EACnD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,EACF,GAAG;AACD,WAAO,KAAK,kBAAkB,iBAAiB,cAAc;AAAA,EAC/D;AAAA,EACA,YAAY,YAAY;AACtB,UAAM,OAAO,KAAK,KAAK,UAAQ,KAAK,KAAK,EAAE,MAAM,UAAU;AAC3D,WAAO,QAAQ,MAAM,UAAU,MAAM;AAAA,EACvC;AAAA,EACA,kBAAkB,YAAY;AAC5B,UAAM,OAAO,KAAK,KAAK,UAAQ,KAAK,KAAK,EAAE,MAAM,UAAU;AAC3D,WAAO,MAAM,UAAU,KAAK,WAAS,MAAM,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,aAAa,YAAY;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,SAAY,QAAQ,CAAC;AAAA,IACnF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,uBAAsB,uBAAuB;AAAA,EACjD,eAAe,MAAM;AACnB,WAAO,QAAQ,KAAK,IAAI,KAAK,KAAK,YAAY,KAAK,IAAI;AAAA,EACzD;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,kBAAkB,KAAK,QAAQ,OAAO,UAAQ,KAAK,eAAe,IAAI,CAAC,CAAC;AAAA,EACtF;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,SAAS,KAAK,IAAM,WAAS,MAAM,OAAO,UAAQ,KAAK,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,aAAW,KAAK,kBAAkB,OAAO,CAAC,CAAC;AAAA,EAC9I;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,sBAAsB,mBAAmB;AACvD,gBAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,MAClJ;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,eAAe,IAAI,aAAa;AAAA,EACvC;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,OAAO,SAAS,gBAAgB,OAAO;AACrC,UAAM,eAAe,QAAQ,UAAU,gBAAgB,KAAK;AAC5D,SAAK,aAAa,IAAI,YAAY;AAClC,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,SAAK,aAAa,YAAY;AAAA,EAChC;AAAA,EACA,SAAS,cAAc;AACrB,SAAK,UAAU,YAAY;AAC3B,QAAI,cAAc;AAChB,mBAAa,YAAY;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,YAAY;AAAA,EAChC;AAAA,EACA,UAAU,cAAc;AACtB,QAAI,CAAC,aAAc;AACnB,SAAK,aAAa,OAAO,YAAY;AAAA,EACvC;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,YAAY;AAC9B,SAAK,eAAe,IAAI,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,UAAU,SAAO,CAAC,GAAG,SAAS,KAAK,GAAG;AAC5C,IAAM,cAAc,IAEjB,SAAS,CAAC,GAAG,SAAS,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAI,GAAG,GAAG,IAAI;AACjE,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AACZ,SAAK,KAAK;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,SAAS,KAAK,SAAS;AAC5B,SAAK,YAAY,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,UAAU,UAAU,UAAU,IAAI;AAAA,EAChD;AAAA,EACA,KAAK,KAAK,QAAQ,UAAU;AAC1B,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EAC/C;AAAA,EACA,aAAa;AACX,SAAK,OAAO,SAAS,OAAO;AAAA,EAC9B;AAAA,EACA,aAAa,MAAM,UAAU;AAC3B,UAAM,UAAU,KAAK,OAAO,IAAI,gBAAgB,IAAI;AACpD,UAAM,IAAI,KAAK,SAAS,cAAc,GAAG;AACzC,MAAE,MAAM,UAAU;AAClB,MAAE,OAAO;AACT,MAAE,WAAW;AACb,SAAK,SAAS,KAAK,YAAY,CAAC;AAChC,MAAE,cAAc,IAAI,WAAW,SAAS;AAAA,MACtC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,MAAM,KAAK;AAAA,IACb,CAAC,CAAC;AACF,SAAK,OAAO,IAAI,gBAAgB,OAAO;AACvC,SAAK,SAAS,KAAK,YAAY,CAAC;AAAA,EAClC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,SAAS,KAAK,SAAS;AAC5B,SAAK,YAAY,KAAK,OAAO;AAC7B,SAAK,UAAU,IAAI,gBAAgB,KAAK,UAAU,MAAM;AACxD,SAAK,SAAS,OAAO,KAAK,UAAU,MAAM;AAC1C,SAAK,gBAAgB,SAAS,MAAM,KAAK,OAAO,CAAC;AACjD,SAAK,OAAO,iBAAiB,WAAW,MAAM,KAAK,UAAU,KAAK,CAAC;AACnE,SAAK,OAAO,iBAAiB,UAAU,MAAM,KAAK,UAAU,IAAI,CAAC;AAAA,EACnE;AAAA,EACA,UAAU,KAAK;AACb,SAAK,OAAO,IAAI,GAAG;AACnB,SAAK,QAAQ,KAAK,GAAG;AAAA,EACvB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,QAAQ,aAAa;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA2B;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,SAAS,OAAO,QAAQ,EAAE;AAC/B,SAAK,OAAO,iBAAiB,WAAW,WAAS;AAC/C,UAAI,MAAM,QAAQ,kBAAkB,MAAM,aAAa,MAAM;AAC3D,aAAK,OAAO,SAAS,OAAO;AAAA,MAC9B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,6BAA4B;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,cAAc;AAAA,EAC3C,cAAc;AACZ,UAAM;AACN,SAAK,QAAQ,OAAO,KAAK;AACzB,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,qBAAqB,OAAO,sBAAsB;AAAA,MACrD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,kBAAkB,SAAS,KAAK,oBAAoB,eAAe;AACxE,WAAO,MAAM;AACX,UAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAK,YAAY,KAAK,WAAW;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,UAAM,QAAQ,KAAK,WAAW,WAAW;AACzC,UAAM,cAAc,KAAK,oBAAoB,QAAQ;AAAA,MACnD,KAAK;AAAA,MACL,cAAc;AAAA,IAChB,CAAC;AACD,QAAI,CAAC,OAAO;AACV,aAAO,KAAK,MAAM,SAAS,WAAW;AAAA,IACxC;AACA,QAAI,gBAAgB,KAAK,oBAAoB,QAAQ;AAAA,MACnD,KAAK;AAAA,MACL,cAAc;AAAA,IAChB,CAAC;AACD,QAAI,CAAC,KAAK,oBAAoB;AAC5B,uBAAiB,MAAM,WAAW;AAAA,IACpC;AACA,SAAK,MAAM,SAAS,aAAa;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,CAAC,SAAS,WAAW,KAAK,YAAY,QAAQ;AAAA,MACvD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,8BAA8B,QAAQ;AAAA,MACxC;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAuC,SAAS,WAAW,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oCAAmC;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MACrD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,aAAa,MAAM;AAAA,QACnB,cAAc,MAAM;AAAA,MACtB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAsC,SAAS,WAAW,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mCAAkC;AAAA,MAC3C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,SAAS,QAAQ;AACf,SAAK,MAAM,IAAI,MAAM;AAAA,EACvB;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,YAAY,kBAAkB,mCAAmC,8BAA8B;AAC7F,SAAK,mBAAmB;AACxB,SAAK,oCAAoC;AACzC,SAAK,+BAA+B;AACpC,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,QAAQ,IAAI,cAAc,CAAC,CAAC;AACjC,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,SAAK,cAAc,KAAK,UAAU,MAAM,KAAK,iBAAiB,IAAI;AAAA,MAChE,8BAA8B,CAAC,CAAC,KAAK;AAAA,IACvC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,cAAY,KAAK,sCAAsC,QAAQ,CAAC,CAAC,EAAE,UAAU,SAAO,KAAK,MAAM,IAAI,GAAG,CAAC;AAAA,EAC7H;AAAA,EACA,sCAAsC,UAAU;AAC9C,QAAI,CAAC,SAAS,aAAa,eAAe,aAAa;AACrD,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,WAAO,KAAK,wBAAwB,SAAS,aAAa,eAAe,WAAW,EAAE,KAAK,IAAI,YAAW,iCACrG,WADqG;AAAA,MAExG,cAAc,kCACT,SAAS,eACT;AAAA,IAEP,EAAE,CAAC;AAAA,EACL;AAAA,EACA,wBAAwB,aAAa;AACnC,WAAO,KAAK,kCAAkC,IAAI;AAAA,MAChD;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc,KAAK;AACxB,WAAO,KAAK,qBAAqB,WAAS,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,EAC/D;AAAA,EACA,oBAAoB,MAAM;AACxB,QAAI,KAAK,8BAA8B;AACrC,aAAO,KAAK,gBAAgB,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,IACpD;AACA,WAAO,KAAK,wBAAwB,IAAI,EAAE,KAAK,IAAI,YAAU,KAAK,MAAM,MAAM;AAAA,MAC5E,cAAc,kCACT,KAAK,MAAM,MAAM,eACjB;AAAA,IAEP,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,GAAG,CAAC;AAAA,EAClD;AAAA,EACA,OAAO,KAAK;AACV,WAAO,KAAK,MAAM,MAAM,GAAG;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,WAAO,KAAK,MAAM,WAAW,WAAS,KAAK;AAAA,EAC7C;AAAA,EACA,SAAS;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EACA,SAAS,MAAM;AACb,WAAO,UAAU,IAAI;AACrB,WAAO,KAAK,MAAM,WAAW,WAAS,KAAK,EAAE,KAAK,IAAI,WAAS;AAC7D,aAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC/B,YAAI,KAAK;AACP,iBAAO,IAAI,GAAG;AAAA,QAChB;AACA,eAAO;AAAA,MACT,GAAG,KAAK;AAAA,IACV,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,UAAU,IAAI;AACrB,WAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC/B,UAAI,KAAK;AACP,eAAO,IAAI,GAAG;AAAA,MAChB;AACA,aAAO;AAAA,IACT,GAAG,KAAK,MAAM,KAAK;AAAA,EACrB;AAAA,EACA,WAAW,KAAK;AACd,WAAO,KAAK,MAAM,MAAM,UAAU,SAAS,GAAG;AAAA,EAChD;AAAA,EACA,YAAY,KAAK;AACf,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,UAAU,SAAS,GAAG,CAAC;AAAA,EACrE;AAAA,EACA,YAAY,MAAM;AAChB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,MAAM;AACf,QAAI,CAAC,SAAU;AACf,WAAO,KAAK,OAAO,CAAC,KAAK,QAAS,iCAC7B,MAD6B;AAAA,MAEhC,CAAC,GAAG,GAAG,SAAS,OAAO,GAAG;AAAA,IAC5B,IAAI,CAAC,CAAC;AAAA,EACR;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,MAAM,WAAW,CAAC;AAAA,MAC5B;AAAA,IACF,MAAM;AACJ,UAAI,CAAC,UAAU,OAAQ;AACvB,aAAO,KAAK,OAAO,CAAC,KAAK,QAAS,iCAC7B,MAD6B;AAAA,QAEhC,CAAC,GAAG,GAAG,SAAS,OAAO,GAAG;AAAA,MAC5B,IAAI,CAAC,CAAC;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,WAAW,KAAK;AACd,WAAO,KAAK,MAAM,MAAM,SAAS,SAAS,GAAG;AAAA,EAC/C;AAAA,EACA,YAAY,KAAK;AACf,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,SAAS,SAAS,GAAG,CAAC;AAAA,EACpE;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,WAAW,KAAK,MAAM,MAAM,SAAS,UAAU,CAAC;AACtD,QAAI,CAAC,QAAS,QAAO;AACrB,UAAM,YAAY,OAAO,KAAK,QAAQ,EAAE,OAAO,SAAO,IAAI,QAAQ,OAAO,IAAI,EAAE;AAC/E,WAAO,UAAU,OAAO,CAAC,KAAK,QAAQ;AACpC,UAAI,GAAG,IAAI,SAAS,GAAG;AACvB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA,EACA,aAAa,SAAS;AACpB,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,SAAS,MAAM,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM;AACvF,UAAI,CAAC,QAAS,QAAO;AACrB,YAAM,YAAY,OAAO,KAAK,QAAQ,EAAE,OAAO,SAAO,IAAI,QAAQ,OAAO,IAAI,EAAE;AAC/E,aAAO,UAAU,OAAO,CAAC,KAAK,QAAQ;AACpC,YAAI,GAAG,IAAI,SAAS,GAAG;AACvB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,MAAM,WAAW,WAAS,MAAM,cAAc;AAAA,EAC5D;AAAA,EACA,uBAAuB,KAAK,gBAAgB;AAC1C,UAAM,WAAW,eAAe,mBAAmB,CAAC;AACpD,WAAO,SAAS,KAAK,OAAK,QAAQ,CAAC;AAAA,EACrC;AAAA,EACA,0BAA0B,KAAK;AAC7B,WAAO,KAAK,uBAAuB,KAAK,KAAK,MAAM,MAAM,cAAc;AAAA,EACzE;AAAA,EACA,2BAA2B,KAAK;AAC9B,WAAO,KAAK,MAAM,WAAW,WAAS,KAAK,uBAAuB,KAAK,MAAM,cAAc,CAAC;AAAA,EAC9F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,SAAS,kCAAkC,GAAM,SAAS,iCAAiC,GAAM,SAAS,qCAAqC,CAAC,CAAC;AAAA,IAC3M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,UAAU,MAAM;AACvB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,MAAM,GAAG;AAAA,EACvB;AACA,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc,KAAK,aAAa,YAAY;AAAA,EAC1D;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,iBAAiB,aAAa;AAAA,EAC5C;AAAA,EACA,YAAY,cAAc,UAAU,eAAe,aAAa;AAC9D,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,aAAa,KAAK,aAAa,YAAY;AAChD,SAAK,mBAAmB,IAAI,QAAQ;AACpC,SAAK,mBAAmB,IAAI,gBAAgB,oBAAI,IAAI,CAAC;AACrD,SAAK,iBAAiB,IAAI,gBAAgB,oBAAI,IAAI,CAAC;AACnD,QAAI,cAAe,OAAM,IAAI,MAAM,oDAAoD;AACvF,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,yBAAyB;AACvB,mBAAe,UAAU,SAAO,KAAK,gBAAgB,GAAG,CAAC;AACzD,UAAM,mBAAmB,KAAK,YAAY,SAAS,qBAAqB;AACxE,UAAM,uBAAuB,KAAK,YAAY,SAAS,wBAAwB;AAC/E,UAAM,mBAAmB,KAAK,aAAa,aAAa;AACxD,UAAM,mBAAmB,cAAc,CAAC,kBAAkB,KAAK,gBAAgB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,aAAa,aAAa,MAAM,cAAc,IAAI,WAAW,CAAC,CAAC;AAC5J,kBAAc,CAAC,kBAAkB,sBAAsB,gBAAgB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,QAAQ,UAAU,KAAK,MAAM;AAChH,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AACA,YAAM,SAAS,6BAA6B,UAAU,CAAC,GAAG,QAAQ;AAClE,UAAI,QAAQ;AACV,YAAI,CAAC,OAAO;AACV,kBAAQ,oBAAI,IAAI;AAAA,QAClB;AACA,eAAO,QAAQ,MAAM,EAAE,QAAQ,WAAS;AACtC,gBAAM,eAAe,MAAM,CAAC;AAC5B,gBAAM,cAAc,MAAM,CAAC;AAC3B,cAAIG,YAAW,OAAO,IAAI,YAAY,KAAK,CAAC;AAC5C,UAAAA,YAAW,kCACNA,YACA;AAEL,iBAAO,IAAI,cAAcA,SAAQ;AAAA,QACnC,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC,GAAG,OAAO,OAAO,CAAC,EAAE,UAAU,SAAO,KAAK,eAAe,KAAK,GAAG,CAAC;AAAA,EACrE;AAAA,EACA,gBAAgB,eAAe;AAC7B,QAAI,CAAC,cAAe;AACpB,UAAM,kBAAkB,KAAK,iBAAiB;AAC9C,kBAAc,QAAQ,SAAO;AAC3B,YAAM,aAAa,gBAAgB,IAAI,IAAI,OAAO,KAAK,oBAAI,IAAI;AAC/D,UAAI,UAAU,QAAQ,SAAO;AAC3B,YAAI,WAAW,WAAW,IAAI,IAAI,YAAY,KAAK,CAAC;AACpD,mBAAW,kCACN,WACA,IAAI;AAET,mBAAW,IAAI,IAAI,cAAc,QAAQ;AAAA,MAC3C,CAAC;AACD,sBAAgB,IAAI,IAAI,SAAS,UAAU;AAAA,IAC7C,CAAC;AACD,SAAK,iBAAiB,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,sBAAsB;AACpB,SAAK,aAAa,kBAAkB,EAAE,KAAK,OAAO,UAAQ,KAAK,YAAY,QAAQ,yCAAyC,MAAM,IAAI,GAAG,UAAU,UAAQ,KAAK,YAAY,oBAAoB,IAAI,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC,GAAG,OAAO,OAAO,GAAG,UAAU,UAAQ,KAAK,KAAK,eAAe,IAAI,EAAE,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,UAAQ,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAAA,EACxW;AAAA,EACA,eAAe,QAAQ;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,SAAS,IAAI,YAAY;AAClC,WAAO,iBAAiB,MAAM,EAAE,KAAK,YAAU;AAC7C,UAAI,QAAQ,QAAS,oBAAmB,OAAO,OAAO;AACtD,WAAK,aAAa;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,mBAAmB;AAC7B,WAAO,KAAK,YAAY,QAAQ,EAAE,KAAK,IAAI,WAAS,KAAK,gBAAgB,OAAO,KAAK,GAAG,iBAAiB,CAAC,CAAC;AAAA,EAC7G;AAAA,EACA,YAAY,cAAc;AACxB,WAAO,KAAK,eAAe,MAAM,IAAI,YAAY;AAAA,EACnD;AAAA,EACA,aAAa,cAAc;AACzB,WAAO,KAAK,eAAe,KAAK,IAAI,SAAO,IAAI,IAAI,YAAY,CAAC,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,QAAQ,mBAAmB;AACjC,WAAO,KAAK,gBAAgB,KAAK,YAAY,OAAO,GAAG,KAAK,GAAG,iBAAiB;AAAA,EAClF;AAAA,EACA,SAAS,cAAc,KAAK,cAAc;AACxC,WAAO,KAAK,YAAY,QAAQ,cAAc,EAAE,KAAK,IAAI,eAAe,GAAG,IAAI,cAAY,SAAS,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,EACvI;AAAA,EACA,aAAa,cAAc,KAAK,cAAc;AAC5C,UAAM,eAAe,KAAK,YAAY,OAAO,cAAc;AAC3D,WAAO,gBAAgB,YAAY,EAAE,cAAc,KAAK,YAAY;AAAA,EACtE;AAAA,EACA,qBAAqB,eAAe,MAAM,cAAc;AACtD,WAAO,KAAK,YAAY,QAAQ,cAAc,EAAE,KAAK,IAAI,2BAA2B,GAAG,IAAI,0BAAwB,qBAAqB,eAAe,MAAM,YAAY,CAAC,CAAC;AAAA,EAC7K;AAAA,EACA,yBAAyB,eAAe,MAAM,cAAc;AAC1D,UAAM,eAAe,KAAK,YAAY,OAAO,cAAc;AAC3D,WAAO,4BAA4B,YAAY,EAAE,eAAe,MAAM,YAAY;AAAA,EACpF;AAAA,EACA,gBAAgB,OAAO,QAAQ,mBAAmB;AAChD,QAAI,eAAe;AACnB,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,QAAI,OAAO,QAAQ,UAAU;AAC3B,qBAAe,IAAI;AACnB,YAAM,IAAI;AAAA,IACZ;AACA,UAAM,OAAO,IAAI,MAAM,IAAI;AAC3B,UAAM,OAAO,aAAW;AACtB,UAAI,UAAU,EAAG,SAAQ,KAAK,OAAO;AAAA,IACvC;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,WAAK,mDAAmD;AACxD,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,MAAM,aAAc,QAAO,gBAAgB,KAAK,CAAC;AACtD,UAAM,aAAa,KAAK,CAAC,KAAK,MAAM,aAAa;AACjD,UAAM,YAAY,KAAK,CAAC;AACxB,QAAI,eAAe,KAAK;AACtB,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,CAAC,YAAY;AACf,WAAK,wFAAwF;AAC7F,aAAO,gBAAgB;AAAA,IACzB;AACA,UAAM,SAAS,KAAK,eAAe,MAAM,IAAI,UAAU;AACvD,QAAI,CAAC,QAAQ;AACX,WAAK,yCAAyC,UAAU;AACxD,aAAO,gBAAgB;AAAA,IACzB;AACA,QAAI,eAAe,OAAO,SAAS;AACnC,QAAI,OAAO,iBAAiB,aAAa;AACvC,aAAO,gBAAgB;AAAA,IACzB;AACA,wBAAoB,kBAAkB,OAAO,YAAU,UAAU,IAAI;AACrE,QAAI,aAAc,gBAAe,YAAY,cAAc,iBAAiB;AAC5E,QAAI,OAAO,iBAAiB,SAAU,gBAAe;AACrD,WAAO,gBAAgB,gBAAgB;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAS,mBAAmB,GAAM,SAAY,QAAQ,GAAM,SAAS,sBAAqB,EAAE,GAAM,SAAS,kBAAkB,CAAC;AAAA,IACzL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,8BAA8B,kBAAkB,QAAQ;AAC/D,QAAM,OAAO,OAAO,gBAAgB;AACpC,MAAI,KAAK,cAAc,WAAW,GAAG;AACnC,WAAO;AAAA,EACT;AACA,SAAO,KAAK,cAAc,OAAO,CAAC,KAAK,iBAAiB;AACtD,UAAM,WAAW,8BAA8B,cAAc,MAAM;AACnE,UAAM,QAAQ,kCACT,SAAS,QACT,KAAK;AAEV,WAAO,iCACF,MADE;AAAA,MAEL;AAAA,IACF;AAAA,EACF,GAAG,IAAI;AACT;AACA,SAAS,+BAA+B,UAAU;AAChD,QAAM,WAAW,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAO;AAChD,UAAM,WAAW,8BAA8B,KAAK,QAAQ;AAC5D,WAAO,CAAC,KAAK,QAAQ;AAAA,EACvB,CAAC;AACD,SAAO,SAAS,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAO,iCAC1C,MAD0C;AAAA,IAE7C,CAAC,GAAG,GAAG;AAAA,EACT,IAAI,CAAC,CAAC;AACR;AACA,SAAS,6BAA6B,QAAQ,UAAU;AACtD,QAAM,iBAAiB,+BAA+B,QAAQ;AAC9D,SAAO,OAAO,QAAQ,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAClE,WAAO,iCACF,MADE;AAAA,MAEL,CAAC,GAAG,GAAG,MAAM;AAAA,IACf;AAAA,EACF,GAAG,MAAM;AACX;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,wBAAwB;AAClC,SAAK,UAAU,OAAO,qBAAqB;AAC3C,SAAK,kBAAkB;AACvB,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,QAAQ,OAAO,cAAc;AAClC,SAAK,SAAS,OAAO,aAAa;AAClC,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,wBAAwB,OAAO,4BAA4B;AAChE,SAAK,eAAe,OAAO,mBAAmB;AAC9C,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,cAAc,OAAO,kBAAkB;AAC5C,QAAI,wBAAwB;AAC1B,UAAI,UAAU,EAAG,SAAQ,KAAK,2DAA2D;AACzF;AAAA,IACF;AACA,SAAK,2BAA2B;AAChC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,QAAQ;AACf;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,YAAY,eAAe;AACpC,QAAI,YAAY,iBAAiB,QAAQ;AACvC,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,UAAM,iBAAiB,KAAK,aAAa,oBAAoB,KAAK;AAClE,SAAK,aAAa,OAAO,gBAAgB,MAAM,KAAK,UAAU,CAAC;AAAA,EACjE;AAAA,EACA,YAAY;AACV,QAAI,iBAAiB,KAAK,mBAAmB;AAC7C,QAAI,CAAC,eAAgB,kBAAiB;AACtC,QAAI,KAAK,cAAc,eAAgB;AACvC,UAAM,MAAM,KAAK,QAAQ,IAAI,cAAc;AAC3C,QAAI,KAAK;AACP,WAAK,SAAS,KAAK,aAAa,GAAG,GAAG;AACtC,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,wBAAwB,cAAc;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,UAAM,YAAY,KAAK,MAAM,SAAS,QAAQ,CAAC;AAC/C,QAAI,iBAAiB,UAAU,QAAQ;AACvC,QAAI,OAAO,UAAU,KAAK,QAAQ,aAAa,KAAK,MAAM,CAAC;AAC3D,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AACA,WAAO,KAAK,QAAQ;AAClB,aAAO,KAAK;AACZ,UAAI,KAAK,QAAQ;AACf,yBAAiB,KAAK;AACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,YAAY;AAClC,QAAI,UAAU,UAAU,UAAU;AAClC,QAAI,eAAe,WAAW;AAC5B,gBAAU;AAAA,IACZ;AACA,YAAQ,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,yBAAyB;AACvB,SAAK,aAAa,OAAO,KAAK,oBAAoB,iBAAiB,MAAM;AACvE,WAAK,kBAAkB;AACvB,iBAAW,MAAM,KAAK,kBAAkB,MAAM,CAAC;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,aAAa,KAAK;AAChB,WAAO,KAAK,sBAAsB,IAAI,GAAG;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAkB,yBAAwB,EAAE,CAAC;AAAA,IAC3G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AAAA,MACvD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAAA,MACjC,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACzF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,cAAc,CAAM,iBAAiB;AAAA,MACrC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,YAAY,OAAO,uBAAuB,cAAc;AACtD,SAAK,QAAQ;AACb,SAAK,wBAAwB;AAC7B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB,KAAK,MAAM,SAAS,KAAK,qBAAqB;AACtE,SAAK,eAAe,KAAK,MAAM,SAAS,KAAK,qBAAqB;AAClE,UAAM,aAAa,KAAK,sBAAsB,KAAK,KAAK,YAAY,EAAE,KAAK,qBAAqB,CAAC;AACjG,SAAK,aAAa,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM;AACjD,WAAK,oBAAoB,IAAI;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAuC,kBAAuB,cAAc,GAAM,kBAAkB,4BAA4B,GAAM,kBAAkB,mBAAmB,CAAC;AAAA,IAC/M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iCAAiC,CAAC;AAAA,MAC/C,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AAAA,MACvD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAAA,MACjC,UAAU,SAAS,4CAA4C,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACtG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,qBAAqB,IAAI,qBAAqB,IAAI,gBAAgB;AAAA,QAClF;AAAA,MACF;AAAA,MACA,cAAc,CAAM,iBAAiB;AAAA,MACrC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA,MAGV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,eAAe;AAAA,QACjC;AAAA,MACF;AAAA,MACA,cAAc,CAAM,YAAY;AAAA,MAChC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,mBAAmB;AAAA,EACvB,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,cAAc;AAAA,EACd,cAAc;AAAA,EACd,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,KAAK;AAAA,EACL,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,SAAS;AAAA,EACT,KAAK;AAAA,EACL,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,eAAe;AAAA,EACf,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,0BAA0B,oBAAI,IAAI,CAAC;AAAA,EAAC;AAAA,EAA6C;AAAA;AAA0F,GAAG;AAAA,EAAC;AAAA,EAAqC;AAAA;AAAkF,GAAG;AAAA,EAAC;AAAA,EAAiC;AAAA;AAA8E,CAAC,CAAC;AACja,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,IAAI,MAAM,KAAK;AACb,SAAK,SAAS,OAAO,GAAG,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,kBAAkB;AAChB,eAAW,MAAM,KAAK,MAAM,cAAc,MAAM,GAAG,KAAK,KAAK;AAAA,EAC/D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,UAAU,CAAC;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACjC,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,aAAa,OAAO;AAAA,MACjC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,IAAI,cAAc;AAC5B,SAAK,KAAK;AACV,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,WAAW;AACT,UAAM,SAAS,UAAU,KAAK,GAAG,eAAe,OAAO,EAAE,KAAK,aAAa,KAAK,QAAQ,CAAC;AACzF,SAAK,aAAa,OAAO,QAAQ,WAAS;AACxC,WAAK,cAAc,KAAK,KAAK;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,UAAU,GAAM,kBAAkB,mBAAmB,CAAC;AAAA,IAC9I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AAAA,MACtC,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,WAAWH,QAAO,OAAO,MAAM;AACzC,SAAK,YAAY;AACjB,SAAK,QAAQA;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,QAAQ,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,IAAI,YAAY;AACd,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,YAAY,CAACA,QAAO,SAAS,KAAK,MAAMA;AAAA,EACtD;AAAA,EACA,YAAY,SAAS,OAAO,SAAS;AACnC,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,6BAA6B,SAAS;AACpC,UAAM,KAAK,CAAC;AACZ,YAAQ,iBAAiB,CAAC,QAAQ,eAAe,iBAAiB;AAChE,UAAI,OAAO,iBAAiB,MAAM;AAChC,cAAM,OAAO,KAAK,MAAM,mBAAmB,KAAK,SAAS,IAAI,cAAc,MAAM,IAAI,IAAI,KAAK,KAAK,GAAG,gBAAgB,CAAC;AACvH,WAAG,KAAK,IAAI,WAAW,QAAQ,IAAI,CAAC;AAAA,MACtC,WAAW,gBAAgB,QAAQ,kBAAkB,MAAM;AACzD,aAAK,MAAM,OAAO,aAAa;AAAA,MACjC,OAAO;AACL,YAAI,kBAAkB,MAAM;AAC1B,gBAAM,OAAO,KAAK,MAAM,IAAI,aAAa;AACzC,cAAI,QAAQ,iBAAiB,MAAM;AACjC,iBAAK,MAAM,KAAK,MAAM,YAAY;AAClC,eAAG,KAAK,IAAI,WAAW,QAAQ,IAAI,CAAC;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,aAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACzC,SAAG,CAAC,EAAE,KAAK,QAAQ,YAAY,GAAG,CAAC,EAAE,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,yBAAyB,SAAS;AAChC,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK;AACjD,YAAM,UAAU,KAAK,MAAM,IAAI,CAAC;AAChC,cAAQ,QAAQ,QAAQ;AACxB,cAAQ,QAAQ,QAAQ;AACxB,cAAQ,QAAQ,OAAO,KAAK;AAAA,IAC9B;AACA,YAAQ,sBAAsB,YAAU;AACtC,UAAI,OAAO,iBAAiB,MAAM;AAChC,cAAM,UAAU,KAAK,MAAM,IAAI,OAAO,YAAY;AAClD,gBAAQ,QAAQ,YAAY,OAAO;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,MAAM,UAAU,KAAK,UAAU;AAClC,WAAK,MAAM,MAAM;AACjB,WAAK,MAAM,mBAAmB,KAAK,QAAQ,EAAE;AAC7C,WAAK,iBAAiB;AACtB,WAAK,SAAS;AACd;AAAA,IACF;AACA,QAAI,KAAK,YAAY,KAAK,gBAAgB;AACxC,WAAK,MAAM,MAAM;AACjB,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,CAAC,KAAK,UAAU,OAAO;AACzB,WAAK,SAAS,KAAK,QAAQ,KAAK,KAAK,EAAE,OAAO,KAAK,SAAS;AAAA,IAC9D;AACA,QAAI,KAAK,QAAQ;AACf,YAAM,UAAU,KAAK,OAAO,KAAK,KAAK;AACtC,UAAI,SAAS;AACX,aAAK,6BAA6B,OAAO;AACzC,aAAK,yBAAyB,OAAO;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,YAAM,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC;AAAA,IACrF,OAAO;AACL,YAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,QAAQ,gBAAM,KAAK,KAAK;AAC5B,QAAI,CAAC,MAAM,QAAQ,KAAK,EAAG;AAC3B,UAAM,YAAY,KAAK;AACvB,UAAM,WAAW,KAAK;AACtB,QAAI,OAAO,aAAa,eAAe,OAAO,KAAK,cAAc,eAAe,KAAK,cAAc,IAAI;AACrG,cAAQ,MAAM,OAAO,UAAQ,UAAU,KAAK,QAAQ,GAAG,KAAK,SAAS,CAAC;AAAA,IACxE;AACA,YAAQ,KAAK,UAAU;AAAA,MACrB,KAAK;AACH,aAAK,UAAU,KAAK;AACpB,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB,cAAM,QAAQ;AACd,aAAK,aAAa,KAAK;AACvB;AAAA,MACF;AACE,aAAK,aAAa,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAiB,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,eAAe,CAAC;AAAA,IAC1K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,MAC9B,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,YAAY,OAAO;AAAA,QAC9B,SAAS,CAAC,GAAG,iBAAiB,SAAS;AAAA,QACvC,UAAU,CAAC,GAAG,kBAAkB,UAAU;AAAA,QAC1C,UAAU,CAAC,GAAG,kBAAkB,UAAU;AAAA,QAC1C,WAAW,CAAC,GAAG,mBAAmB,WAAW;AAAA,QAC7C,SAAS,CAAC,GAAG,iBAAiB,SAAS;AAAA,QACvC,WAAW,CAAC,GAAG,mBAAmB,WAAW;AAAA,QAC7C,UAAU,CAAC,GAAG,kBAAkB,UAAU;AAAA,MAC5C;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,oBAAoB,MAAM,OAAO,cAAc;AACzD,SAAK,qBAAqB;AAC1B,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,wBAAwB;AAC7B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,WAAW;AACT,SAAK,aAAa,OAAO,KAAK,mBAAmB,UAAU,MAAM;AAC/D,UAAI,KAAK,uBAAuB;AAC9B,aAAK,YAAY;AAAA,MACnB;AACA,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AACD,UAAM,SAAS,UAAU,KAAK,KAAK,eAAe,OAAO,EAAE,KAAK,aAAa,KAAK,QAAQ,GAAG,OAAO,WAAS,EAAE,MAAM,kBAAkB,oBAAoB,GAAG,OAAO,WAAS,SAAS,MAAM,QAAQ,OAAO,CAAC;AAC7M,SAAK,aAAa,OAAO,QAAQ,MAAM;AACrC,UAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAK,KAAK,cAAc,cAAc,IAAI,MAAM,UAAU;AAAA,UACxD,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC,CAAC;AAAA,MACJ;AACA,WAAK,mBAAmB;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,aAAS,KAAK,QAAQ;AACtB,SAAK,YAAY;AACjB,SAAK,MAAM,cAAc;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAuB,oBAAoB,CAAC,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,mBAAmB,CAAC;AAAA,IACpO;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,QAAQ,YAAY,IAAI,aAAa,EAAE,CAAC;AAAA,MACrD,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,qBAAqB;AAAA,QACrB,uBAAuB;AAAA,MACzB;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,SAAS,UAAU;AAC1B,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,aAAS,QAAQ,WAAS;AACxB,eAAS,MAAM,QAAQ;AAAA,IACzB,CAAC;AACD;AAAA,EACF;AACA,SAAO,KAAK,QAAQ,EAAE,QAAQ,SAAO;AACnC,aAAS,GAAG,EAAE,YAAY;AAC1B,aAAS,GAAG,EAAE,uBAAuB;AAAA,EACvC,CAAC;AACH;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,OAAO;AACjB,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,kBAAkB;AAChB,SAAK,KAAK,KAAK,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAqB,UAAU,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,SAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,aAAa,OAAO,mBAAmB,OAAO,OAAO;AAC/D,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAC1B,SAAK,aAAa,IAAI,cAAc;AACpC,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,YAAY;AAAA,IAChC;AACA,SAAK,eAAe,KAAK,kBAAkB,kBAAkB,KAAK,aAAa,EAAE,EAAE,KAAK,qBAAqB,CAAC,EAAE,UAAU,eAAa;AACrI,WAAK,MAAM,MAAM;AACjB,UAAI,UAAW,MAAK,MAAM,mBAAmB,KAAK,WAAW;AAC7D,UAAI,KAAK,oBAAoB;AAC3B,YAAI,CAAC,KAAK,UAAU;AAClB,eAAK,WAAW,KAAK;AAAA,QACvB,OAAO;AACL,eAAK,MAAM,cAAc;AAAA,QAC3B;AAAA,MACF,OAAO;AACL,aAAK,MAAM,aAAa;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,aAAc,MAAK,aAAa,YAAY;AAAA,EACvD;AAAA,EACA,cAAc;AACZ,SAAK,MAAM;AAAA,EACb;AAAA,EACA,kBAAkB;AAChB,SAAK,WAAW,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,cAAc,CAAC,CAAC;AAC9F,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,aAAa,CAAC,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,iBAAiB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,aAAa,CAAC;AAAA,IACpQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,QAAQ;AAAA,QACN,WAAW,CAAC,GAAG,iBAAiB,WAAW;AAAA,QAC3C,oBAAoB,CAAC,GAAG,mCAAmC,oBAAoB;AAAA,MACjF;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAY,UAAU,aAAa,OAAO,uBAAuB,cAAc;AAC7E,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,wBAAwB;AAC7B,SAAK,eAAe;AACpB,SAAK,eAAe;AAAA,MAClB,QAAQ,CAAC;AAAA,MACT,SAAS,CAAC;AAAA,IACZ;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,gCAAgC,CAAC;AACtC,SAAK,cAAc;AACnB,SAAK,UAAU;AAAA,MACb,cAAc,SAAO;AACnB,aAAK,sBAAsB;AAC3B,aAAK,sBAAsB;AAC3B,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,UAAM,aAAa,KAAK,sBAAsB,KAAK,KAAK,KAAK,YAAY,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,eAAe,CAAC,kBAAQ,IAAI,WAAW,KAAK,iBAAiB,CAAC,CAAC;AAC1K,SAAK,aAAa,OAAO,YAAY,CAAC,MAAM,CAAC,MAAM;AACjD,WAAK,MAAM,MAAM;AACjB,WAAK,oBAAoB,IAAI;AAC7B,UAAI,KAAK,qBAAqB;AAC5B,aAAK,sBAAsB;AAAA,MAC7B;AACA,UAAI,IAAI,WAAW;AACjB,aAAK,gBAAgB;AACrB,cAAM,iBAAiB,SAAS,OAAO;AAAA,UACrC,WAAW,CAAC;AAAA,YACV,SAAS;AAAA,YACT,UAAU,KAAK;AAAA,UACjB,CAAC;AAAA,UACD,QAAQ,KAAK;AAAA,QACf,CAAC;AACD,cAAM,MAAM,KAAK,MAAM,gBAAgB,IAAI,WAAW;AAAA,UACpD,OAAO;AAAA,UACP,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,OAAO;AACL,aAAK,MAAM,mBAAmB,KAAK,aAAa,KAAK,OAAO;AAAA,MAC9D;AACA,WAAK,cAAc;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,SAAS,MAAM,cAAc,UAAU,KAAK,qBAAqB;AACnE,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,uBAAuB,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,KAAK,QAAS;AAC1E,QAAI,KAAK,KAAK,QAAQ;AACpB,iBAAW,OAAO,KAAK,KAAK,QAAQ;AAClC,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,KAAK,QAAQ,GAAG,GAAG;AAC/D,cAAI,CAAC,kBAAQ,KAAK,oBAAoB,GAAG,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,KAAK,GAAG;AACxE,iBAAK,oBAAoB,GAAG,IAAI,KAAK,KAAK,OAAO,GAAG,EAAE;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,KAAK,SAAS;AACrB,iBAAW,OAAO,KAAK,KAAK,SAAS;AACnC,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,KAAK,SAAS,GAAG,GAAG;AAChE,cAAI,CAAC,KAAK,8BAA8B,GAAG,GAAG;AAC5C,iBAAK,8BAA8B,GAAG,IAAI,KAAK,oBAAoB,GAAG,EAAE,UAAU,WAAS;AACzF,mBAAK,KAAK,UAAU,GAAG,EAAE,KAAK;AAAA,YAChC,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe;AAAA,MAClB,SAAS,CAAC;AAAA,OACP,KAAK,OAFU;AAAA,MAGlB,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,CAAC,KAAK,KAAK,OAAQ;AACvB,WAAO,iBAAiB,KAAK,aAAa,QAAQ,mBAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,QAAS,iCAClD,MADkD;AAAA,MAErD,CAAC,GAAG,GAAG;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,KAAK,MAAM,KAAK,KAAK,SAAS,GAAG,GAAG;AAAA,SAChC,KAAK,KAAK,SAAS,GAAG,GAAG,UAAU;AAAA,QACrC,KAAK,cAAY;AACf,cAAI,KAAK,KAAK,SAAS,GAAG,GAAG;AAC3B,iBAAK,KAAK,OAAO,GAAG,EAAE,QAAQ;AAAA,UAChC;AACA,cAAI,KAAK,KAAK,UAAU,GAAG,GAAG,QAAQ,GAAG;AACvC,iBAAK,KAAK,QAAQ,GAAG,GAAG,QAAQ,EAAE,QAAQ;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAAA,IAEJ,IAAI,CAAC,CAAC,EACP;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,KAAK,6BAA6B,EAAE,QAAQ,SAAO;AAC7D,WAAK,8BAA8B,GAAG,EAAE,YAAY;AAAA,IACtD,CAAC;AACD,SAAK,gCAAgC,CAAC;AACtC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,kBAAqB,QAAQ,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,4BAA4B,GAAM,kBAAkB,mBAAmB,CAAC;AAAA,IAClR;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,MAC9C,QAAQ;AAAA,QACN,MAAM,CAAC,GAAG,0BAA0B,MAAM;AAAA,MAC5C;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,GAAM,oBAAoB;AAAA,IAClF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,IAAI,cAAc;AAC5B,SAAK,KAAK;AACV,SAAK,eAAe;AACpB,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,WAAW;AACT,SAAK,aAAa,OAAO,UAAU,KAAK,GAAG,eAAe,OAAO,GAAG,WAAS;AAC3E,YAAM,gBAAgB;AACtB,WAAK,cAAc,KAAK,KAAK;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,UAAU,GAAM,kBAAkB,mBAAmB,CAAC;AAAA,IAC3I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW,CAAC,mBAAmB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,UAAU,QAAQ,OAAO,mBAAmB;AAC1C,UAAM,SAAS,kBAAkB,OAAO,CAAC,KAAK,QAAQ;AACpD,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AACA,aAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG;AAAA,IAC7D,GAAG,CAAC,CAAC,KAAK,CAAC;AACX,WAAO,KAAK,aAAa,QAAQ,OAAO,GAAG,MAAM;AAAA,EACnD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAkB,qBAAqB,EAAE,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,gBAAgB;AAAA,MAC/B,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB;AAAA,MAC1B,cAAc,CAAC,gBAAgB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,UAAU,OAAO,YAAY,OAAO,SAAS;AAC3C,gBAAY,aAAa,UAAU,YAAY;AAC/C,QAAI,CAAC,SAAS,cAAc,SAAS,cAAc,OAAQ,QAAO;AAClE,QAAI,cAAc,CAAC;AACnB,QAAI,cAAc,CAAC;AACnB,QAAI,CAAC,SAAS;AACZ,oBAAc,MAAM,OAAO,UAAQ,OAAO,SAAS,QAAQ,EAAE,KAAK;AAClE,oBAAc,MAAM,OAAO,UAAQ,OAAO,SAAS,QAAQ,EAAE,KAAK;AAAA,IACpE,OAAO;AACL,oBAAc,MAAM,OAAO,UAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC;AAC5G,oBAAc,MAAM,OAAO,UAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM;AACnF,YAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAG,QAAO;AAAA,iBAAY,EAAE,OAAO,IAAI,EAAE,OAAO,EAAG,QAAO;AAAA,YAAO,QAAO;AAAA,MAChG,CAAC;AAAA,IACH;AACA,UAAM,SAAS,CAAC,GAAG,aAAa,GAAG,aAAa,GAAG,MAAM,OAAO,UAAQ,QAAQ,UAAU,KAAK,OAAO,IAAI,UAAU,YAAY,QAAQ,UAAU,KAAK,OAAO,IAAI,UAAU,QAAQ,CAAC;AACrL,WAAO,cAAc,QAAQ,SAAS,OAAO,QAAQ;AAAA,EACvD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,mBAAmB;AACvD,aAAO,KAAK,qBAAqB,WAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,UAAS;AAAA,IACpB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAC9E,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU,OAAO,QAAQ,0BAA0B,OAAO,kBAAkB;AAC1E,WAAO,SAAS,OAAO;AAAA,MACrB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,QAAQ,KAAK;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,UAAU,EAAE,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,KAAK,UAAU,mBAAmB,WAAY;AAC5C,QAAM,iBAAiB,KAAK,kBAAkB;AAC9C,SAAO,IAAI,KAAK,KAAK,QAAQ,IAAI,iBAAiB,GAAK,EAAE,YAAY;AACvE;AACA,IAAM,oBAAN,MAAM,2BAA0B,SAAS;AAAA,EACvC,YAAY,oBAAoB,QAAQ,iBAAiB;AACvD,UAAM,QAAQ,eAAe;AAC7B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO,UAAU,QAAQ;AACjC,UAAM,SAAS,4BAA4B,KAAK,kBAAkB;AAClE,WAAO,MAAM,UAAU,OAAO,QAAQ,UAAU,MAAM;AAAA,EACxD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAkB,oBAAoB,EAAE,GAAM,kBAAkB,WAAW,EAAE,GAAM,kBAAkB,4BAA4B,EAAE,CAAC;AAAA,IAC7L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,uBAAsB,SAAS;AAAA,EACnC,YAAY,oBAAoB,QAAQ,iBAAiB;AACvD,UAAM,QAAQ,eAAe;AAC7B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO,UAAU,QAAQ;AACjC,UAAM,SAAS,mBAAmB,KAAK,kBAAkB;AACzD,WAAO,MAAM,UAAU,OAAO,QAAQ,UAAU,MAAM;AAAA,EACxD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,oBAAoB,EAAE,GAAM,kBAAkB,WAAW,EAAE,GAAM,kBAAkB,4BAA4B,EAAE,CAAC;AAAA,IACzL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,uBAAsB,SAAS;AAAA,EACnC,YAAY,oBAAoB,QAAQ,iBAAiB;AACvD,UAAM,QAAQ,eAAe;AAC7B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,UAAU,OAAO,UAAU,QAAQ;AACjC,UAAM,SAAS,mBAAmB,KAAK,kBAAkB;AACzD,WAAO,MAAM,UAAU,OAAO,QAAQ,UAAU,MAAM;AAAA,EACxD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,oBAAoB,EAAE,GAAM,kBAAkB,WAAW,EAAE,GAAM,kBAAkB,4BAA4B,EAAE,CAAC;AAAA,IACzL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,YAAY,OAAO,YAAY;AAAA,EACtC;AAAA,EACA,UAAU,OAAO;AACf,QAAI,OAAO,UAAU,SAAU,QAAO;AACtC,WAAO,KAAK,UAAU,SAAS,gBAAgB,MAAM,KAAK;AAAA,EAC5D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,oBAAoB,UAAU;AACrC,SAAO,MAAM;AACX,UAAM,eAAe,SAAS,IAAI,mBAAmB;AACrD,UAAMI,YAAW,SAAS,IAAI,QAAQ;AACtC,UAAM,oBAAoB,SAAS,IAAI,mBAAmB;AAC1D,iBAAa,aAAa,EAAE,UAAU,cAAY;AAChD,YAAM,cAAc,mBAAmB,KAAK,QAAQ,QAAQ,QAAQ,EAAE;AACtE,MAAAA,UAAS,SAAS,GAAG,iBAAiB,IAAI,WAAW;AAAA,IACvD,CAAC;AAAA,EACH;AACF;AACA,IAAM,yBAAyB;AAAA,EAC7B,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AACT;AACA,IAAM,WAAN,cAAuB,OAAO;AAAA,EAC5B,YAAY,qBAAqB;AAC/B,UAAM;AACN,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,WAAW;AACT,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,QAAI,aAAa,kBAAkB,WAAW,GAAG;AAC/C,aAAO,iBAAiB,WAAW;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM,CAAC,mBAAmB;AAC5B;AACA,IAAM,uCAAuC;AAAA,EAC3C,SAAS;AAAA,EACT,UAAU;AACZ;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,QAAQ,QAAQ;AAC1B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,SAAK,QAAQ,QAAQ,QAAQ,CAAC;AAAA,MAC5B,OAAO;AAAA,MACP;AAAA,IACF,MAAM;AACJ,YAAM,SAAS,MAAM;AACrB,UAAI,CAAC,OAAQ;AACb,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,OAAO,IAAI,MAAM;AAAA,MACxB,OAAO;AACL,cAAM,gBAAgB,WAAW,CAAC;AAAA,UAChC;AAAA,WACG,OACJ,GAAG;AAAA,UACF,MAAM;AAAA,QACR,CAAC;AACD,aAAK,OAAO,IAAI,aAAa;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,SAAS,aAAa,GAAM,SAAc,QAAQ,CAAC,CAAC;AAAA,IACzG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,WAAW,QAAQ,QAAQ;AAClC,MAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,SAAO,OAAO,OAAO,CAAC,KAAK,UAAU;AACnC,UAGI,sCACC,QADD;AAAA,MAEF,YAAY,OAAO;AAAA,MACnB,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,QAAQ,SAAS,GAAG;AAAA,IAC7D,IANE;AAAA;AAAA,IAptJN,IAstJQ,IADC,oBACD,IADC;AAAA,MADH;AAAA;AAOF,QAAI,KAAK,SAAS,GAAG,WAAW,UAAU,OAAO,CAAC;AAClD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAC1B,EAAAA,iBAAgBA,iBAAgB,SAAS,IAAI,CAAC,IAAI;AAClD,EAAAA,iBAAgBA,iBAAgB,wBAAwB,IAAI,CAAC,IAAI;AACjE,EAAAA,iBAAgBA,iBAAgB,eAAe,IAAI,CAAC,IAAI;AAC1D,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,SAAS,gBAAgB,MAAM,WAAW;AACxC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AACA,SAAS,YAAY,UAAU,CAAC,GAAG;AACjC,SAAO,gBAAgB,gBAAgB,SAAS,CAAC;AAAA,IAC/C,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,MAAM,CAAC,cAAc;AAAA,EACvB,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,QAAQ,aAAa;AAAA,EACjC,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,wBAAwB,QAAQ,aAAa;AAAA,IACvD,MAAM,CAAC,mBAAmB;AAAA,EAC5B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,QAAQ,eAAe;AAAA,EACnC,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,QAAQ,kBAAkB;AAAA,EACtC,CAAC,CAAC;AACJ;AACA,SAAS,kBAAkB,UAAU;AACnC,SAAO,gBAAgB,gBAAgB,eAAe,CAAC;AAAA,IACrD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC;AACJ;AACA,SAAS,uBAAuB,SAAS;AACvC,SAAO,gBAAgB,gBAAgB,wBAAwB,CAAC;AAAA,IAC9D,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC,CAAC;AACJ;AACA,SAAS,kBAAkB,UAAU;AACnC,QAAM,YAAY,CAAC,gBAAgB,wBAAwB;AAAA,IACzD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,QAAQ;AAAA,IACf,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,QAAQ;AAAA,IACf,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,mBAAmB;AAAA,IAC1B,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,2BAA2B;AAAA,IAClC,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAC,aAAa;AAAA,IACpB,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,wBAAwB,sCAAsC;AAAA,IAC/D,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC;AACD,aAAW,WAAW,UAAU;AAC9B,cAAU,KAAK,GAAG,QAAQ,UAAU;AAAA,EACtC;AACA,SAAO,yBAAyB,SAAS;AAC3C;AACA,SAAS,oBAAoB,UAAU,CAAC,GAAG;AACzC,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,wBAAwB,QAAQ,aAAa;AAAA,IACvD,MAAM,CAAC,mBAAmB;AAAA,EAC5B,CAAC,CAAC;AACJ;AACA,IAAM,uBAAuB,CAAC,oBAAoB,6BAA6B,cAAc,qBAAqB,eAAe,qBAAqB,8BAA8B,wBAAwB;AAO5M,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,0BAA0B,wBAAwB,oCAAoC,uBAAuB,UAAU,cAAc,gBAAgB,mBAAmB,eAAe,aAAa;AAAA,MACnN,SAAS,CAAC,cAAc,aAAa,qBAAqB,cAAc,oBAAoB,oBAAoB,6BAA6B,cAAc,qBAAqB,eAAe,qBAAqB,8BAA8B,wBAAwB;AAAA,MAC1Q,SAAS,CAAC,cAAc,aAAa,qBAAqB,cAAc,oBAAoB,0BAA0B,wBAAwB,oCAAoC,uBAAuB,UAAU,cAAc,gBAAgB,mBAAmB,eAAe,eAAe,oBAAoB,6BAA6B,cAAc,qBAAqB,eAAe,qBAAqB,8BAA8B,wBAAwB;AAAA,IACld,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,kBAAkB,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,MACzE,SAAS,CAAC,cAAc,aAAa,qBAAqB,cAAc,oBAAoB,cAAc,aAAa,qBAAqB,cAAc,kBAAkB;AAAA,IAC9K,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,aAAa,qBAAqB,cAAc,oBAAoB,0BAA0B,wBAAwB,oCAAoC,uBAAuB,UAAU,cAAc,gBAAgB,mBAAmB,eAAe,eAAe,GAAG,oBAAoB;AAAA,MACzT,SAAS,CAAC,cAAc,aAAa,qBAAqB,cAAc,oBAAoB,GAAG,oBAAoB;AAAA,MACnH,cAAc,CAAC,0BAA0B,wBAAwB,oCAAoC,uBAAuB,UAAU,cAAc,gBAAgB,mBAAmB,eAAe,aAAa;AAAA,MACnN,WAAW,CAAC,kBAAkB,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IAC3E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,MAC5C,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,kBAAkB,sBAAsB;AAAA,QAClD,YAAY;AAAA,QACZ,YAAY;AAAA,MACd,CAAC,CAAC,CAAC;AAAA,MACH,SAAS,CAAC,gBAAgB,oBAAoB,gBAAgB,kBAAkB;AAAA,IAClF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,MAC5C,SAAS,CAAC,gBAAgB,kBAAkB;AAAA,MAC5C,WAAW,CAAC,kBAAkB,sBAAsB;AAAA,QAClD,YAAY;AAAA,QACZ,YAAY;AAAA,MACd,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIf,OAAO,QAAQ,UAAU,CAAC,GAAG;AAC3B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,eAAe,YAAY,OAAO,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,UAAU,CAAC,GAAG;AAC5B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,oBAAoB,OAAO,CAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc;AAAA,MACxB,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,gBAAgB,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc;AAAA,MACxB,SAAS,CAAC,cAAc;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,IAAI,gBAAgB,SAAS;AAC3B,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,CAAC,QAAS;AACd,YAAQ,OAAO,UAAU,SAAS;AAAA,EACpC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,QAAQ;AAAA,QACN,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,WAAW,IAAI,aAAa;AAAA,EACnC;AAAA,EACA,UAAU,OAAO;AACf,SAAK,SAAS,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,SAAS,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,eAAe,GAAG;AAChB,UAAM,IAAI,OAAO,aAAa,EAAE,KAAK;AACrC,QAAI,EAAE,YAAY,MAAM,KAAK,EAAE,YAAY,MAAM,KAAK,EAAE,YAAY,EAAE,YAAY,MAAM,KAAK,EAAE,YAAY,MAAM,KAAK,EAAE,YAAY,EAAE,oBAAoB,EAAE,iBAAiB,UAAU,GAAG;AACxL,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,kDAAkD,QAAQ;AAC1F,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,GAAG,OAAU,eAAe,EAAE,SAAS,SAAS,gDAAgD,QAAQ;AACtG,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,GAAG,OAAU,eAAe;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;AAAA,IACrC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,oBAAoB,OAAO,iBAAiB;AACjD,SAAK,oBAAoB,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,QAAI;AAAA,MACF;AAAA,IACF,IAAI,MAAM,QAAQ,CAAC;AACnB,QAAI,CAAC,gBAAgB;AACnB,YAAM,aAAa,UAAU,KAAK,eAAe,aAAa,KAAK,QAAQ,MAAM,GAAG,CAAC;AACrF,uBAAiB,YAAY;AAAA,IAC/B;AACA,QAAI,CAAC,eAAgB,QAAO,GAAG,IAAI;AACnC,WAAO,KAAK,kBAAkB,kBAAkB,cAAc,EAAE,KAAK,IAAI,YAAU;AACjF,UAAI,CAAC,UAAU,KAAK,YAAY,iBAAiB;AAC/C,aAAK,kBAAkB,YAAY;AAAA,UACjC,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAkB,CAAC,OAAO,UAAU;AACxC,QAAM,SAAS,OAAO,MAAM;AAC5B,QAAM,gBAAgB,OAAO,aAAa;AAC1C,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,oBAAoB,OAAO,iBAAiB;AAClD,QAAM,oBAAoB,OAAO,wBAAwB;AACzD,MAAI;AAAA,IACF;AAAA,EACF,IAAI,MAAM,QAAQ,CAAC;AACnB,MAAI,CAAC,gBAAgB;AACnB,UAAM,aAAa,UAAU,eAAe,aAAa,QAAQ,MAAM,GAAG,CAAC;AAC3E,qBAAiB,YAAY;AAAA,EAC/B;AACA,MAAI,CAAC,eAAgB,QAAO,GAAG,IAAI;AACnC,SAAO,kBAAkB,kBAAkB,cAAc,EAAE,KAAK,IAAI,YAAU;AAC5E,QAAI,CAAC,UAAU,YAAY,iBAAiB;AAC1C,wBAAkB,YAAY;AAAA,QAC5B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,gBAAgB,CAAC,GAAG;AAC9B,eAAW,OAAO,eAAe;AAC/B,UAAI,aAAa,eAAe,GAAG,GAAG;AACpC,aAAK,GAAG,IAAI,cAAc,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,iBAAN,cAA6B,cAAc;AAAA,EACzC,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,gBAAgB,CAAC,GAAG;AAC9B,eAAW,OAAO,eAAe;AAC/B,UAAI,aAAa,eAAe,GAAG,KAAK,cAAc,GAAG,MAAM,QAAW;AACxE,aAAK,GAAG,IAAI,cAAc,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,sBAAN,cAAkC,iBAAiB;AAAA,EACjD,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,gBAAgB,CAAC,GAAG;AAC9B,SAAK,iBAAiB;AACtB,eAAW,OAAO,eAAe;AAC/B,UAAI,aAAa,eAAe,GAAG,KAAK,cAAc,GAAG,MAAM,QAAW;AACxE,aAAK,GAAG,IAAI,cAAc,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,oCAAN,cAAgD,oBAAoB;AAAA,EAClE,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AACnB,SAAK,iBAAiB;AAAA,EACxB;AACF;AACA,IAAM,wBAAN,cAAoC,wBAAwB;AAAA,EAC1D,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,kCAAN,cAA8C,kCAAkC;AAAA,EAC9E,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,iCAAN,cAA6C,sBAAsB;AAAA,EACjE,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,2CAAN,cAAuD,gCAAgC;AAAA,EACrF,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,gBAAgB,CAAC,GAAG;AAC9B,eAAW,OAAO,eAAe;AAC/B,UAAI,aAAa,eAAe,GAAG,GAAG;AACpC,aAAK,GAAG,IAAI,cAAc,GAAG;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,2BAAN,cAAuC,UAAU;AAAA,EAC/C,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,mCAAN,cAA+C,yBAAyB;AAAA,EACtE,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,mBAAN,cAA+B,yBAAyB;AAAA,EACtD,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AAEA,IAAM,2BAAN,cAAuC,iBAAiB;AAAA,EACtD,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,uBAAN,cAAmC,iBAAiB;AAAA,EAClD,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AAEA,IAAM,+BAAN,cAA2C,qBAAqB;AAAA,EAC9D,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,qCAAN,cAAiD,oBAAoB;AAAA,EACnE,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,6BAAN,cAAyC,mCAAmC;AAAA,EAC1E,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,qCAAN,cAAiD,2BAA2B;AAAA,EAC1E,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,6CAAN,cAAyD,mCAAmC;AAAA,EAC1F,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,iCAAN,cAA6C,2BAA2B;AAAA,EACtE,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,yCAAN,cAAqD,+BAA+B;AAAA,EAClF,YAAY,gBAAgB,CAAC,GAAG;AAC9B,UAAM,aAAa;AAAA,EACrB;AACF;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,mBAAN,cAA+B,UAAU;AAAA,EACvC,YAAY,MAAM,MAAM;AACtB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,gBAAN,cAA4B,UAAU;AAAA,EACpC,YAAY,MAAM,MAAM;AACtB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,iBAAN,cAA6B,UAAU;AAAA,EACrC,YAAY,MAAM,QAAQ,QAAQ;AAChC,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC5D,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN,cAAc,MAAM;AAAA,MACtB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,SAAS,WAAW,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AACb,CAAC;AACD,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,cAAc;AACxB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,EAAC;AACb;AACA,IAAM,yBAAN,cAAqC,kBAAkB;AAAA,EACrD,WAAW;AACT,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,SAAK,aAAa,MAAM;AAAA,EAC1B;AACF;AACA,IAAM,8BAAN,cAA0C,kBAAkB;AAAA,EAC1D,YAAY,cAAcL,QAAO;AAC/B,UAAM,YAAY;AAClB,SAAK,QAAQA;AAAA,EACf;AAAA,EACA,WAAW;AACT,WAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,aAAa,MAAM;AAAA,EACnE;AACF;AACA,IAAM,qBAAqB;AAAA,EACzB,MAAM,cAAc;AAClB,WAAO,IAAI,uBAAuB,YAAY;AAAA,EAChD;AAAA,EACA,OAAO,cAAc;AACnB,WAAO,IAAI,4BAA4B,cAAc,aAAa,MAAM;AAAA,EAC1E;AAAA,EACA,QAAQ,cAAc;AACpB,WAAO,IAAI,4BAA4B,cAAc,CAAC;AAAA,EACxD;AAAA,EACA,OAAO,cAAcA,QAAO;AAC1B,WAAO,IAAI,4BAA4B,cAAcA,MAAK;AAAA,EAC5D;AACF;AACA,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,+BAAN,cAA2C,wBAAwB;AAAA,EACjE,YAAY,OAAO;AACjB,UAAM,KAAK;AAAA,EACb;AAAA,EACA,SAAS,SAAS;AAChB,QAAI,KAAK,OAAO;AACd,cAAQ,aAAa,SAAS,KAAK,KAAK;AAAA,IAC1C;AAAA,EACF;AACF;AACA,IAAM,4BAAN,cAAwC,wBAAwB;AAAA,EAC9D,cAAc;AACZ,UAAM;AAAA,EACR;AAAA,EACA,SAAS,GAAG;AAAA,EAAC;AACf;AACA,IAAM,4BAA4B;AAAA,EAChC,MAAM,OAAO;AACX,WAAO,IAAI,6BAA6B,KAAK;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,WAAO,IAAI,0BAA0B;AAAA,EACvC;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,SAAS,cAAc,aAAa,aAAa,GAAG,0BAA0B,0BAA0B,KAAK,GAAG,UAAU,CAAC,GAAG;AACxI,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,0BAA0B;AAC/B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,gBAAgB;AACd,UAAM,UAAU,KAAK,cAAc;AACnC,QAAI,KAAK,WAAW,OAAO,KAAK,KAAK,OAAO,EAAE,SAAS,GAAG;AACxD,aAAO,KAAK,KAAK,OAAO,EAAE,QAAQ,SAAO;AACvC,YAAI,KAAK,QAAQ,GAAG,GAAG;AACrB,kBAAQ,GAAG,IAAI,KAAK,QAAQ,GAAG;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,wBAAwB,SAAS,OAAO;AAC7C,SAAK,YAAY,cAAc,OAAO;AACtC,WAAO;AAAA,EACT;AACF;AACA,IAAM,uBAAN,cAAmC,gBAAgB;AAAA,EACjD,gBAAgB;AACd,UAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,YAAQ,cAAc,KAAK;AAC3B,WAAO;AAAA,EACT;AACF;AACA,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EAClD,gBAAgB;AACd,UAAM,UAAU,SAAS,cAAc,QAAQ;AAC/C,YAAQ,cAAc,KAAK;AAC3B,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,mBAAmB,SAAS,SAAS;AACnC,WAAO,IAAI,sBAAsB,SAAS,aAAa,aAAa,GAAG,QAAW,OAAO;AAAA,EAC3F;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,IAAI,sBAAsB,SAAS,aAAa,aAAa,GAAG,QAAW,OAAO;AAAA,EAC3F;AAAA,EACA,kBAAkB,SAAS,SAAS;AAClC,WAAO,IAAI,qBAAqB,SAAS,aAAa,aAAa,GAAG,QAAW,OAAO;AAAA,EAC1F;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,IAAI,qBAAqB,SAAS,aAAa,cAAc,GAAG,QAAW,OAAO;AAAA,EAC3F;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,WAAW,cAAc;AACvB,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,oBAAN,cAAgC,gBAAgB;AAAA,EAC9C,cAAc;AACZ,UAAM,MAAS;AAAA,EACjB;AACF;AACA,IAAM,2BAAN,cAAuC,gBAAgB;AAAA,EACrD,WAAW,cAAc;AACvB,WAAO,KAAK,KAAK,OAAO,EAAE,QAAQ,SAAO,aAAa,SAAS,GAAG,IAAI,KAAK,QAAQ,GAAG,CAAC;AACvF,iBAAa,kBAAkB,cAAc;AAC7C,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EACpD,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,OAAO;AACL,WAAO,IAAI,kBAAkB;AAAA,EAC/B;AAAA,EACA,UAAU,SAAS;AACjB,WAAO,IAAI,yBAAyB,OAAO;AAAA,EAC7C;AAAA,EACA,SAAS,SAAS;AAChB,WAAO,IAAI,wBAAwB,OAAO;AAAA,EAC5C;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,MAAM,cAAc,aAAa,aAAa,GAAG,sBAAsB,sBAAsB,UAAU,GAAG;AACpH,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,eAAe;AACb,SAAK,UAAU,KAAK,cAAc;AAClC,WAAO,GAAG,IAAI,EAAE,KAAK,UAAU,MAAM,aAAa,KAAK,SAAS,KAAK,aAAa,KAAK,mBAAmB,CAAC,CAAC;AAAA,EAC9G;AACF;AACA,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EAClD,YAAY,KAAK,aAAa,qBAAqB;AACjD,UAAM,KAAK,aAAa,mBAAmB;AAAA,EAC7C;AAAA,EACA,gBAAgB;AACd,UAAM,UAAU,SAAS,cAAc,QAAQ;AAC/C,YAAQ,MAAM,KAAK;AACnB,WAAO;AAAA,EACT;AACF;AACA,IAAM,uBAAN,cAAmC,gBAAgB;AAAA,EACjD,YAAY,MAAM,aAAa,qBAAqB;AAClD,UAAM,MAAM,aAAa,mBAAmB;AAAA,EAC9C;AAAA,EACA,gBAAgB;AACd,UAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,YAAQ,MAAM;AACd,YAAQ,OAAO,KAAK;AACpB,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAmB;AAAA,EACvB,mBAAmB,KAAK;AACtB,WAAO,IAAI,sBAAsB,KAAK,aAAa,aAAa,GAAG,sBAAsB,KAAK,CAAC;AAAA,EACjG;AAAA,EACA,4BAA4B,KAAK,WAAW;AAC1C,WAAO,IAAI,sBAAsB,KAAK,aAAa,aAAa,GAAG,sBAAsB,UAAU,SAAS,CAAC;AAAA,EAC/G;AAAA,EACA,4BAA4B,KAAK,WAAW;AAC1C,WAAO,IAAI,sBAAsB,KAAK,aAAa,aAAa,GAAG,sBAAsB,UAAU,SAAS,CAAC;AAAA,EAC/G;AAAA,EACA,2BAA2B,KAAK,WAAW;AACzC,WAAO,IAAI,qBAAqB,KAAK,aAAa,aAAa,GAAG,sBAAsB,UAAU,SAAS,CAAC;AAAA,EAC9G;AAAA,EACA,6BAA6B,KAAK,WAAW;AAC3C,WAAO,IAAI,sBAAsB,KAAK,aAAa,cAAc,GAAG,sBAAsB,UAAU,SAAS,CAAC;AAAA,EAChH;AAAA,EACA,4BAA4B,KAAK,WAAW;AAC1C,WAAO,IAAI,qBAAqB,KAAK,aAAa,cAAc,GAAG,sBAAsB,UAAU,SAAS,CAAC;AAAA,EAC/G;AACF;AACA,IAAM,qBAAN,MAAyB;AAAA,EACvB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,8BAAN,cAA0C,mBAAmB;AAAA,EAC3D,YAAY,WAAW,mBAAmB,kBAAkB,iBAAiB,KAAK,GAAG;AACnF,UAAM,SAAS;AACf,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,cAAc,UAAU;AACtB,SAAK,kBAAkB,QAAQ;AAC/B,UAAM,WAAW,SAAS,IAAI,0BAAwB;AACtD,UAAM,UAAU,SAAS,wBAAwB,KAAK,OAAO;AAC7D,UAAM,eAAe,KAAK,kBAAkB,aAAa,gBAAgB,SAAS,KAAK,kBAAkB,SAAS,GAAG,QAAQ;AAC7H,SAAK,gBAAgB,WAAW,YAAY;AAC5C,WAAO;AAAA,EACT;AACF;AACA,IAAM,kCAAN,cAA8C,mBAAmB;AAAA,EAC/D,YAAY,WAAW,kBAAkB,iBAAiB,KAAK,GAAG,cAAc,aAAa,aAAa,GAAG;AAC3G,UAAM,SAAS;AACf,SAAK,kBAAkB;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc,UAAU;AACtB,UAAM,SAAS,SAAS,IAAI,cAAc;AAC1C,UAAM,WAAW,SAAS,IAAI,0BAAwB;AACtD,UAAM,eAAe,SAAS,wBAAwB,KAAK,OAAO,EAAE,OAAO,QAAQ;AACnF,SAAK,gBAAgB,WAAW,YAAY;AAC5C,WAAO,WAAW,aAAa,QAAQ;AACvC,UAAM,UAAU,aAAa,SAAS,UAAU,CAAC;AACjD,SAAK,YAAY,cAAc,OAAO;AACtC,WAAO;AAAA,EACT;AACF;AACA,IAAM,6BAAN,cAAyC,mBAAmB;AAAA,EAC1D,YAAY,aAAa,mBAAmB,kBAAkB,iBAAiB,KAAK,GAAG;AACrF,UAAM,WAAW;AACjB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,gBAAgB;AACd,SAAK,kBAAkB,QAAQ;AAC/B,UAAM,kBAAkB,KAAK,kBAAkB,aAAa,mBAAmB,KAAK,SAAS,KAAK,gBAAgB,SAAS,KAAK,kBAAkB,SAAS,CAAC;AAC5J,oBAAgB,cAAc;AAC9B,WAAO;AAAA,EACT;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,sBAAsB,WAAW,SAAS;AACxC,WAAO,IAAI,gCAAgC,WAAW,WAAW,iBAAiB,UAAU,OAAO,CAAC;AAAA,EACtG;AAAA,EACA,2BAA2B,WAAW,cAAc,SAAS;AAC3D,WAAO,IAAI,4BAA4B,WAAW,mBAAmB,OAAO,YAAY,GAAG,WAAW,iBAAiB,UAAU,OAAO,CAAC;AAAA,EAC3I;AAAA,EACA,0BAA0B,aAAa,cAAc,SAAS;AAC5D,WAAO,IAAI,2BAA2B,aAAa,mBAAmB,OAAO,YAAY,GAAG,WAAW,iBAAiB,SAAS,OAAO,CAAC;AAAA,EAC3I;AAAA,EACA,4BAA4B,WAAW,cAAc,SAAS;AAC5D,WAAO,IAAI,4BAA4B,WAAW,mBAAmB,QAAQ,YAAY,GAAG,WAAW,iBAAiB,UAAU,OAAO,CAAC;AAAA,EAC5I;AAAA,EACA,2BAA2B,aAAa,cAAc,SAAS;AAC7D,WAAO,IAAI,2BAA2B,aAAa,mBAAmB,QAAQ,YAAY,GAAG,WAAW,iBAAiB,SAAS,OAAO,CAAC;AAAA,EAC5I;AAAA,EACA,4BAA4B,WAAW,cAAc,SAAS;AAC5D,WAAO,IAAI,4BAA4B,WAAW,mBAAmB,MAAM,YAAY,GAAG,WAAW,iBAAiB,UAAU,OAAO,CAAC;AAAA,EAC1I;AAAA,EACA,2BAA2B,aAAa,cAAc,SAAS;AAC7D,WAAO,IAAI,2BAA2B,aAAa,mBAAmB,MAAM,YAAY,GAAG,WAAW,iBAAiB,SAAS,OAAO,CAAC;AAAA,EAC1I;AACF;AACA,SAAS,eAAe;AAAA,EACtB,MAAM;AACR,IAAI,CAAC,GAAG;AACN,SAAO,aAAW;AAChB,QAAI,CAAC,IAAI,MAAM,MAAS,EAAE,QAAQ,QAAQ,KAAK,IAAI,GAAI,QAAO;AAC9D,WAAO,cAAc,QAAQ,OAAO,GAAG,IAAI,OAAO;AAAA,MAChD,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc,OAAO,QAAQ;AACpC,QAAM,OAAO,oBAAI,KAAK;AACtB,OAAK,YAAY,KAAK,YAAY,IAAI,MAAM;AAC5C,OAAK,SAAS,IAAI,IAAI,IAAI,GAAG;AAC7B,SAAO,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,QAAQ;AACjD;AACA,SAAS,qBAAqB;AAC5B,SAAO,aAAW;AAChB,QAAI,CAAC,IAAI,MAAM,MAAS,EAAE,QAAQ,QAAQ,KAAK,IAAI,GAAI,QAAO;AAC9D,WAAO,kBAAkB,OAAO,QAAQ,KAAK,CAAC,IAAI,OAAO;AAAA,MACvD,YAAY;AAAA,IACd;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,UAAQ,MAAM,QAAQ,SAAS,EAAE;AACjC,MAAI,CAAC,iBAAiB,KAAK,KAAK,EAAG,QAAO;AAC1C,MAAI,WAAW;AACf,MAAI,aAAa;AACjB,WAAS,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AACrC,UAAM,QAAQ,OAAO,MAAM,IAAI,CAAC,CAAC,IAAI;AACrC,gBAAY,QAAQ,KAAK,CAAC,EAAE,QAAQ;AACpC,iBAAa,aAAa,IAAI;AAAA,EAChC;AACA,SAAO,WAAW,OAAO;AAC3B;AACA,SAAS,cAAc;AAAA,EACrB,UAAU;AAAA,EACV,UAAU;AACZ,IAAI,CAAC,GAAG;AACN,SAAO,aAAW;AAChB,QAAI,CAAC,IAAI,MAAM,MAAS,EAAE,QAAQ,QAAQ,KAAK,IAAI,GAAI,QAAO;AAC9D,UAAM,QAAQ,OAAO,QAAQ,KAAK;AAClC,WAAO,YAAY,OAAO,SAAS,OAAO,KAAK,YAAY,OAAO,SAAS,OAAO;AAAA,EACpF;AACF;AACA,SAAS,YAAY,OAAO,KAAK,KAAK;AACpC,SAAO,QAAQ,MAAM;AAAA,IACnB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI;AACN;AACA,SAAS,YAAY,OAAO,KAAK,KAAK;AACpC,SAAO,QAAQ,MAAM;AAAA,IACnB,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF,IAAI;AACN;AACA,SAAS,iBAAiB;AAAA,EACxB;AACF,IAAI,CAAC,GAAG;AAEN,QAAM,WAAW,aAAW;AAC1B,WAAO,gBAAgB,QAAQ,OAAO,iBAAiB,IAAI,OAAO;AAAA,MAChE,UAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO,mBAAmB;AACjD,MAAI,SAAS,UAAU,KAAK,UAAU,MAAO,QAAO;AACpD,MAAI,qBAAqB,UAAU,GAAI,QAAO;AAC9C,SAAO;AACT;AACA,SAAS,qBAAqB;AAAA,EAC5B,gBAAgB;AAAA,EAChB,gBAAgB;AAClB,IAAI,CAAC,GAAG;AACN,SAAO,aAAW;AAChB,QAAI,CAAC,IAAI,MAAM,MAAS,EAAE,QAAQ,QAAQ,KAAK,IAAI,GAAI,QAAO;AAC9D,UAAM,QAAQ,OAAO,QAAQ,KAAK;AAClC,WAAO,kBAAkB,OAAO,aAAa,KAAK,kBAAkB,OAAO,aAAa;AAAA,EAC1F;AACF;AACA,SAAS,kBAAkB,OAAO,gBAAgB;AAChD,SAAO,MAAM,SAAS,iBAAiB;AAAA,IACrC,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF,IAAI;AACN;AACA,SAAS,kBAAkB,OAAO,gBAAgB;AAChD,SAAO,MAAM,SAAS,iBAAiB;AAAA,IACrC,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF,IAAI;AACN;AACA,SAAS,0BAA0B;AACjC,SAAO,aAAW;AAChB,QAAI,cAAc,QAAQ,KAAK,EAAG,QAAO;AACzC,WAAO,kBAAkB,QAAQ,KAAK,IAAI,OAAO;AAAA,MAC/C,iBAAiB;AAAA,IACnB;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,QAAM,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE,CAAC;AACnC,SAAO,IAAI,QAAQ,MAAM;AAC3B;AACA,SAAS,cAAc;AACrB,SAAO,aAAW;AAChB,QAAI,cAAc,QAAQ,KAAK,EAAG,QAAO;AACzC,WAAO,WAAW,QAAQ,KAAK,IAAI,OAAO;AAAA,MACxC,KAAK;AAAA,IACP;AAAA,EACF;AACF;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,qBAAqB,KAAK,KAAK,KAAK,gBAAgB,KAAK,KAAK,GAAG;AACnE,UAAM,IAAI,SAAS,cAAc,GAAG;AACpC,MAAE,OAAO;AACT,WAAO,CAAC,CAAC,EAAE;AAAA,EACb;AACA,SAAO;AACT;AACA,IAAM,2BAA2B;AACjC,SAAS,iBAAiB;AAAA,EACxB,UAAU;AACZ,IAAI;AAAA,EACF,SAAS;AACX,GAAG;AACD,SAAO,aAAW;AAChB,UAAM,UAAU,gBAAgB,QAAQ,OAAO,OAAO;AACtD,WAAO,UAAU,OAAO;AAAA,MACtB,iBAAiB;AAAA,QACf,aAAa,QAAQ;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,OAAO,SAAS;AACvC,MAAI,cAAc,KAAK,EAAG,QAAO;AACjC,SAAO,QAAQ,KAAK,KAAK;AAC3B;AACA,IAAM,gBAAgB;AAAA,EACpB,YAAY;AAAA,EACZ,cAAc,MAAM,WAAW;AAAA,EAC/B,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,UAAU;AAAA,EACV,cAAc;AAAA,EACd,KAAK;AAAA,EACL,UAAU;AAAA,EACV,iBAAiB;AACnB;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,qBAAqB,iBAAiB;AACpC,WAAO,mBAAmB,IAAI,YAAY;AAAA,EAC5C;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,SAAK,gBAAgB,WAAW,OAAO;AACvC,WAAO,KAAK,OAAO,OAAO,EAAE,KAAK,SAAS,MAAM,KAAK,gBAAgB,cAAc,OAAO,CAAC,CAAC;AAAA,EAC9F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,SAAS,eAAe,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["filter", "index", "map", "delay", "resource", "document", "CoreFeatureKind"]}