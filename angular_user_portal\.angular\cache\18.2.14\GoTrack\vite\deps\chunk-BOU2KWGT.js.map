{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.feature-management/fesm2022/abp-ng.feature-management-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nclass FeaturesService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'AbpFeatureManagement';\n    this.delete = (providerName, providerKey) => this.restService.request({\n      method: 'DELETE',\n      url: '/api/feature-management/features',\n      params: {\n        providerName,\n        providerKey\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.get = (providerName, providerKey) => this.restService.request({\n      method: 'GET',\n      url: '/api/feature-management/features',\n      params: {\n        providerName,\n        providerKey\n      }\n    }, {\n      apiName: this.apiName\n    });\n    this.update = (providerName, providerKey, input) => this.restService.request({\n      method: 'PUT',\n      url: '/api/feature-management/features',\n      params: {\n        providerName,\n        providerKey\n      },\n      body: input\n    }, {\n      apiName: this.apiName\n    });\n  }\n  static {\n    this.ɵfac = function FeaturesService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FeaturesService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FeaturesService,\n      factory: FeaturesService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FeaturesService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FeaturesService, index as StringValues };\n"], "mappings": ";;;;;;;;;;;AAGA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS,CAAC,cAAc,gBAAgB,KAAK,YAAY,QAAQ;AAAA,MACpE,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,MAAM,CAAC,cAAc,gBAAgB,KAAK,YAAY,QAAQ;AAAA,MACjE,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,SAAK,SAAS,CAAC,cAAc,aAAa,UAAU,KAAK,YAAY,QAAQ;AAAA,MAC3E,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAY,WAAW,CAAC;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AACb,CAAC;", "names": []}