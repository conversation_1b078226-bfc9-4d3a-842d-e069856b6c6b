<div class="bg-white rounded-2xl shadow-lg p-6 text-center w-full mx-auto">
  <app-profile-image (userloaded)="profile.set($event)" [reload]="reload()" />

  <!-- optionList -->
  <ul class="mt-4 space-y-2 text-right text-gray-700 text-sm">
    <li class="flex items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded">
      <a [routerLink]="['/track-accounts']" class="flex items-center gap-3"
        ><mat-icon>person_outline</mat-icon>
        <div>{{ 'UserPortal:change account' | i18n }}</div></a
      >
    </li>
    <li class="flex items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded">
      <a [routerLink]="['/subscription-requests']" class="flex items-center gap-3"
        ><mat-icon>library_books</mat-icon>
        <div>{{ 'UserPortal:my requests' | i18n }}</div></a
      >
    </li>
    <li class="flex items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded">
      <a (click)="openEmailVerify()" class="flex items-center gap-3"
        ><mat-icon>check_circle</mat-icon>
        <div>{{ 'UserPortal:virify email' | i18n }}</div></a
      >
    </li>
    <li class="flex items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded">
      <a (click)="deleteAccount()" class="flex items-center gap-3"
        ><mat-icon>cancel</mat-icon>
        <div>{{ 'UserPortal:DeleteAccount' | i18n }}</div></a
      >
    </li>
  </ul>
</div>
