import { Component, inject, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import {
  GetMobileUserProfileDto,
  MobileIdentityUserService,
} from '@proxy/mobile/mobile-identity-users';

import { ProfileImageComponent } from '@shared/components/profile-image/profile-image.component';
import { openVerifyEmailDialog } from '@shared/components/verify-email-dialog/verify-email-dialog.component';
import { logout } from '@shared/functions/logout';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { from, map, switchMap } from 'rxjs';

@Component({
  selector: 'app-accounts-profile',
  standalone: true,
  templateUrl: './accounts-profile.component.html',
  imports: [LanguagePipe, MatIcon, RouterLink, ProfileImageComponent],
})
export class AccountsProfileComponent {
  profile = signal<GetMobileUserProfileDto | undefined>(undefined);
  dialog = inject(MatDialog);
  reload = signal(false);

  mobileIdentityUserService = inject(MobileIdentityUserService);

  ngOnInit(): void {}

  openEmailVerify() {
    openVerifyEmailDialog(this.dialog, this.profile()).subscribe(v => {
      if (v) {
        this.reload.set(!this.reload());
      }
    });
  }

  deleteAccount() {
    this.mobileIdentityUserService
      .delete()
      .pipe(
        switchMap(() => {
          return from(logout());
        })
      )
      .subscribe();
  }
}
