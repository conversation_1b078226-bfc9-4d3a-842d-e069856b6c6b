import { Component, inject } from '@angular/core';

import { DynamicTabsComponent } from '@shared/components/dynamic-tabs/dynamic-tabs.component';
import { GeozoneComponent, ScheduledAlarmsComponent } from './components';
import { FEATURES } from '@shared/constants/features-token';

@Component({
  selector: 'app-alerts',
  standalone: true,
  templateUrl: `./alerts.component.html`,
  imports: [DynamicTabsComponent],
})
export class AlertsComponent {
  description: string = 'UserPortal:AlertsPageDescription';
  activeTabIndex: number = 0;
  features = inject(FEATURES);
  tabs = [];
  ngOnInit(): void {
    if (this.features()['GoTrack.GeographicAreaManagement']) {
      this.tabs.push({
        label: 'UserPortal:GeographicAreasTab',
        iconActive: '/assets/images/svg/active-geo-area.svg',
        iconInactive: '/assets/images/svg/inactive-geo-area.svg',
        component: GeozoneComponent,
      });
    }

    const alerts = [
      'GoTrack.Alerts.ExceedingSpeedAlert',
      'GoTrack.Alerts.JobTimeAlert',
      'GoTrack.Alerts.ExitingRouteAlert',
      'GoTrack.Alerts.EnteringZoneAlert',
      'GoTrack.Alerts.ExitingZoneAlert',
    ];
    if (
      alerts.find(v => {
        return this.features()[v];
      })
    ) {
      this.tabs.push({
        label: 'UserPortal:AlertsTab',
        iconActive: '/assets/images/svg/active-alert.svg',
        iconInactive: '/assets/images/svg/inactive-alert.svg',
        component: ScheduledAlarmsComponent,
      });
    }
  }
}
