import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router, RouterLink } from '@angular/router';
import * as polyline from '@mapbox/polyline';
import { GeoZoneService } from '@proxy/mobile/geo-zones';
import { changeNodeDto, MapComponent } from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { circleToPolygon } from '@shared/functions/map-helper';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-geo-zone-create',
  standalone: true,
  templateUrl: './geo-zone-create.component.html',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    RouterLink,
    MapComponent,
    LanguagePipe,
    ValidationComponent,
  ],
})
export class GeoZoneCreateComponent implements OnInit {
  draw = signal<any>({ polygon: true, circle: true });
  shawDraw = signal(false);

  private fb = inject(FormBuilder);
  private geoZoneService = inject(GeoZoneService);
  private router = inject(Router);

  form: FormGroup = this.fb.group({
    name: this.fb.control('', [Validators.required]),
    polyLine: [[]],
  });
  nodes$ = signal<any[]>([]);

  ngOnInit(): void {}

  showLine() {
    this.shawDraw.set(true);
  }
  onNodeChange(event: changeNodeDto) {
    const layer = event.event.layer;

    if (event.event.layerType == 'circle') {
      const center = (layer as any)._latlng;
      const r = (layer as any)._mRadius;
      const circle = circleToPolygon(center, r, 60);
      this.nodes$.set([...circle, circle[0]]);
    } else if (event.event.layerType == 'polygon') {
      const r = (layer as any)._latlngs;
      const temp = r[0].map(latLng => {
        const { lat, lng } = latLng;
        return [lat, lng];
      });

      this.nodes$.set([...temp, temp[0]]);
    }
    this.form.controls['polyLine'].setValue(this.nodes$());
  }

  save() {
    const obj = { ...this.form.value };
    obj.polyLine = { line: polyline.encode(this.nodes$()) };
    this.geoZoneService.create(obj).subscribe(res => {
      this.router.navigate(['/main/alerts']);
    });
  }
}
