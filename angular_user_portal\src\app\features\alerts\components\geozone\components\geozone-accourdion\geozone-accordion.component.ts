import { AsyncPipe } from '@angular/common';
import { Component, Signal, inject, input, output, signal } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { GeoZoneDto, GeoZoneService } from '@proxy/mobile/geo-zones';
import { getRandomHexColor } from '@shared';
import { MapComponent } from '@shared/components/map/map.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { Layer, MapOptions, Polygon, latLng } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
import * as polyline from '@mapbox/polyline';

@Component({
  selector: 'app-geozone-accordion',
  standalone: true,
  template: ` <mat-expansion-panel (opened)="getInfo(geoZone().id)">
    <mat-expansion-panel-header>
      <mat-panel-title class="flex" (click)="$event.stopPropagation(); $event.preventDefault()">
        <!-- <button class="e" mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item (click)="Delete(geoZone())">
            <mat-icon>delete</mat-icon>
            <span>{{ 'remove' | i18n }}</span>
          </button>
        </mat-menu> -->
        {{ geoZone().name }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="h-48 w-full">
      <app-map
        [line]="(opendgeoZones$() | async)[geoZone().id]"
        [options]="options()[geoZone().id] ? options()[geoZone().id]() : {}"
      />
    </div>
  </mat-expansion-panel>`,
  imports: [
    MatExpansionModule,
    MatDialogModule,
    MapComponent,
    AsyncPipe,
    MatDialogModule,
    MatMenuModule,
  ],
})
export class GeozoneAccordionComponent {
  private geoZoneService = inject(GeoZoneService);
  options = signal<{ [key: string]: Signal<MapOptions> }>({});

  geoZone = input<GeoZoneDto>();
  opendgeoZones$ = input<BehaviorSubject<{ [key: string]: Layer[] }>>();
  getData = output();

  options$ = signal<MapOptions>({});
  getInfo(id: string) {
    this.geoZoneService.get(id).subscribe(val => {
      const r = polyline.decode(val.polyLine.line);
      const geo = new Polygon(r, { color: getRandomHexColor() });
      this.opendgeoZones$().next({
        ...this.opendgeoZones$().value,
        [id]: [geo],
      });
      this.options.update(options => {
        return { ...options, [id]: signal({ zoom: 12, center: latLng(r[0][0], r[0][1]) }) };
      });
    });
  }

  Delete(route) {
    //
  }
}
