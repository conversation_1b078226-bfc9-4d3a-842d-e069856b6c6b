import { Component, inject, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatMenuModule } from '@angular/material/menu';
import { Router, RouterLink } from '@angular/router';
import { GeoZoneDto, GeoZoneService } from '@proxy/mobile/geo-zones';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { Layer } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
import { GeozoneAccordionComponent } from './components/geozone-accourdion/geozone-accordion.component';

@Component({
  selector: 'app-geozone',
  standalone: true,
  templateUrl: `./geozone.component.html`,
  imports: [
    MatExpansionModule,
    MatDialogModule,
    MatMenuModule,
    GeozoneAccordionComponent,
    LanguagePipe,
    RouterLink,
    MatButton,
  ],
})
export class GeozoneComponent {
  private geoZoneService = inject(GeoZoneService);
  geoZones$ = signal<GeoZoneDto[]>([]);
  opendgeoZones$: BehaviorSubject<{ [key: string]: Layer[] }> = new BehaviorSubject({});

  ngOnInit(): void {
    this.getGeoZones();
  }
  getGeoZones() {
    this.geoZoneService.getList({ maxResultCount: 1000, skipCount: 0 }).subscribe(val => {
      this.geoZones$.set(val.items);
    });
  }
}
