<div class="flex flex-col justify-center items-center min-h-screen scale-90">
  <mat-card
    class="p-6 rounded-lg shadow-lg transition-all duration-500 card-blue"
    [ngClass]="{
      'w-10/12 max-w-md': !alertForm.get('alertType').value || step() !== 1,
      'w-full max-w-5xl': alertForm.get('alertType').value && step() === 1
    }"
  >
    <h2 class="main-header">
      {{ (step() === 1 ? 'UserPortal:BasicInfo' : 'UserPortal:NotificationData') | i18n }}
    </h2>

    <form [formGroup]="alertForm" class="p-4">
      @if (step() === 1) {
      <div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <div class="main-fields">
            <mat-label class="pr-2">{{ 'UserPortal:SelectAlertType' | i18n }}</mat-label>
            <!-- اختيار نوع الإنذار -->
            <mat-form-field appearance="outline" class="w-full">
              <mat-select formControlName="alertType" (selectionChange)="onAlertTypeChange()">
                @for (type of alertTypes; track type) { @if (features()[type.feature]) {
                <mat-option [value]="type.key"> {{ type.value | i18n }}</mat-option>
                } }
              </mat-select>
            </mat-form-field>
            @if(features()['GoTrack.GroupManagement']){
            <div class="flex flex-wrap justify-between items-center">
              <mat-label class="pr-2">{{ 'UserPortal:BasedOnGroups' | i18n }}</mat-label>
              <mat-radio-group
                [ngModel]="withGroups()"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="withGroups.set($event)"
              >
                <mat-radio-button class="example-radio-button" [value]="false">
                  {{ 'UserPortal:No' | i18n }}
                </mat-radio-button>
                <mat-radio-button class="example-radio-button" [value]="true">
                  {{ 'UserPortal:Yes' | i18n }}
                </mat-radio-button>
              </mat-radio-group>
            </div>
            }
            <div class="flex justify-between items-center">
              <mat-label class="pr-2">{{ 'UserPortal:SelectVehicles' | i18n }}</mat-label>
              <mat-checkbox
                (change)="toggleSelectAll('vehicleIds', availableVehicles$())"
                [checked]="alertForm.controls.vehicleIds.value?.length == availableVehicles$().size"
                labelPosition="before"
              >
                {{ 'UserPortal:AllVehicles' | i18n }}
              </mat-checkbox>
            </div>
            <mat-form-field appearance="outline" class="w-full">
              <mat-select
                [formControl]="alertForm.controls.vehicleIds"
                multiple
                placeholder="{{ 'UserPortal:SelectVehicles' | i18n }}"
              >
                <mat-select-trigger>
                  @if ((alertForm.get('vehicleIds')?.value?.length || 0) > 0) {
                  <mat-chip-set>
                    @for (vehicleId of alertForm.get('vehicleIds')?.value; track vehicleId) {
                    <mat-chip [removable]="true" (removed)="removeChips(vehicleId, 'vehicleIds')">
                      {{ availableVehicles$().get(vehicleId).name }}
                      <mat-icon matChipRemove>cancel</mat-icon>
                    </mat-chip>
                    }
                  </mat-chip-set>
                  } @else {
                  <span class="text-gray-500">{{ 'UserPortal:SelectVehicles' | i18n }}</span>
                  }
                </mat-select-trigger>
                @for (vehicle of availableVehicles$().values(); track vehicle) {
                <mat-option [value]="vehicle.id">
                  {{ vehicle.name }}
                </mat-option>
                }
              </mat-select>
            </mat-form-field>
            <!-- اختيار المجموعات (يظهر فقط إذا كان يعتمد على المجموعات) -->
            @if (withGroups()&&features()['GoTrack.GroupManagement']) {
            <div class="flex justify-between items-center">
              <mat-label class="pr-2">{{ 'UserPortal:SelectGroup' | i18n }}</mat-label>
              <mat-checkbox
                (change)="toggleSelectAll('vehicleGroupIds', availableGroups$())"
                [checked]="
                  alertForm.get('vehicleGroupIds')?.value?.length == availableGroups$().size
                "
                labelPosition="before"
              >
                {{ 'UserPortal:AllGroups' | i18n }}
              </mat-checkbox>
            </div>
            <mat-form-field appearance="outline" class="w-full">
              <mat-select
                [formControl]="alertForm.controls.vehicleGroupIds"
                multiple
                placeholder="{{ 'UserPortal:SelectGroups' | i18n }}"
              >
                <mat-select-trigger>
                  @if ((alertForm.get('vehicleGroupIds')?.value?.length || 0) > 0) {
                  <mat-chip-set>
                    @for (groupId of alertForm.get('vehicleGroupIds')?.value; track groupId) {
                    <mat-chip
                      [removable]="true"
                      (removed)="removeChips(groupId, 'vehicleGroupIds')"
                    >
                      {{ availableGroups$().get(groupId).name }}
                      <mat-icon matChipRemove>cancel</mat-icon>
                    </mat-chip>
                    }
                  </mat-chip-set>
                  } @else {
                  <span class="text-gray-500">{{ 'UserPortal:SelectGroups' | i18n }}</span>
                  }
                </mat-select-trigger>
                @for (group of availableGroups$().values(); track group) {
                <mat-option [value]="group.id">
                  {{ group.name }}
                </mat-option>
                }
              </mat-select>
            </mat-form-field>
            }
          </div>
          <div class="addtional-fields">
            @if ([AlertType.EnterZone,AlertType.ExitZone].includes(alertForm.get('alertType').value)
            ) {
            <alert-zones-alert-type [alertForm]="alertForm" [geoZones]="geoZones$()" />

            } @if ([AlertType.ExitingRoute].includes(alertForm.get('alertType').value) ) {
            <alert-path-alert-type [alertForm]="alertForm" [paths]="paths$()" />

            } @if (alertForm.get('alertType').value === AlertType.SpeedViolation) {
            <alert-speed-alert-type [alertForm]="alertForm" />

            } @if (alertForm.get('alertType').value === AlertType.WorkTime) {
            <alert-time-alert-type [alertForm]="alertForm" />

            }
          </div>
        </div>
      </div>
      } @if (step() === 2) {
      <alert-select-notification [alertForm]="alertForm" />
      }
      <div class="flex justify-between mt-6">
        <button mat-button mat-flat-button class="cancleButton" (click)="prevStep()">
          {{ (step() == 1 ? 'UserPortal:Cancel' : 'UserPortal:Previous') | i18n }}
        </button>
        <button mat-button mat-flat-button (click)="nextStep()">
          {{ (step() == 1 ? 'UserPortal:Next' : 'UserPortal:Save') | i18n }}
        </button>
      </div>
    </form>
  </mat-card>
</div>
