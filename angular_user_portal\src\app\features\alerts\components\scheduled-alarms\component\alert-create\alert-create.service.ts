import { Injectable, inject } from '@angular/core';
import { CreateAlertDefinitionBaseDto } from '@proxy/mobile/alert-definitions/dtos';
import { ExceedingSpeedAlertDefinitionService } from '@proxy/mobile/alert-definitions/exceeding-speed-alert-definitions';
import { ExitingRouteAlertDefinitionService } from '@proxy/mobile/alert-definitions/exiting-route-alert-definitions';
import { JobTimeAlertDefinitionService } from '@proxy/mobile/alert-definitions/job-time-alert-definitions';
import { EnteringZoneAlertDefinitionService } from '@proxy/mobile/alert-definitions/zone-alert-definitions/entering-zone-alert-definitions';
import { ExitingZoneAlertDefinitionService } from '@proxy/mobile/alert-definitions/zone-alert-definitions/exiting-zone-alert-definitions';
import { AlertType } from './alert-type.enum';

@Injectable({
  providedIn: 'root',
})
export class AlertCreateService {
  private enteringZoneAlertService = inject(EnteringZoneAlertDefinitionService);
  private exitingZoneAlertService = inject(ExitingZoneAlertDefinitionService);
  private exceedingSpeedAlertService = inject(ExceedingSpeedAlertDefinitionService);
  private jobTimeAlertService = inject(JobTimeAlertDefinitionService);
  private exitingRouteAlertDefinitionService = inject(ExitingRouteAlertDefinitionService);

  createAlert(
    alertData: CreateAlertDefinitionBaseDto & {
      alertType: AlertType;
      geoZoneIds: string[];
      routeIds: string[];
      maxSpeed: number;
      name?: string;
      startTime?: string;
      endTime?: string;
      daysOfWeek: any[];
    }
  ) {
    switch (alertData.alertType) {
      case AlertType.EnterZone: {
        const { geoZoneIds, vehicleIds, vehicleGroupIds, notificationMethods } = alertData;
        return this.enteringZoneAlertService.create({
          geoZoneIds,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        });
      }
      case AlertType.ExitZone: {
        const { geoZoneIds, vehicleIds, vehicleGroupIds, notificationMethods } = alertData;

        return this.exitingZoneAlertService.create({
          geoZoneIds,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        });
      }
      case AlertType.SpeedViolation: {
        const { maxSpeed, vehicleIds, vehicleGroupIds, notificationMethods } = alertData;

        return this.exceedingSpeedAlertService.create({
          maxSpeed,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        });
      }
      case AlertType.WorkTime: {
        const {
          name,
          startTime,
          endTime,
          daysOfWeek,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        } = alertData;

        const startTimeOnly = new Date(startTime).toLocaleTimeString('en-US', { hour12: false });
        const endTimeOnly = new Date(endTime).toLocaleTimeString('en-US', { hour12: false });

        return this.jobTimeAlertService.create({
          name,
          startTime: startTimeOnly,
          endTime: endTimeOnly,
          daysOfWeek,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        });
      }
      case AlertType.ExitingRoute: {
        const { routeIds, vehicleIds, vehicleGroupIds, notificationMethods } = alertData;

        return this.exitingRouteAlertDefinitionService.create({
          routeIds,
          vehicleIds,
          vehicleGroupIds,
          notificationMethods,
        });
      }
      default:
        throw new Error('unknown');
    }
  }
}
