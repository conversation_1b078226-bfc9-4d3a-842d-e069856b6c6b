import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { AlertType } from '@proxy/alert-definitions';
import { EnteringZoneAlertDefinitionService } from '@proxy/mobile/alert-definitions/zone-alert-definitions/entering-zone-alert-definitions';
import { ExitingZoneAlertDefinitionService } from '@proxy/mobile/alert-definitions/zone-alert-definitions/exiting-zone-alert-definitions';
import { GeoZoneDto } from '@proxy/mobile/geo-zones';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { Layer } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
import { GeozoneAccordionComponent } from '../../../geozone/components/geozone-accourdion/geozone-accordion.component';

@Component({
  selector: 'app-show-geozone-dialog',
  standalone: true,
  templateUrl: './show-geozone-dialog.component.html',
  imports: [MatButton, LanguagePipe, MatExpansionModule, GeozoneAccordionComponent],
})
export class ShowGeozoneDialogComponent {
  dialogRef = inject(MatDialogRef<ShowGeozoneDialogComponent>);
  data = inject<{ id: string; type: AlertType }>(MAT_DIALOG_DATA);

  exitingZoneAlertDefinitionService = inject(ExitingZoneAlertDefinitionService);
  enteringZoneAlertDefinitionService = inject(EnteringZoneAlertDefinitionService);
  services = {
    [AlertType.EnteringZone]: this.enteringZoneAlertDefinitionService,
    [AlertType.ExitingZone]: this.exitingZoneAlertDefinitionService,
  };
  geoZones$ = signal<GeoZoneDto[]>([]);

  opendgeoZones$: BehaviorSubject<{ [key: string]: Layer[] }> = new BehaviorSubject({});

  ngOnInit(): void {
    this.GetGeoZones();
  }
  GetGeoZones() {
    this.services[this.data.type]
      .getGeoZones(this.data.id, { maxResultCount: 100, skipCount: 0 })
      .subscribe(val => {
        this.geoZones$.set(val.items);
      });
  }
}
