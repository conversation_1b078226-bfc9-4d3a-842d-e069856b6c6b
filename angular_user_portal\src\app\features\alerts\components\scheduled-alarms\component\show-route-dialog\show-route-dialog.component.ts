import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { ExitingRouteAlertDefinitionService } from '@proxy/mobile/alert-definitions/exiting-route-alert-definitions';
import { RouteDto } from '@proxy/mobile/routes/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { Layer } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
import { RouteAccordionComponent } from 'src/app/features/routes/components/defined-routes/components/route-accordion/route-accordion.component';

@Component({
  selector: 'app-show-route-dialog',
  standalone: true,
  templateUrl: './show-route-dialog.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatChipsModule,
    LanguagePipe,
    MatExpansionModule,
    RouteAccordionComponent,
  ],
})
export class ShowAlertRoutesComponent {
  dialogRef = inject(MatDialogRef<ShowAlertRoutesComponent>);
  data = inject(MAT_DIALOG_DATA);

  private exitingRouteAlertDefinition = inject(ExitingRouteAlertDefinitionService);
  routes$ = signal<RouteDto[]>([]);

  opendroutes$: BehaviorSubject<{ [key: string]: Layer[] }> = new BehaviorSubject({});
  openedNodes$: BehaviorSubject<{
    [key: string]: Layer[];
  }> = new BehaviorSubject({});

  ngOnInit(): void {
    this.GetRoutes();
  }
  GetRoutes() {
    this.exitingRouteAlertDefinition
      .getRoutes(this.data, { maxResultCount: 100, skipCount: 0 })
      .subscribe(val => {
        this.routes$.set(val.items);
      });
  }
}
