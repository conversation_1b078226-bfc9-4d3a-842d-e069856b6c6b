import { isNumber, LocalizationModule } from '@abp/ng.core';
import { DatePipe, KeyValuePipe } from '@angular/common';
import { Component, computed, inject, input, signal, WritableSignal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatFormField, MatSelectModule } from '@angular/material/select';
import { RouterLink } from '@angular/router';
import { MonitoringService } from '@proxy/mobile/monitoring';
import { WarpGtsHistoryDto } from '@proxy/mobile/monitoring/dtos/device-history';
import { LiveLocationDto } from '@proxy/mobile/monitoring/dtos/live-locations';
import { VehicleReportService } from '@proxy/mobile/reports';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { car_svg } from '@shared';
import { openDateRangeDialog } from '@shared/components/date-range-dialog/date-range-dialog.component';
import { CustomLine, CustomMarker, MapComponent } from '@shared/components/map/map.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { combineLatest, firstValueFrom, interval, map, of, startWith, switchMap, tap } from 'rxjs';

@Component({
  selector: 'app-track-vehicle-history',
  templateUrl: './track-vehicle-history.component.html',
  standalone: true,
  imports: [
    LocalizationModule,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatButtonModule,
    MatIcon,
    RouterLink,
    MatSelectModule,
    MatFormField,
    KeyValuePipe,
    MapComponent,
  ],
  styles: `
  :host {
  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-vertical-padding: 10px;
  --mat-form-field-container-height: 44px;
  --mdc-filled-button-container-shape: 10px;
  --mdc-filled-button-container-color: #7164e4;
  }
  `,
})
export class TrackVehicleHistoryComponent {
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);
  alertService = inject(AlertService);

  times = {
    hour: 60 * 60 * 1000,
    day: 24 * 60 * 60 * 1000,
  };
  selectedTime = new FormControl<number>(this.times.hour);

  selectedTime$ = toSignal(this.selectedTime.valueChanges.pipe(startWith(this.times.hour)));

  toTime = signal(new Date());
  from = computed(() => {
    if (!isNumber(this.selectedTime$())) {
      return '';
    }
    if (this.selectedTime$() == this.times.day) {
      return this.datePipe.transform(
        new Date(+this.toTime() - this.selectedTime.value).toISOString(),
        'yyyy-MM-dd'
      );
    }
    if (this.selectedTime$() == this.times.hour) {
      return this.datePipe.transform(
        new Date(+this.toTime() - this.selectedTime.value).toISOString(),
        'HH:mm a  dd/MM '
      );
    }
    return this.datePipe.transform(
      new Date(+this.toTime() - this.selectedTime.value).toISOString(),
      'HH:mm a  dd/MM '
    );
  });
  to = computed(() => {
    if (!isNumber(this.selectedTime$())) {
      return '';
    }
    if (this.selectedTime$() == this.times.day) {
      return this.datePipe.transform(this.toTime().toISOString(), 'yyyy-MM-dd');
    }
    if (this.selectedTime$() == this.times.hour) {
      return this.datePipe.transform(this.toTime().toISOString(), 'HH:mm a  dd/MM ');
    }
    return this.datePipe.transform(this.toTime().toISOString(), 'HH:mm a  dd/MM ');
  });

  destance = signal<string>('0 Km');
  info = [
    {
      icon: 'calendar_today',
      name: 'UserPortal:From',
      key: this.from,
    },
    { icon: 'directions_car', name: 'UserPortal:distance', key: this.destance },
    {
      icon: 'calendar_today',
      name: 'UserPortal:To',
      key: this.to,
    },
  ];

  vehicleService = inject(VehicleService);
  monitoringService = inject(MonitoringService);
  vehicleReportService = inject(VehicleReportService);

  selectedVehcileHistory$: WritableSignal<WarpGtsHistoryDto | any> = signal({});

  id = input<string | null>(null);

  interval$ = interval(5000).pipe(startWith(null));

  vehicle$ = signal<VehicleDto | any>({});
  line$ = signal<Layer[]>([]);

  options$ = signal<MapOptions>({});

  monitorInfo$ = combineLatest([
    toObservable(this.id),
    this.selectedTime.valueChanges.pipe(startWith(this.times.hour)),
  ]).pipe(
    switchMap(val => {
      if ((val[0], val[1]))
        return combineLatest([
          this.report(val[0], new Date(+this.toTime() - val[1]), this.toTime()).pipe(
            tap(val => {
              this.destance.set(val.distance + 'KM');
            })
          ),
          this.monitoringService
            .getVehicleHistory({
              onlySpeed: true,
              vehicleId: val[0],
              fromDate: new Date(+this.toTime() - val[1]).toLocalISOString().split('Z')[0],
              toDate: this.toTime().toLocalISOString().split('Z')[0],
            })
            .pipe(
              map(history => {
                if (!history) {
                  this.line$.set([]);
                  this.alertService.warning('there is no movement in the selected timeframe', {
                    duration: 3000,
                  });
                  return;
                }
                if (history.length == 0) {
                  this.line$.set([]);
                  this.alertService.warning('there is no movement in the selected timeframe', {
                    duration: 3000,
                  });
                  return;
                }
                const nodes: any[] = history[0].values.map(v => {
                  return [+v.latitude, +v.longitude];
                });
                this.line$.set([CustomLine({ line: nodes, color: '#ff0000' })]);
                this.options$.set({ zoom: 12, center: latLng(nodes[0]) });
                this.selectedVehcileHistory$.set(history);
              })
            ),
        ]);
      else {
        return of(null);
      }
    })
  );

  async ngOnInit() {
    const res = await firstValueFrom(this.vehicleService.get(this.id()));
    res.colorHex = hexToColor(res.colorHex);
    this.vehicle$.set(res);
    this.monitorInfo$.subscribe();
  }

  report(vehicleId: string, from: Date = new Date(), to: Date = new Date()) {
    if (from && to && from.toLocalISOString() && to.toLocalISOString()) {
      return this.vehicleReportService.getVehicleDistanceAverageAndMaxSpeedReport({
        vehicleId: vehicleId,
        ignoreSpeedUnder: 0,
        fromDate: from.toLocalISOString().split('Z')[0],
        toDate: to.toLocalISOString().split('Z')[0],
      });
    }
  }

  openDateDialog() {
    openDateRangeDialog(this.dialog, {
      fromDate: new Date(+new Date() - this.selectedTime.value),
      toDate: new Date(),
    })
      .pipe(
        tap(val => {
          if (val) {
            this.toTime.set(val.toDate);
            this.selectedTime.setValue(+val.toDate - +val.fromDate);
          }
        })
      )
      .subscribe();
  }
}
