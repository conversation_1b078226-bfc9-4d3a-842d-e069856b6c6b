import { Component } from '@angular/core';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [LanguagePipe],
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss'],
})
export class AboutComponent {
  logoPath: string = '/assets/images/svg/go-track-about-logo.svg';
  socialIcons = [
    {
      name: 'Facebook',
      src: '/assets/images/svg/facebook.svg',
      link: 'https://www.facebook.com/gotrack.syr',
    },
    {
      name: 'WhatsApp',
      src: '/assets/images/svg/whatsapp-about.svg',
      link: 'https://api.whatsapp.com/send?phone=0949333701',
    },
    {
      name: 'Telegram',
      src: '/assets/images/svg/telegram.svg',
      link: 'https://t.me/gotracksy',
    },
    {
      name: 'Instagram',
      src: '/assets/images/svg/instagram.png',
      link: 'https://www.instagram.com/gotrack.syr/',
    },
  ];
}
