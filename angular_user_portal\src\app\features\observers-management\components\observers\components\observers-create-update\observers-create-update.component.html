<mat-card class="p-6 h-full">
  <h2 class="main-header">
    {{ !!data ? ('UserPortal:EditObserver' | i18n) : ('UserPortal:AddObserver' | i18n) }}
  </h2>
  <form [formGroup]="observerForm" class="p-4 flex flex-col h-full">
    <div class="h-full">
      <div class="main-fields">
        <mat-label class="pr-2 mb-3">{{ 'UserPortal:ObserverName' | i18n }}</mat-label>
        <mat-form-field appearance="outline" class="mb-2 w-full">
          <input matInput formControlName="name" [placeholder]="'UserPortal:ObserverName' | i18n" />
        </mat-form-field>

        <mat-label class="pr-2">{{ 'UserPortal:PhoneNumber' | i18n }}</mat-label>
        <mat-form-field appearance="outline" class="w-full">
          <input
            matInput
            formControlName="phoneNumber"
            [placeholder]="'UserPortal:PhoneNumber' | i18n"
          />
        </mat-form-field>

        @if (!!!data) {
        <div class="flex flex-wrap justify-between items-center">
          <mat-label class="pr-2">{{ 'UserPortal:BasedOnGroups' | i18n }}</mat-label>
          <mat-radio-group
            [ngModel]="withGroups()"
            [ngModelOptions]="{ standalone: true }"
            (ngModelChange)="withGroups.set($event)"
          >
            <mat-radio-button class="example-radio-button" [value]="false">
              {{ 'UserPortal:No' | i18n }}
            </mat-radio-button>
            <mat-radio-button class="example-radio-button" [value]="true">
              {{ 'UserPortal:Yes' | i18n }}
            </mat-radio-button>
          </mat-radio-group>
        </div>
        <div class="flex justify-between items-center">
          <mat-label class="pr-2">{{ 'UserPortal:SelectVehicles' | i18n }}</mat-label>
          <mat-checkbox
            (change)="toggleSelectAll('vehicleIds', availableVehicles$())"
            [checked]="observerForm.controls.vehicleIds.value?.length == availableVehicles$().size"
            labelPosition="before"
          >
            {{ 'UserPortal:AllVehicles' | i18n }}
          </mat-checkbox>
        </div>
        <mat-form-field appearance="outline" class="w-full">
          <mat-select
            formControlName="vehicleIds"
            multiple
            placeholder="{{ 'UserPortal:SelectVehicles' | i18n }}"
          >
            <mat-select-trigger>
              @if ((observerForm.controls.vehicleIds?.value?.length || 0) > 0) {
              <mat-chip-set>
                @for (vehicleId of observerForm.controls.vehicleIds.value; track vehicleId) {
                <mat-chip [removable]="true" (removed)="removeChips(vehicleId, 'vehicleIds')">
                  {{ availableVehicles$().get(vehicleId).name }}
                  <mat-icon matChipRemove>cancel</mat-icon>
                </mat-chip>
                }
              </mat-chip-set>
              } @else {
              <span class="text-gray-500">{{ 'UserPortal:SelectVehicles' | i18n }}</span>
              }
            </mat-select-trigger>
            @for (vehicle of availableVehicles$().values(); track vehicle) {
            <mat-option [value]="vehicle.id">
              {{ vehicle.name }}
            </mat-option>
            }
          </mat-select>
        </mat-form-field>
        @if (withGroups()) {
        <div class="flex justify-between items-center">
          <mat-label class="pr-2">{{ 'UserPortal:SelectGroup' | i18n }}</mat-label>
          <mat-checkbox
            (change)="toggleSelectAll('vehicleGroupIds', availableGroups$())"
            [checked]="
              observerForm.controls.vehicleGroupIds.value?.length == availableGroups$().size
            "
            labelPosition="before"
          >
            {{ 'UserPortal:AllGroups' | i18n }}
          </mat-checkbox>
        </div>
        <mat-form-field appearance="outline" class="w-full">
          <mat-select
            [formControl]="observerForm.controls.vehicleGroupIds"
            multiple
            placeholder="{{ 'UserPortal:SelectGroups' | i18n }}"
          >
            <mat-select-trigger>
              @if ((observerForm.get('vehicleGroupIds')?.value?.length || 0) > 0) {
              <mat-chip-set>
                @for (groupId of observerForm.get('vehicleGroupIds')?.value; track groupId) {
                <mat-chip [removable]="true" (removed)="removeChips(groupId, 'vehicleGroupIds')">
                  {{ availableGroups$().get(groupId).name }}
                  <mat-icon matChipRemove>cancel</mat-icon>
                </mat-chip>
                }
              </mat-chip-set>
              } @else {
              <span class="text-gray-500">{{ 'UserPortal:SelectGroups' | i18n }}</span>
              }
            </mat-select-trigger>
            @for (group of availableGroups$().values(); track group) {
            <mat-option [value]="group.id">
              {{ group.name }}
            </mat-option>
            }
          </mat-select>
        </mat-form-field>
        } }
      </div>
    </div>

    <div class="flex justify-between mt-6">
      <button mat-button mat-flat-button class="cancleButton" (click)="cancel()">
        {{ 'UserPortal:cancel' | i18n }}
      </button>

      <button mat-button mat-flat-button [disabled]="observerForm.invalid" (click)="saveObserver()">
        {{ 'UserPortal:save' | i18n }}
      </button>
    </div>
  </form>
</mat-card>
