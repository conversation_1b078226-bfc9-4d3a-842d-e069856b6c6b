import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { Mat<PERSON>utton } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { ObservationService } from '@proxy/mobile/observations';
import { UserTrackAccountAssociationDto } from '@proxy/mobile/user-track-account-associations/dtos';
import { openAddVehiclesDialog } from '@shared/components/add-vehicles-dialog/add-vehicles-dialog.component';
import { openAddVehiclesGroupDialog } from '@shared/components/add-vehicles-group-dialog/add-vehicles-group-dialog.component';
import { openRelatedItemsDialog } from '@shared/components/related-items-dialog/related-items-dialog.component';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, firstValueFrom, map, switchMap } from 'rxjs';
import { ObserversCreateUpdateComponent } from './components/observers-create-update/observers-create-update.component';

@Component({
  standalone: true,
  selector: 'app-observers',
  templateUrl: './observers.component.html',
  imports: [
    MatExpansionModule,
    LanguagePipe,
    MatMenuModule,
    LanguagePipe,
    CommonModule,
    MatIconModule,
    MatButton,
    MatProgressSpinner,
    MatSlideToggle,
  ],
})
export class ObserversComponent {
  private observationService = inject(ObservationService);
  private alertService = inject(AlertService);
  private dialog = inject(MatDialog);
  isLoading = signal<boolean>(false);

  selectedObserver: string | null = null;
  assignedVehiclesAndGroups: any[] = [];

  observers$ = signal<UserTrackAccountAssociationDto[]>([]);

  ngOnInit(): void {
    this.loadObservers();
  }

  loadObservers() {
    this.observationService
      .getList({ skipCount: 0, maxResultCount: 100 })
      .pipe(map(val => val.items))
      .subscribe(val => {
        this.observers$.set(val);
      });
  }

  deleteObserver(observer: UserTrackAccountAssociationDto) {
    this.observationService.deleteObserver(observer.phoneNumber).subscribe(val => {
      this.loadObservers();
    });
  }

  async viewAssignedVehicles(observer: UserTrackAccountAssociationDto) {
    const vehicles = await firstValueFrom(
      this.observationService.getListVehicleAndVehicleGroupOfObsever(observer.id, {
        skipCount: 0,
        maxResultCount: 100,
      })
    );
    openRelatedItemsDialog({ items: vehicles.items, type: 'Observation', dialog: this.dialog })
      .pipe(
        filter((data: any) => data && data?.action),
        switchMap(data => {
          return this.observationService.removeObservationVehicle(observer.id, data.id);
        })
      )
      .subscribe(() => {
        this.loadObservers();
      });
  }

  openCreateObserverDialog(data?: any): void {
    const dialogRef = this.dialog.open(ObserversCreateUpdateComponent, {
      data: data,
      height: '90%',
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadObservers();
      }
    });
  }
  addGroups(observer: UserTrackAccountAssociationDto) {
    openAddVehiclesGroupDialog(this.dialog, observer.id)
      .pipe(
        filter(val => !!val),
        switchMap(val => this.observationService.addObservationVehicleGroup(observer.id, val))
      )
      .subscribe(() => {
        this.loadObservers();
      });
  }

  addVehicles(observer: UserTrackAccountAssociationDto) {
    openAddVehiclesDialog(this.dialog, observer.id)
      .pipe(
        filter(val => !!val),
        switchMap(val => this.observationService.addObservationVehicle(observer.id, val))
      )
      .subscribe(() => {
        this.loadObservers();
      });
  }
  toggleAlert(event: any, observer: UserTrackAccountAssociationDto): void {
    const isChecked = event.checked;
    this.isLoading.set(true);
    const toggleAction = isChecked
      ? this.observationService.activateObserver(observer.userId)
      : this.observationService.deactivateObserver(observer.userId);

    toggleAction.subscribe({
      next: () => {
        this.alertService.success(isChecked ? 'SuccessEnabled' : 'SuccessDisabled');
      },
      complete: () => {
        this.loadObservers();
        this.isLoading.set(false);
      },
    });
  }
}
