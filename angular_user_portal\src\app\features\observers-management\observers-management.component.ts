import { Component } from '@angular/core';
import { DynamicTabsComponent } from '@shared/components/dynamic-tabs/dynamic-tabs.component';
import { ObserversComponent } from './components/observers/observers.component';

@Component({
  selector: 'app-observers-management',
  standalone: true,
  imports: [DynamicTabsComponent],
  templateUrl: './observers-management.component.html',
})
export class ObserversManagementComponent {
  tabs: { label: string; component: any }[] = [
    {
      label: 'GoTrack:Observers',
      component: ObserversComponent,
    },
  ];
}
