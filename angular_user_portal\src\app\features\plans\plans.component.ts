import { KeyValuePipe, Location } from '@angular/common';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { SubscriptionPlanService } from '@proxy/mobile/subscription-plans';
import { SubscriptionPlanDto } from '@proxy/mobile/subscription-plans/dtos';
import { FeatureDto } from '@proxy/volo/abp/feature-management';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom } from 'rxjs';
import { TempSubscriptionCacheService } from '../track-accounts/services/temp-subscription-cache.service';

@Component({
  selector: 'app-plans',
  standalone: true,
  templateUrl: `./plans.component.html`,
  imports: [MatIcon, KeyValuePipe, LanguagePipe, MatButton],
})
export class PlansComponent {
  plans = new Map<string, SubscriptionPlanDto>([]);
  features = new Map<string, FeatureDto[]>([]);

  tempSubscriptionCacheService = inject(TempSubscriptionCacheService);
  activatedRoute = inject(ActivatedRoute);
  router = inject(Router);
  subscriptionPlanService = inject(SubscriptionPlanService);
  location = inject(Location);

  params$ = toSignal(this.activatedRoute.queryParams);

  ngOnInit(): void {
    this.subscriptionPlanService.getList().subscribe(value => {
      value.map(plan => {
        this.getFeatures(plan.key);
        this.plans.set(plan.key, { ...plan });
      });
    });
  }

  setPlan(plan: SubscriptionPlanDto) {
    if (this.params$().select) {
      this.tempSubscriptionCacheService.setValues({
        ...this.tempSubscriptionCacheService.getValues(),
        subscriptionPlanKey: plan.key,
        userCount: plan.userCount,
      });
      this.tempSubscriptionCacheService.take.push(1);
      this.location.back();
    } else {
      this.router.navigate(['/track-accounts', 'create']);
    }
  }

  async getFeatures(key: string) {
    const r = await firstValueFrom(this.subscriptionPlanService.getFeatures(key));
    this.features.set(key, r);
  }
}
