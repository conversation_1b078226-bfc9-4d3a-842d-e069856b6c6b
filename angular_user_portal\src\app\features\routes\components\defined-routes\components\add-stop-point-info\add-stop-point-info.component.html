<div mat-dialog-title class="dialog-title">
  <h2>{{ 'UserPortal:add new stop point' | i18n }}</h2>
</div>

<div mat-dialog-content class="dialog-content">
  <form [formGroup]="form" class="space-y-4">
    <div class="space-y-4">
      <div>
        <mat-label class="block mb-2">{{ 'UserPortal:name' | i18n }}</mat-label>
        <mat-form-field appearance="outline" class="w-full">
          <input formControlName="name" matInput />
          <mat-error>
            <app-validation [errors]="form.get('name')" />
          </mat-error>
        </mat-form-field>
      </div>

      <div>
        <mat-label class="block mb-2">{{ 'UserPortal:color' | i18n }}</mat-label>
        <mat-form-field appearance="outline" class="w-full">
          <mat-select
            formControlName="hexColor"
            #e
            panelClass="flex flex-wrap"
            [ngStyle]="{ backgroundColor: e.value }"
          >
            @for (color of colors(); track color) {
            <mat-option [value]="color">
              <div class="size-8" [ngStyle]="{ backgroundColor: color }"></div>
            </mat-option>
            }
          </mat-select>
          <mat-error>
            <app-validation [errors]="form.get('hexColor')" />
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </form>
</div>

<div mat-dialog-actions class="dialog-actions flex justify-end gap-2 p-4">
  <button mat-flat-button (click)="close()" class="cancel-button">
    {{ 'UserPortal:cancel' | i18n }}
  </button>
  <button mat-flat-button color="primary" (click)="save()">
    {{ 'UserPortal:save' | i18n }}
  </button>
</div>
