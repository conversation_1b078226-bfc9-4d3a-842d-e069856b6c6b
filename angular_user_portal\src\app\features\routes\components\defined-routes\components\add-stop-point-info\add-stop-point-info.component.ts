import { CommonModule, NgStyle } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, Observable } from 'rxjs';

// Interface for dialog data
export interface AddStopPointDialogData {
  routeId?: string;
  stopPoint?: any; // Replace with actual stop point type
}

// Interface for dialog result
export interface AddStopPointResult {
  name: string;
  hexColor: string;
}

@Component({
  selector: 'app-add-stop-point-info',
  standalone: true,
  templateUrl: './add-stop-point-info.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
  ],
})
export class AddStopPointInfoComponent implements OnInit {
  private fb = inject(FormBuilder);
  private dialogRef = inject(MatDialogRef<AddStopPointInfoComponent>);
  data = inject<AddStopPointDialogData>(MAT_DIALOG_DATA);

  colors = signal(colors);
  form: FormGroup = this.fb.group({
    name: ['', [Validators.required]],
    hexColor: ['#ffffff', [Validators.required]],
  });

  ngOnInit(): void {
    // If editing existing stop point, populate form
    if (this.data?.stopPoint) {
      this.form.patchValue({
        name: this.data.stopPoint.name,
        hexColor: this.data.stopPoint.hexColor,
      });
    }
  }

  save() {
    if (this.form.valid) {
      const result: AddStopPointResult = {
        name: this.form.get('name')?.value,
        hexColor: this.form.get('hexColor')?.value,
      };
      this.dialogRef.close(result);
    } else {
      // Mark all fields as touched to show validation errors
      this.form.markAllAsTouched();
    }
  }

  close() {
    this.dialogRef.close();
  }
}

// Helper function to open the dialog
export const openAddStopPointInfoDialog = (
  dialog: MatDialog,
  dialogData?: AddStopPointDialogData
): Observable<AddStopPointResult | undefined> => {
  return dialog
    .open(AddStopPointInfoComponent, {
      data: dialogData || {},
    })
    .afterClosed()
    .pipe(filter(val => !!val));
};
