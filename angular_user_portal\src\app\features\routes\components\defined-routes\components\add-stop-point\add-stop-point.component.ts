import { CommonModule, NgStyle } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCard } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import { RouteViewModelDto } from '@proxy/mobile/routes/dtos';
import { CreateStopPointDto } from '@proxy/mobile/stop-points/dtos';
import {
  changeNodeDto,
  CustomDrawMarker,
  CustomLine,
  CustomMarker,
  MapComponent,
} from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { hexToColor } from '@shared/functions/hex-to-color';
import { getMinDistanceToPolyline, removeLayer } from '@shared/functions/map-helper';
import { live_icon } from '@shared/helper-assets/live';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { map, switchMap } from 'rxjs';

// Interface for dialog data
export interface AddStopPointDialogData {
  routeId?: string;
  stopPoint?: any; // Replace with actual stop point type
}

@Component({
  selector: 'app-add-stop-point',
  standalone: true,
  templateUrl: './add-stop-point.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
    MapComponent,
    MatCard,
  ],
})
export class AddStopPointComponent implements OnInit {
  lineSpacelength = 30;

  draw = signal<any>({
    marker: { icon: CustomDrawMarker },
  });
  shawDraw = signal(false);
  colors = signal(colors);
  route: RouteViewModelDto;
  nodes$ = signal<Layer[]>([]);
  options$ = signal<MapOptions>({});

  private fb = inject(FormBuilder);
  private alertService = inject(AlertService);
  private routeService = inject(RouteService);
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);
  lineNodes = [];

  form = this.fb.group({
    name: ['', [Validators.required]],
    hexColor: ['#ffffff', [Validators.required]],
    point: this.fb.group({
      longitudeX: [null, [Validators.required]],
      latitudeY: [null, [Validators.required]],
    }),
  });

  ngOnInit(): void {
    this.activatedRoute.params
      .pipe(
        switchMap(params => {
          return this.routeService.get(params.routeId);
        }),
        map(val => {
          const nodes: any[] = val.line.map(v => {
            return [+v.latitudeY, +v.longitudeX];
          });
          this.lineNodes = nodes;
          this.nodes$.set([CustomLine({ line: nodes, color: hexToColor(val.color) })]);
          val.stopPoints.map(v => {
            this.nodes$.update(val => {
              return [
                ...val,
                CustomMarker({
                  icon: live_icon(hexToColor(hexToColor(v.color))),
                  latlang: [v.point.latitudeY, v.point.longitudeX] as LatLngTuple,
                }),
              ];
            });
          });
          this.options$.set({ zoom: 12, center: latLng(nodes[0]) });
          this.route = val;
        })
      )
      .subscribe();
  }

  onNodeChange(event: changeNodeDto) {
    if (event.event.layerType == 'marker') {
      const polylinePoints = this.lineNodes.map(v => ({
        latitudeY: v[0],
        longitudeX: v[1],
      }));
      const { lat, lng } = event.event.layer._latlng;
      const minDistance = getMinDistanceToPolyline(lat, lng, polylinePoints);

      const latitudeYControl = this.form.controls.point.controls.latitudeY;
      const longitudeXControl = this.form.controls.point.controls.longitudeX;
      if (minDistance > this.lineSpacelength) {
        removeLayer(event.layers, lat, lng);
        this.alertService.error('Stop point must be within 30 meters of the route line.');
        return;
      }
      if (latitudeYControl.value && longitudeXControl.value) {
        removeLayer(event.layers, latitudeYControl.value, longitudeXControl.value);
        this.alertService.warning('you Can Only Add One Point');
      }
      latitudeYControl.setValue(lat);
      longitudeXControl.setValue(lng);
    }
  }
  showLine() {
    this.draw.set({ marker: true });
    this.shawDraw.set(true);
  }
  save() {
    if (this.form.valid) {
      const result: CreateStopPointDto = {
        name: this.form.get('name')?.value,
        hexColor: this.form.get('hexColor')?.value,
        point: this.form.get('point')?.value,
      };
      this.routeService
        .addStopPointToRoute(this.activatedRoute.snapshot.params.routeId, result)
        .subscribe(() => {
          this.router.navigate(['/main/routes']);
        });
    } else {
      this.form.markAllAsTouched();
    }
  }

  close() {
    this.router.navigate(['/main/routes']);
  }
}
