<div class="flex flex-col justify-center items-center min-h-screen scale-90">
  <mat-card class="w-3/4 rounded-lg shadow-lg transition-all duration-500 h-11/12 card-blue">    
    <form [formGroup]="form" class="p-6">
      <h2 class="main-header">{{ 'add new Route' | i18n }}</h2>

      <div class="flex flex-col mt-4">
        <div >
          <mat-label class="px-2">{{ 'UserPortal:name' | i18n }}</mat-label>
          <mat-form-field appearance="outline" class="w-full">
            <input formControlName="name" matInput required />
            <mat-error>
              <app-validation [errors]="form.controls.name" />
            </mat-error>
          </mat-form-field>
        </div>
        <div class=>
          <mat-label class="px-2">{{ 'UserPortal:color' | i18n }}</mat-label>
          <mat-form-field appearance="outline" class="w-full">
            <mat-select
              formControlName="hexColor"
              #e
              panelClass="flex flex-wrap"
              required
              [ngStyle]="{ backgroundColor: e.value }"
            >
              @for (color of colors(); track color) {
              <mat-option [value]="color"
                ><div class="size-8" [ngStyle]="{ backgroundColor: color }"></div
              ></mat-option>
              }
            </mat-select>
            <mat-error>
              <app-validation [errors]="form.controls.hexColor" />
            </mat-error>
          </mat-form-field>
        </div>
        <div>
          <a class="cursor-pointer text-main_perple p-2 text-xl" (click)="showLine()"> {{"Start Drawing"|i18n}}</a>
          <div class="w-full h-60" [ngClass]="{'h-96':shawDraw()}">
            <app-map
              class="h-full"
              (change_nodes)="onNodeChange($event)"
              [draw]="draw()"
              [showDraw]="shawDraw()"
            />
          </div>
          <mat-error>
            <app-validation [errors]="form.controls.line" />
          </mat-error>
        </div>
      </div>
      <div class="flex justify-between mt-6">
        <button mat-button mat-flat-button class="cancleButton" (click)="close()">
          {{ 'UserPortal:cancel' | i18n }}
        </button>
        <button mat-button mat-flat-button [disabled]="form.invalid" (click)="save()">
          {{ 'UserPortal:save' | i18n }}
        </button>
      </div>
    </form>
  </mat-card>
</div>