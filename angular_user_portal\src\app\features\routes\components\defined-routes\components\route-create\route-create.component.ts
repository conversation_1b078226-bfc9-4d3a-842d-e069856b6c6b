import { CommonModule, NgClass, NgStyle } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import {
  changeNodeDto,
  CustomDrawMarker,
  CustomMarker,
  MapComponent,
} from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { colorToHex, hexToColor } from '@shared/functions/hex-to-color';
import { getMinDistanceToPolyline, removeLayer } from '@shared/functions/map-helper';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { take } from 'rxjs';
import { openAddStopPointInfoDialog } from '../add-stop-point-info/add-stop-point-info.component';
import { live_icon } from '@shared/helper-assets/live';
import { icon } from 'leaflet';

@Component({
  selector: 'app-route-create',
  standalone: true,
  templateUrl: './route-create.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MapComponent,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
    NgClass,
  ],
})
export class RouteCreateComponent implements OnInit {
  lineSpacelength = 30;
  draw = signal<any>({
    polyline: true,
    marker: { icon: CustomDrawMarker },
  });
  shawDraw = signal(false);
  colors = signal(colors);

  private fb = inject(FormBuilder);
  private routeService = inject(RouteService);
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private alertService = inject(AlertService);

  form: FormGroup = this.fb.group({
    name: this.fb.control('', Validators.required),
    hexColor: this.fb.control('', Validators.required),
    line: this.fb.control('', Validators.required),
    stopPoints: [[]],
  });

  ngOnInit(): void {}

  onNodeChange(event: changeNodeDto) {
    if (event.event.layerType == 'marker') {
      const polylinePoints = this.form.controls['line'].value;
      const { lat, lng } = event.event.layer._latlng;

      const minDistance = getMinDistanceToPolyline(lat, lng, polylinePoints);
      // Check if polyline has at least 2 points
      if (!polylinePoints || polylinePoints.length < 2) {
        removeLayer(event.layers, lat, lng);

        this.alertService.error(
          'Please draw a route line with at least 2 points before adding stop points.'
        );
        return;
      }
      if (minDistance > this.lineSpacelength) {
        removeLayer(event.layers, lat, lng);

        this.alertService.error('Stop point must be within 30 meters of the route line.');
        return;
      }

      openAddStopPointInfoDialog(this.dialog)
        .pipe(take(1))
        .subscribe(val => {
          if (val) {
            const point = { longitudeX: lng, latitudeY: lat };
            this.form.controls['stopPoints'].setValue([
              ...this.form.controls.stopPoints.value,
              {
                point: point,
                name: val.name,
                hexColor: val.hexColor,
              },
            ]);
            removeLayer(event.layers, lat, lng);
            const v: any = {
              latlang: [lat, lng],
              icon: live_icon(val.hexColor),
            };
            event.layers.addLayer(CustomMarker(v));
            this.draw.set({ polyline: true });
          }
        });
    }
    if (event.event.layerType == 'polyline') {
      const temp = event.event.layer._latlngs.map((latLng: any) => {
        const { lat, lng } = latLng;
        return { longitudeX: lng, latitudeY: lat };
      });
      this.form.controls['line'].setValue(temp);

      this.draw.set({ polyline: true });
    }
  }

  save() {
    const obj = { ...this.form.value };
    obj.hexColor = colorToHex(obj.hexColor);
    this.routeService.create(obj).subscribe(() => {
      this.router.navigate(['/main', 'routes']);
    });
  }
  close() {
    this.router.navigate(['/main', 'routes']);
  }

  showLine() {
    this.draw.set({ polyline: true });
    this.shawDraw.set(true);
  }
}
