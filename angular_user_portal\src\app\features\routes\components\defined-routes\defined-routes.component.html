<div class="flex justify-between items-center mt-4">
  <h3 class="text-lg font-semibold">{{ 'UserPortal:lines' | i18n }}</h3>
  <button mat-button color="warn" [routerLink]="['add-route']">
    {{ 'add new Route' | i18n }}
  </button>
</div>

<mat-accordion class="mt-4">
  @for (route of routes$(); track route; let i = $index) {
  <app-route-accordion
    [route]="route"
    [opendroutes$]="opendroutes$"
    [openedNodes$]="openedNodes$"
    (getData)="this.GetRoutes()"
  />
  }
</mat-accordion>
