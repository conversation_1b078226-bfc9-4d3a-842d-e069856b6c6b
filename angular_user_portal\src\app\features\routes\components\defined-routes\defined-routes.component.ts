import { Component, inject, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatMenuModule } from '@angular/material/menu';
import { RouterLink } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import { RouteDto } from '@proxy/mobile/routes/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { Layer } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
import { RouteAccordionComponent } from './components/route-accordion/route-accordion.component';

@Component({
  selector: 'app-defined-routes',
  standalone: true,
  templateUrl: `./defined-routes.component.html`,
  imports: [
    MatExpansionModule,
    MatDialogModule,
    MatButton,
    MatDialogModule,
    LanguagePipe,
    MatMenuModule,
    RouterLink,
    RouteAccordionComponent,
  ],
})
export class DefinedRoutesComponent {
  private routeService = inject(RouteService);
  routes$ = signal<RouteDto[]>([]);

  opendroutes$: BehaviorSubject<{ [key: string]: Layer[] }> = new BehaviorSubject({});
  openedNodes$: BehaviorSubject<{
    [key: string]: Layer[];
  }> = new BehaviorSubject({});

  ngOnInit(): void {
    this.GetRoutes();
  }
  GetRoutes() {
    this.routeService.getList({ maxResultCount: 1000, skipCount: 0 }).subscribe(val => {
      this.routes$.set(val.items);
    });
  }
}
