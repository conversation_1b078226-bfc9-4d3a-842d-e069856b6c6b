import { LocalizationModule } from '@abp/ng.core';
import { NgClass } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { FEATURES } from '@shared/constants/features-token';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-services-page',
  templateUrl: './services-page.component.html',
  standalone: true,
  imports: [LocalizationModule, RouterLink, LanguagePipe, NgClass],
  styles: `
    .item {
      @apply rounded-xl flex justify-center items-center  size-40 cursor-pointer;//rotate-45
    }
    .inner_item {
      @apply  text-center align-middle w-2/3 h-2/3 text-white;//-rotate-45
    }
  `,
})
export class ServicesPageComponent {
  feature = inject(FEATURES);

  alerts = computed(() => {
    const features = [
      'GoTrack.GeographicAreaManagement',
      'GoTrack.Alerts.ExceedingSpeedAlert',
      'GoTrack.Alerts.JobTimeAlert',
      'GoTrack.Alerts.ExitingRouteAlert',
      'GoTrack.Alerts.EnteringZoneAlert',
      'GoTrack.Alerts.ExitingZoneAlert',
    ];
    return features.find(v => {
      return this.feature()[v];
    });
  });
}
