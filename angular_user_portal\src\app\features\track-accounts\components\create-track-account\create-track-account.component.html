<div class="bg-[url('/assets/images/background-grad.png')] bg-cover min-h-svh h-full">
  <div
    class="grid flex-1 grid-cols-1 grid-rows-1 justify-center justify-items-center content-center items-center p-6 h-full md:p-12 min-h-svh"
  >
    <div class="relative bg-white bg-opacity-90 rounded-3xl size-full">
      <div class="flex flex-col justify-center items-center h-full">
        <img src="assets/images/logo/LogoColored.png" class="mb-4 w-32" alt="" />
        <div class="w-full flex justify-center">
          <div class="p-4 sm:w-96">
            <form [formGroup]="final" class="gray">
              @if(this.step()==1){
              <p class="mb-2 text-center text-main_gray">
                {{ 'UserPortal:CREATE_ACCOUNT_PANNER' | i18n }}
              </p>
              <div class="grid grid-cols-2 gap-4 mb-2">
                <button
                  [ngClass]="{ sel: !proOption() }"
                  (click)="this.pro.set(true); this.step.set(2)"
                  mat-button
                  mat-flat-button
                >
                  {{ 'UserPortal:business' | i18n }}
                </button>
                <button
                  [ngClass]="{ sel: proOption() }"
                  (click)="this.pro.set(false); this.step.set(2)"
                  mat-button
                  mat-flat-button
                >
                  {{ 'UserPortal:personal' | i18n }}
                </button>
              </div>
              } @if(this.step()==2) { @if (pro()) {
              <div class="my-4">
                <mat-form-field>
                  <input
                    matInput
                    [placeholder]="'UserPortal:companyName' | i18n"
                    formControlName="companyName"
                    required
                  />
                  <mat-error>
                    <app-validation [errors]="final.controls['companyName']" />
                  </mat-error>
                </mat-form-field>
              </div>
              }
              <div formGroupName="address" class="grid grid-cols-2 gap-4">
                <mat-form-field>
                  <mat-select
                    matInput
                    [placeholder]="'UserPortal:governorate' | i18n"
                    formControlName="governorate"
                    required
                  >
                    @for (item of arrays.Governorate; track $index) {
                    <mat-option [value]="item.nodeId">{{ item.displayName }}</mat-option>
                    }
                  </mat-select>
                </mat-form-field>
                <mat-form-field>
                  <mat-select
                    matInput
                    [placeholder]="'UserPortal:city' | i18n"
                    formControlName="city"
                    required
                  >
                    @for (item of city(); track $index) {
                    <mat-option [value]="item.nodeId">{{ item.displayName }}</mat-option>
                    }
                  </mat-select>
                  <mat-error>
                    <app-validation [errors]="final.controls['address'].controls['city']" />
                  </mat-error>
                </mat-form-field>

                <mat-form-field>
                  <input matInput [placeholder]="'UserPortal:area' | i18n" formControlName="area" />
                </mat-form-field>
                <mat-form-field>
                  <input
                    matInput
                    [placeholder]="'UserPortal:street' | i18n"
                    formControlName="street"
                  />
                </mat-form-field>
              </div>
              <div class="my-4">
                {{ 'UserPortal:What kind of vehicles' | i18n }}
              </div>
              <div class="mb-4">
                <div class="p-2 my-2 text-white rounded-md shadow-md bg-main_red">
                  <mat-checkbox class="w-full" formControlName="mach" labelPosition="before">
                    <span class="size-8 me-2" [innerHTML]="car_svg('white') | safeHtml"></span>
                    <span class="mt-2">{{ 'UserPortal:Vehicle' | i18n }}</span>
                  </mat-checkbox>
                </div>
              </div>
              <mat-form-field>
                <input
                  matInput
                  [placeholder]="'UserPortal:accountName' | i18n"
                  formControlName="accountName"
                  required=""
                />
                <mat-error>
                  <app-validation [errors]="final.controls['accountName']" />
                </mat-error>
              </mat-form-field>
              <mat-form-field>
                <mat-select
                  matInput
                  [placeholder]="'UserPortal:subscriptionPlan' | i18n"
                  formControlName="subscriptionPlanKey"
                >
                  @for (item of plans(); track $index) {
                  <mat-option [value]="item.key">{{ item.localizedName }}</mat-option>
                  }
                </mat-select>
                <mat-error>
                  <app-validation [errors]="final.controls['subscriptionPlanKey']" />
                </mat-error>
              </mat-form-field>

              <div class="flex justify-end my-2">
                <a class="text-main_blue hover:text-main_perple" (click)="goToPlans()">{{
                  'UserPortal:viewPlans' | i18n
                }}</a>
              </div>

              <mat-label class="px-2">{{
                'UserPortal:subscriptionDurationInMonths' | i18n
              }}</mat-label>
              <div class="flex gap-2">
                <mat-form-field>
                  <mat-select
                    matInput
                    [placeholder]="'UserPortal:subscriptionDurationInMonths' | i18n"
                    formControlName="subscriptionDurationInMonths"
                  >
                    @for (item of months(); track $index) {
                    <mat-option [value]="item"
                      ><span> {{ item }} {{ 'month' | i18n }} </span>
                      @if ( discounts$().get(item) ) {
                      <span class="text-main_blood_red">
                        {{ 'UserPortal:discount' | i18n }}(
                        {{
                          discounts$().get(item).isPercentage
                            ? discounts$().get(item).value * 100
                            : discounts$().get(item).value
                        }}{{ discounts$().get(item).isPercentage ? '%' : ('SP' | i18n) }}) </span
                      >}</mat-option
                    >
                    }
                  </mat-select>
                  <mat-error>
                    <app-validation [errors]="final.controls['subscriptionDurationInMonths']" />
                  </mat-error>
                </mat-form-field>
                <button (click)="getDiscounts()" mat-mini-fab>
                  <mat-icon>autorenew</mat-icon>
                </button>
              </div>
              @if (final.controls ['subscriptionPlanKey'].value!=null) {
              <mat-label class="px-2">{{ 'UserPortal:userCount' | i18n }}</mat-label>
              <mat-form-field>
                <input
                  [type]="'number'"
                  matInput
                  [readonly]="true"
                  formControlName="userCount"
                  [placeholder]="'UserPortal:userCount' | i18n"
                />
              </mat-form-field>
              }

              <div class="flex flex-wrap mb-3">
                <mat-label class="pr-2 w-full">{{ 'UserPortal:trackAccountSms' | i18n }}</mat-label>
                <mat-radio-group
                  [ngModel]="withSms()"
                  [ngModelOptions]="{ standalone: true }"
                  (ngModelChange)="withSms.set($event)"
                >
                  <mat-radio-button [value]="false">
                    {{ 'UserPortal:No' | i18n }}
                  </mat-radio-button>
                  <mat-radio-button [value]="true">
                    {{ 'UserPortal:Yes' | i18n }}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
              @if (withSms()) {
              <mat-label class="px-2">{{ 'UserPortal:smsBundleId' | i18n }}</mat-label>
              <mat-form-field>
                <mat-select
                  matInput
                  [placeholder]="'UserPortal:smsBundleId' | i18n"
                  formControlName="smsBundleId"
                  [required]="withSms()"
                >
                  @for (item of smsBundles(); track $index) {
                  <mat-option [value]="item.id"
                    >{{ item.name }} ( {{ item.messagesCount }} {{ 'message' | i18n }} )
                    {{ item.price }} {{ 'SP' | i18n }}
                  </mat-option>
                  }
                </mat-select>
                <mat-error>
                  <app-validation [errors]="final.controls['smsBundleId']" />
                </mat-error>
              </mat-form-field>
              }
              <mat-label class="px-2">{{ 'UserPortal:promoCode' | i18n }}</mat-label>
              <mat-form-field>
                <input
                  matInput
                  type="text"
                  [placeholder]="'UserPortal:promoCode' | i18n"
                  formControlName="promoCode"
                />
              </mat-form-field>

              @if (excel()) {
              <mat-label class="px-2">{{ 'UserPortal:vehicleCount' | i18n }}</mat-label>
              <div class="flex gap-2">
                <mat-form-field>
                  <input
                    matInput
                    type="number"
                    min="0"
                    [placeholder]="'UserPortal:vehicleCount' | i18n"
                    [ngModel]="vehicleCount()"
                    (ngModelChange)="vehicleCount.set($event)"
                    [ngModelOptions]="{ standalone: true }"
                  />
                </mat-form-field>
              </div>
              <mat-label class="px-2">{{ 'UserPortal:trackersCount' | i18n }}</mat-label>
              <div class="flex gap-2">
                <mat-form-field>
                  <input
                    matInput
                    type="number"
                    min="0"
                    [placeholder]="'UserPortal:trackersCount' | i18n"
                    [ngModel]="trackersCount()"
                    (ngModelChange)="trackersCount.set($event)"
                    [ngModelOptions]="{ standalone: true }"
                  />
                </mat-form-field>
              </div>

              } @if (pro()==true) {
              <div class="my-4">
                {{ 'UserPortal:vehicle Adding method' | i18n }}
              </div>
              <mat-radio-group
                [ngModel]="excel()"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="excel.set($event)"
              >
                <div class="">
                  <mat-radio-button class="example-radio-button" [value]="true">
                    {{ 'UserPortal:add via excel' | i18n }}
                  </mat-radio-button>
                </div>
                <div class="">
                  <mat-radio-button class="example-radio-button" [value]="false">
                    {{ 'UserPortal:add manual' | i18n }}
                  </mat-radio-button>
                </div>
              </mat-radio-group>
              } @if (excel() && fileVehicles.length > 0) {
              <button mat-button mat-flat-button (click)="reupload()">
                {{ 'UserPortal:reuploadExcel' | i18n }}
              </button>
              }
              <div class="mt-2">
                <div>
                  @for (item of final.controls ['subscriptionVehicleInfoCreateDtos'].value; track
                  $index) {
                  <div
                    class="p-4 mb-4 rounded-lg border-2 shadow-xl text-main_gray border-main_gray border-opacity-35"
                  >
                    <div class="flex justify-between items-center">
                      <div class="text-main_dark_blue">
                        {{ 'UserPortal:Vehicle' | i18n }} {{ $index + 1 }}
                      </div>
                      <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
                      <mat-menu #menu>
                        <button mat-menu-item (click)="addVehiclesDialog($index)">
                          <mat-icon>edit</mat-icon> <span>{{ 'edit' | i18n }}</span>
                        </button>
                        <button mat-menu-item (click)="remove($index)">
                          <mat-icon>delete</mat-icon> <span>{{ 'delete' | i18n }}</span>
                        </button>
                      </mat-menu>
                    </div>
                    <div class="my-2">
                      <span>{{ 'UserPortal:licensePlateSubClass' | i18n }}:</span>
                      <span class="text-main_red">
                        {{ item.licensePlateSubClass }}
                      </span>
                    </div>
                    <div class="my-2">
                      <span>{{ 'UserPortal:licensePlateSerial' | i18n }}:</span>
                      <span class="text-main_red">
                        {{ item.licensePlateSerial }}
                      </span>
                    </div>
                    <div class="my-2">
                      <span>{{ 'UserPortal:consumptionRate' | i18n }}:</span>
                      <span class="text-main_red">
                        {{ item.consumptionRate }}{{ 'UserPortal:Km' | i18n }}/{{ totalCapacity
                        }}{{ 'UserPortal:L' | i18n }}
                      </span>
                    </div>
                    <div class="flex items-center my-2">
                      <span>{{ 'UserPortal:color' | i18n }}:</span>
                      <div
                        class="inline-block mx-2 rounded-full size-6"
                        [ngStyle]="{ background: item.color }"
                      ></div>
                    </div>
                  </div>
                  }
                </div>
              </div>
              @if(pro()==false||(fileVehicles.length>0)||!excel()) {
              <div class="mt-2">
                <div class="mb-4">
                  <div class="flex items-center">
                    <button
                      class="p-2 px-4 text-white rounded-lg bg-main_perple me-4"
                      (click)="addVehiclesDialog()"
                    >
                      +
                    </button>
                    <div class="text-main_dark_blue">{{ 'UserPortal:add new vihicle' | i18n }}</div>
                  </div>
                </div>
              </div>
              }
              <div class="grid grid-cols-2 gap-4">
                <button mat-button mat-flat-button [disabled]="!final.valid" (click)="save()">
                  {{ 'UserPortal:confirm' | i18n }}
                </button>
                <button
                  mat-button
                  mat-flat-button
                  class="cancleButton"
                  [routerLink]="'/track-accounts'"
                >
                  {{ 'UserPortal:Cancel' | i18n }}
                </button>
              </div>
              } @if(this.step()==3) {

              <img src="assets/images/excel.svg" class="size-10" alt="" />
              <div>
                <div class="mb-2">{{ 'UserPortal:UploadExcelFile_1' | i18n }}</div>
                <div class="mb-2">{{ 'UserPortal:UploadExcelFile_2' | i18n }}</div>
                <div class="mb-2">{{ 'UserPortal:UploadExcelFile_3' | i18n }}</div>
                <div class="mb-2">{{ 'UserPortal:UploadExcelFile_4' | i18n }}</div>
                <div class="mb-2">
                  {{ 'The Vehicle Count must be' | i18n }} {{ vehicleCount() }}
                </div>
                <div class="mb-2">
                  {{ 'The tracking device Count must be' | i18n }} {{ trackersCount() }}
                </div>
              </div>
              <div class="flex flex-col gap-4 mb-4">
                <a
                  #download
                  href="https://gotrack-flutter.dev05.tatweer.sy/assets/assets/templates/template.xlsx"
                  download
                ></a>
                <button mat-raised-button color="primary" (click)="download.click()">
                  <mat-icon>download</mat-icon>
                  {{ 'UserPortal:DownloadExcelTemplate' | i18n }}
                </button>
                <input
                  type="file"
                  #fileInput
                  style="display: none"
                  (change)="onFileSelected($event)"
                  accept=".xlsx, .xls"
                />
                <button
                  mat-raised-button
                  color="accent"
                  [ngClass]="{ '!text-main_green': fileVehicles.length > 0 }"
                  (click)="fileInput.click()"
                >
                  {{ 'UserPortal:UploadExcelFile' | i18n }}
                </button>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <button
                  mat-button
                  mat-flat-button
                  [disabled]="!final.valid"
                  (click)="setFileData()"
                >
                  {{ 'UserPortal:confirm' | i18n }}
                </button>
                <button mat-button mat-flat-button class="cancleButton" (click)="step.set(2)">
                  {{ 'UserPortal:Cancel' | i18n }}
                </button>
              </div>
              }
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
