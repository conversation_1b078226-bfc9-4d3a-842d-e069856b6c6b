import { Component, inject, input, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatOption, MatSelect, MatSelectModule } from '@angular/material/select';
import { RouterLink } from '@angular/router';
import { SmsBundleRenewalRequestService } from '@proxy/mobile/requests/account-subscription-requests/sms-bundle-renewal-requests';
import { TrackAccountSubscriptionService } from '@proxy/mobile/track-accounts/track-account-subscriptions';
import { TrackAccountSubscriptionDto } from '@proxy/mobile/track-accounts/track-account-subscriptions/dtos';
import { SmsBundleService } from '@proxy/mobile/sms-bundles';
import { SmsBundleDto } from '@proxy/mobile/sms-bundles/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, of, switchMap } from 'rxjs';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { ConfirmationDialogService } from '@shared/components/confirmation-dialog';
import { ConfigStateService } from '@abp/ng.core';

@Component({
  selector: 'app-renew-sms-bundel',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    LanguagePipe,
    RouterLink,
    MatIcon,
    MatSelect,
    MatOption,
  ],
  template: `
    <div class="relative ">
      <mat-icon class="absolute top-4 left-4" [routerLink]="['/track-accounts']"
        >keyboard_arrow_left</mat-icon
      >
      <h2 class="text-center bg-white rounded-xl shadow-lg p-4 text-black ">
        {{ 'SmsBundleRenewal' | i18n }}
      </h2>
    </div>
    <div class="flex flex-col  items-center min-h-screen mt-4">
      <mat-card
        class="p-2 md:p-6  rounded-lg shadow-lg transition-all duration-500 card-blue w-10/12 max-w-md"
      >
        <form [formGroup]="form" class="p-4" (ngSubmit)="onSubmit()">
          <div
            class="bg-main_perple text-white bg-opacity-40 rounded-lg p-4 mb-6 flex justify-center flex-col gap-2"
          >
            <div class="text-center">
              <mat-icon class="size-6"> message</mat-icon>
              <span class=""> {{ 'UserPortal:CurrentsmsBundleCount' | i18n }}</span>
            </div>
            <div class="text-center font-semibold">
              {{ subscription().smsBundleCount }} {{ 'UserPortal:message' | i18n }}
            </div>
          </div>

          <div class="main-fields">
            <mat-label class="pr-2">{{ 'UserPortal:smsBundle' | i18n }}</mat-label>
            <mat-form-field appearance="outline" class="w-full">
              <mat-select
                matInput
                [placeholder]="'UserPortal:smsBundleCount' | i18n"
                formControlName="smsBundleId"
              >
                @for (item of smsBundles(); track $index) {
                <mat-option [value]="item.id">
                  {{ item.name }} ( {{ item.messagesCount }} {{ 'UserPortal:message' | i18n }} )
                  {{ item.price }} {{ 'SP' | i18n }}
                </mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>

          <div class="flex justify-center mt-6">
            <button mat-button mat-flat-button type="submit" [disabled]="!form.valid">
              {{ 'UserPortal:confirm' | i18n }}
            </button>
          </div>
        </form>
      </mat-card>
    </div>
  `,
})
export class RenewSmsBundelComponent {
  private fb = inject(FormBuilder);
  smsBundleRenewalRequestService = inject(SmsBundleRenewalRequestService);
  dialog = inject(MatDialog);
  trackAccountSubscriptionService = inject(TrackAccountSubscriptionService);
  private smsBundleService = inject(SmsBundleService);
  private confirmationDialogService = inject(ConfirmationDialogService);
  configStateService = inject(ConfigStateService);

  id = input<string>();

  subscription = signal<TrackAccountSubscriptionDto>({
    smsBundleCount: 0,
  } as TrackAccountSubscriptionDto);

  smsBundles = signal<SmsBundleDto[]>([]);

  form = this.fb.group({
    smsBundleId: new FormControl<string>('', [Validators.required]),
  });

  ngOnInit(): void {
    this.trackAccountSubscriptionService.getCurrentSubscription().subscribe(val => {
      this.subscription.set(val);
    });

    this.smsBundleService
      .getList({ maxResultCount: 999, skipCount: 0 })
      .pipe(
        map(response => {
          this.smsBundles.set(response.items);
        })
      )
      .subscribe();
  }

  onSubmit() {
    if (this.form.valid) {
      this.smsBundleRenewalRequestService
        .createTempBill({
          smsBundleId: this.form.value.smsBundleId,
        })
        .subscribe(val => {
          const dialogref = openPriceOfferDialog(this.dialog, { ...val, pay: true });
          dialogref
            .pipe(
              switchMap((res: boolean) => {
                if (res == true)
                  return this.confirmationDialogService
                    .open({
                      payConfirm: true,
                    })
                    .pipe(
                      filter(v => {
                        return v;
                      }),
                      switchMap(confirm => {
                        if (res) {
                          return this.smsBundleRenewalRequestService
                            .create({ smsBundleId: this.form.value.smsBundleId })
                            .pipe(
                              filter(() => {
                                return (
                                  this.configStateService.getAll().setting.values[
                                    'GoTrack.TrackAccountRequest.SmsBundleRenewalRequestFatoraPayEnabled'
                                  ] == 'True'
                                );
                              }),
                              switchMap(id => {
                                if (confirm) {
                                  return this.smsBundleRenewalRequestService
                                    .createPayment({
                                      requestId: JSON.parse(id),
                                      language: 'en',
                                      savedCards: true,
                                      callBackUrl: location.origin + 'track-accounts',
                                    })
                                    .pipe(
                                      map(link => {
                                        window.open(link, '_blank');
                                      })
                                    );
                                }
                              })
                            );
                        }
                      })
                    );

                return of(null);
              })
            )
            .subscribe();
        });
    }
  }

  ngOnDestroy(): void {
    localStorage.removeItem('tempTrackAccountId');
  }
}
