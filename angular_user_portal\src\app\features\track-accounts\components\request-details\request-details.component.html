<div>
  <div class="flex justify-between m-4">
    <img src="assets/images/logo/LogoColored.png" class="" alt="" />
    <button class="top-4 end-4" mat-button [routerLink]="['/track-accounts']">
      {{ 'UserPortal:Back' | i18n }}
    </button>
  </div>
  @if (request()) {
  <div class="grid grid-cols-1 lg:grid-cols-[2fr_4fr_1fr] p-8 pt-0 gap-8">
    <div>
      <div class="flex-grow p-8 bg-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg">
        @if ($any(request()).renewSubscriptionRequestStage==stages.Review) {
        <button class="e" mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item (click)="cancle()">
            <span>{{ 'cancle' | i18n }}</span>
          </button>
        </mat-menu>
        }
        <div class="flex">
          <div class="flex-grow me-4">
            <div class="mt-6 mb-6 font-semibold">{{ 'UserPortal:Order Information' | i18n }}</div>
            <div class="grid grid-cols-[auto_2fr] gap-x-4 gap-y-2">
              @for (item of keysValue.renewSubscriptionRequest; track $index) {
              <div class="text-main_gray">
                {{ item.key | i18n }}
              </div>
              <div>{{ item.value | i18n }}</div>
              }
            </div>
          </div>
        </div>
        <div class="flex mt-4 justify-around">
          <button mat-button (click)="openPriceOffer()">
            {{ 'UserPortal:PriceOffer' | i18n }}
          </button>
          @if ($any(request()).renewSubscriptionRequestStage == "Payment") {
          <button mat-flat-button (click)="payment()">
            {{ 'UserPortal:MakePayment' | i18n }}
          </button>
          }
        </div>

        @if (this.type()==requestTypes.RenewSubscription) {
        <div class="flex gap-4 mt-3">
          <button mat-flat-button (click)="showInfoDialog('newVehcicle')">
            {{ 'UserPortal:newVehicles' | i18n }}
          </button>
          <button mat-flat-button (click)="showInfoDialog('removedUsers')">
            {{ 'UserPortal:RemovedUsers' | i18n }}
          </button>
          <button mat-flat-button (click)="showInfoDialog('removedVehicle')">
            {{ 'UserPortal:RemovedVehicles' | i18n }}
          </button>
        </div>
        } @else {
        <button mat-flat-button (click)="showInfoDialog()">
          {{ 'UserPortal:newVehicles' | i18n }}
        </button>
        }
      </div>
    </div>
  </div>
  }
</div>
