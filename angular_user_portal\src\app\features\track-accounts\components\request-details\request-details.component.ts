import { ConfigStateService } from '@abp/ng.core';
import { DatePipe } from '@angular/common';
import { Component, inject, input, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Router, RouterLink } from '@angular/router';
import { RequestService } from '@proxy/mobile/requests';
import { AddVehiclesRequestService } from '@proxy/mobile/requests/add-vehicles-requests';
import { AddVehiclesRequestDto } from '@proxy/mobile/requests/add-vehicles-requests/dtos';
import { RenewSubscriptionRequestService } from '@proxy/mobile/requests/renew-subscription-requests';
import { RenewSubscriptionRequestDetailsDto } from '@proxy/mobile/requests/renew-track-account-subscriptions/dtos';
import { SmsBundleService } from '@proxy/mobile/sms-bundles';
import { SmsBundleDto } from '@proxy/mobile/sms-bundles/dtos';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { RenewSubscriptionRequestStage } from '@proxy/renew-track-account-subscriptions';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import {
  openRelatedItemsDialog,
  relatedVihicleData,
} from '@shared/components/related-items-dialog/related-items-dialog.component';
import { requestTypes } from '@shared/constants/requestTypes';
import { LanguagePipe, LanguageService } from '@ttwr-framework/ngx-main-visuals';
import { filter, firstValueFrom, map, of, switchMap } from 'rxjs';

@Component({
  selector: 'app-request-details',
  standalone: true,
  templateUrl: `./request-details.component.html`,
  imports: [RouterLink, MatButton, LanguagePipe, MatIcon, MatMenuModule],
})
export class RequestDetailsComponent {
  configStateService = inject(ConfigStateService);
  dialog = inject(MatDialog);
  stages = RenewSubscriptionRequestStage;
  requestTypes = requestTypes;

  private router = inject(Router);
  private languageService = inject(LanguageService);
  private smsBundleService = inject(SmsBundleService);
  private requestService = inject(RequestService);
  private renewSubscriptionRequestService = inject(RenewSubscriptionRequestService);
  private addVehiclesRequestService = inject(AddVehiclesRequestService);
  private datePipe = inject(DatePipe);

  request = signal<RenewSubscriptionRequestDetailsDto | AddVehiclesRequestDto | null>(null);
  smsPundel = signal<SmsBundleDto | null>(null);
  id = input.required<string>();
  type = input.required<string>();

  keysValue = {
    renewSubscriptionRequest: [],
  };

  type$ = toObservable(this.type).pipe(
    switchMap(type => {
      if (type == requestTypes.RenewSubscription) {
        return this.handleRenewSubscriptionRequest();
      }
      if (type == requestTypes.AddVehiclesRequest) {
        return this.handleAddVehiclesRequest();
      }
    })
  );
  ngOnInit(): void {
    this.type$.subscribe();
  }
  handleRenewSubscriptionRequest() {
    return this.renewSubscriptionRequestService.get(this.id()).pipe(
      switchMap(request => {
        if (request.smsBundleId) {
          return this.getSmsPundle(request.smsBundleId).pipe(map(() => request));
        } else {
          return of(request);
        }
      }),
      map(v => {
        this.request.set(v);
        this.keysValue.renewSubscriptionRequest = [
          {
            key: 'UserPortal:trackerInstallationLocation',
            value: v.trackerInstallationLocation,
          },
          {
            key: 'UserPortal:subscriptionPlan',
            value: v.subscriptionPlanLocalizedName,
          },
          {
            key: 'UserPortal:subscriptionDurationInMonths',
            value: `${v.subscriptionDurationInMonths}`,
          },
          {
            key: 'UserPortal:smsBundleId',
            value: `${this.smsPundel().name} ( ${this.smsPundel().messagesCount} ) ${
              this.smsPundel().price
            }
                ${this.languageService.translate('SP')}`,
          },
          { key: 'UserPortal:userCount', value: `${v.userCount}` },
          {
            key: 'UserPortal:stage',
            value: `${v.renewSubscriptionRequestStage}`,
          },
          {
            key: 'UserPortal:newTrackVehiclesCount',
            value: `${v.newTrackVehiclesCount} ${this.languageService.translate('vehicle')}`,
          },
          {
            key: 'UserPortal:removeTrackVehiclesCount',
            value: `${v.removeTrackVehiclesCount} ${this.languageService.translate('vehicle')}`,
          },
        ];
      })
    );
  }
  handleAddVehiclesRequest() {
    return this.addVehiclesRequestService.get(this.id()).pipe(
      map(v => {
        this.request.set(v);

        this.keysValue.renewSubscriptionRequest = [
          {
            key: 'UserPortal:trackerInstallationLocation',
            value: v.trackerInstallationLocation,
          },
          {
            key: 'UserPortal:vehicleCount',
            value: `${v.trackVehicles.length} ${this.languageService.translate('vehicle')}`,
          },
          {
            key: 'UserPortal:creationTime',
            value: `${this.datePipe.transform(v.creationTime)}`,
          },
          {
            key: 'UserPortal:lastModificationTime',
            value: `${this.datePipe.transform(v.lastModificationTime)}`,
          },
        ];
      })
    );
  }

  getSmsPundle(id: string) {
    return this.smsBundleService.get(id).pipe(
      map(response => {
        this.smsPundel.set(response);
      })
    );
  }

  payment() {
    const obj = {
      requestId: this.id(),
      language: this.languageService.selectedLanguage(),
      savedCards: true,
      callBackUrl: '',
    };
    let obs = this.renewSubscriptionRequestService
      .createPayment(obj)
      .pipe(
        filter(
          () =>
            this.configStateService.getAll().setting.values[
              'GoTrack.TrackAccountRequest.RenewTrackAccountSubscriptionFatoraPayEnabled'
            ] == 'True'
        )
      );
    obs
      .pipe(
        map(link => {
          window.open(link, '_blank');
        })
      )
      .subscribe();
  }

  openPriceOffer() {
    openPriceOfferDialog(this.dialog, { id: this.id() });
  }

  cancle() {
    this.requestService.cancel(this.id()).subscribe(() => {
      this.router.navigate(['/track-accounts']);
    });
  }

  async showInfoDialog(key?: 'newVehcicle' | 'removedVehicle' | 'removedUsers') {
    let config: relatedVihicleData = {
      dialog: this.dialog,
      hideAction: true,
      type: 'VEHICLE',
      items: [],
    };
    if (this.type() == requestTypes.AddVehiclesRequest) {
      const req = this.request() as AddVehiclesRequestDto;
      config.items = req.trackVehicles;
      config.header = 'UserPortal:newVehicles';
    }
    if (this.type() == requestTypes.RenewSubscription) {
      if (key == 'newVehcicle') {
        const vehicles = await firstValueFrom(
          this.renewSubscriptionRequestService.getListNewVehicles(this.id(), {
            maxResultCount: 999,
            skipCount: 0,
          })
        );
        config.items = vehicles.items;
        config.header = 'UserPortal:newVehicles';
      }
      if (key == 'removedVehicle') {
        const vehicles = await firstValueFrom(
          this.renewSubscriptionRequestService.getListRemovedVehicles(this.id(), {
            maxResultCount: 999,
            skipCount: 0,
          })
        );
        config.items = vehicles.items;
        config.header = 'UserPortal:RemovedVehicles';
      }
      if (key == 'removedUsers') {
        const users = await firstValueFrom(
          this.renewSubscriptionRequestService.getListRemovedUsers(this.id(), {
            maxResultCount: 999,
            skipCount: 0,
          })
        );
        config.items = users.items;
        config.type = 'Observation';
        config.header = 'UserPortal:RemovedUsers';
      }
    }
    openRelatedItemsDialog(config);
  }
}
