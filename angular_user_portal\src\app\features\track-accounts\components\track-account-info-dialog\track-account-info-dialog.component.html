<div class="p-6 text-sm bg-white rounded-lg shadow-lg">
  <h2 class="font-semibold text-indigo-600">{{ 'UserPortal:accountInfo' | i18n }}</h2>

  <hr class="my-3" />

  @if (trackAccount()) {
  <div class="space-y-2">
    <div class="grid grid-cols-2 gap-y-2 gap-x-4 text-start ps-2">
      <div class="text-main_gray">{{ 'UserPortal:subscriptionPlan' | i18n }}</div>
      <div>{{ trackAccount().subscriptionPlanLocalizedName }}</div>
      <div class="text-main_gray">{{ 'UserPortal:smsBundleCount' | i18n }}</div>
      <div>{{ trackAccount().smsBundleCount }}</div>
      <div class="text-main_gray">{{ 'UserPortal:fromDate' | i18n }}</div>
      <div>{{ trackAccount().from | date : 'YYYY-MM-dd' }}</div>
      <div class="text-main_gray">{{ 'UserPortal:toDate' | i18n }}</div>
      <div>{{ trackAccount().to | date : 'YYYY-MM-dd' }}</div>
      <div class="text-main_gray">
        {{ 'UserPortal:currentTrackAccountSubscriptionPlan' | i18n }}
      </div>
      <div>{{ trackAccount().subscriptionPlanLocalizedName }}</div>
      <div class="text-main_gray">{{ 'UserPortal:userCount' | i18n }}</div>
      <div>{{ trackAccount().userCount }}</div>
      <div class="text-main_gray">{{ 'UserPortal:state' | i18n }}</div>
      <div>{{ trackAccount().state | i18n }}</div>
    </div>
  </div>
  }

  <div class="flex justify-center mt-4 text-sm">
    <button mat-button mat-flat-button (click)="closeDialog()">
      {{ 'UserPortal:ok' | i18n }}
    </button>
  </div>
</div>
