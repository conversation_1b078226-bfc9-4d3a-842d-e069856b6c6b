import { DatePipe } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TrackAccountSubscriptionService } from '@proxy/mobile/track-accounts/track-account-subscriptions';
import { TrackAccountSubscriptionDetailDto } from '@proxy/mobile/track-accounts/track-account-subscriptions/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-track-account-info-dialog',
  standalone: true,
  templateUrl: `./track-account-info-dialog.component.html`,
  imports: [MatButton, LanguagePipe, DatePipe],
})
export class TrackAccountInfoDialogComponent {
  dialogRef = inject(MatDialogRef<TrackAccountInfoDialogComponent>);
  data: { id: string } = inject(MAT_DIALOG_DATA);
  trackAccountSubscription = inject(TrackAccountSubscriptionService);
  trackAccount = signal<TrackAccountSubscriptionDetailDto | null>(null);

  ngOnInit(): void {
    this.getInfo();
  }
  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.
    localStorage.removeItem('tempTrackAccountId');
  }

  getInfo() {
    this.trackAccountSubscription.getCurrentSubscription().subscribe(res => {
      this.trackAccount.set(res);
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }
}
