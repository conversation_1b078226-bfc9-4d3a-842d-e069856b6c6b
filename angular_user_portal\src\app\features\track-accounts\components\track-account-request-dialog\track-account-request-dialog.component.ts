import { ConfigStateService } from '@abp/ng.core';
import { Component, DestroyRef, inject, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { Router, RouterLink } from '@angular/router';
import { PaymentService } from '@proxy/mobile/payments';
import { RequestDto, RequestService } from '@proxy/mobile/requests';
import { SmsBundleRenewalRequestService } from '@proxy/mobile/requests/account-subscription-requests/sms-bundle-renewal-requests';
import { AddVehiclesRequestService } from '@proxy/mobile/requests/add-vehicles-requests';
import { RenewSubscriptionRequestService } from '@proxy/mobile/requests/renew-subscription-requests';
import { ConfirmationDialogService } from '@shared/components/confirmation-dialog';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { requestTypes } from '@shared/constants/requestTypes';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, of, switchMap } from 'rxjs';

@Component({
  selector: 'app-track-account-request-dialog',
  standalone: true,
  templateUrl: './track-account-request-dialog.component.html',
  imports: [MatButtonModule, LanguagePipe, MatCardModule, MatIcon, MatCheckbox],
})
export class TrackAccountrequestDialogComponent {
  dialogRef = inject(MatDialogRef<TrackAccountrequestDialogComponent>);
  requestService = inject(RequestService);
  pymentService = inject(PaymentService);
  destroyRef = inject(DestroyRef);
  confirmationDialogService = inject(ConfirmationDialogService);
  requestType = requestTypes;

  request = signal<RequestDto[]>([]);
  ides = signal<Set<string>>(new Set());
  private dialog = inject(MatDialog);

  ngOnInit(): void {
    this.getRequests();
  }

  getRequests() {
    this.requestService
      .getListOfTrackAccountIdByInput({
        maxResultCount: 999,
        skipCount: 0,
        status: [],
        types: [],
      })
      .pipe(
        map(val => {
          this.request.set(val.items);
        })
      )
      .subscribe();
  }
  icons = {
    SmsBundleRenewalRequest: 'sms.svg',
    RenewSubscription: 'تجديد اشتراك.svg',
    AddVehiclesRequest: 'زيادة المركبات.svg',
  };

  router = inject(Router);
  configStateService = inject(ConfigStateService);
  smsBundleRenewalRequestService = inject(SmsBundleRenewalRequestService);
  renewSubscriptionRequestService = inject(RenewSubscriptionRequestService);
  addVehiclesRequestService = inject(AddVehiclesRequestService);

  servicesSelector = {
    SmsBundleRenewalRequest: {
      service: this.smsBundleRenewalRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.SmsBundleRenewalRequestFatoraPayEnabled',
    },
    RenewSubscription: {
      service: this.renewSubscriptionRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.RenewTrackAccountSubscriptionFatoraPayEnabled',
    },
    AddVehiclesRequest: {
      service: this.addVehiclesRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.AddVehiclesRequestFatoraPayEnabled',
    },
  } as const;

  pay(order: RequestDto) {
    const dialogref = openPriceOfferDialog(this.dialog, { id: order.id, pay: true });
    dialogref
      .pipe(
        filter(v => !!v),
        switchMap(id => {
          return this.confirmationDialogService
            .open({
              payConfirm: true,
            })
            .pipe(
              filter(v => {
                return v;
              }),
              filter(
                () =>
                  this.configStateService.getAll().setting.values[
                    this.servicesSelector[order.type].allowPayment
                  ] == 'True'
              ),
              switchMap(val => {
                if (val) {
                  return this.servicesSelector[order.type].service
                    .createPayment({
                      requestId: order.id,
                      language: 'en',
                      savedCards: true,
                      callBackUrl: location.origin + 'track-accounts',
                    })
                    .pipe(
                      map((link: string) => {
                        window.open(link, '_blank');
                      })
                    );
                } else {
                  return of(null);
                }
              })
            );
        })
      )
      .subscribe();
  }

  toggle(order: RequestDto) {
    if (this.ides().has(order.id)) {
      this.ides().delete(order.id);
    } else {
      if (
        this.configStateService.getAll().setting.values[
          this.servicesSelector[order.type].allowPayment
        ] == 'True'
      )
        this.ides().add(order.id);
    }
  }
  paySelected() {
    return this.pymentService
      .payMultipleRequests({
        requestIds: [...this.ides()],
        language: 'en',
        savedCards: true,
        callBackUrl: location.origin + 'track-accounts',
      })
      .pipe(
        map((link: string) => {
          window.open(link, '_blank');
        })
      )
      .subscribe();
  }
  remove(order: RequestDto) {
    this.confirmationDialogService
      .open({
        title: 'UserPortal:DeleteConfirmation',
        message: 'UserPortal:ConfirmMissage',
        confirmText: 'UserPortal:Delete',
        cancelText: 'UserPortal:Cancel',
        confirmButtonColor: 'warn',
      })
      .subscribe(status => {
        if (status === true) {
          this.requestService.cancel(order.id).subscribe(() => {
            this.getRequests();
          });
        }
      });
  }

  requestDetails(order: any) {
    this.dialogRef.close(true);
    this.router.navigate(['/track-accounts/requestDetails', order.type, order.id]);
  }
}
