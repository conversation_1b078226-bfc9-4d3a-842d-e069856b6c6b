<div>
  <div class="grid grid-flow-col justify-between items-center mx-8 my-4">
    <div class="flex gap-2 items-center">
      <img src="assets/images/logo/LogoColored.png" class="flex-grow" alt="" />
      <div class="">
        <button mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>language</mat-icon>
        </button>
        <mat-menu #menu>
          <button mat-menu-item (click)="changeLang('ar')">العربية</button>
          <button mat-menu-item (click)="changeLang('en')">English</button>
        </mat-menu>
      </div>
    </div>
    <div class="flex items-center h-full">
      <button (click)="back()" mat-button>{{ 'UserPortal:Logout' | i18n }}</button>
    </div>
  </div>

  <div class="flex justify-center items-center h-full">
    <div
      class="p-2 mx-1 w-full bg-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg md:mx-6 md:p-8 lg:w-1/2"
    >
      <div class="flex justify-center mb-4">
        <div class="flex flex-row items-center">
          <button
            class="p-2 px-4 text-white rounded-lg bg-main_perple me-4"
            [routerLink]="'create'"
          >
            +
          </button>
          <div>{{ 'UserPortal:create new subscription' | i18n }}</div>
        </div>
      </div>
      <div class="grid grid-cols-1 gap-y-4 justify-center justify-items-center">
        @for (item of accounts(); track $index) {
        <div
          class="rounded-md cursor-pointer md:w-2/3 bg-main_light_gray md:p-4 w-full"
          (click)="setTrackAccount(item)"
        >
          <div class="flex relative">
            <div class="p-2 m-2 bg-white rounded-full h-fit">
              <img src="assets/images/svg/car.svg" class="size-6 car" alt="" />
            </div>
            <div>
              <div class="p-2">
                {{ 'UserPortal:name' | i18n }} :
                <a>{{ item.name }}</a>
              </div>
              <div class="grid grid-cols-2 gap-y-2 gap-x-4 text-start ps-2">
                <div class="text-main_gray">{{ 'UserPortal:account type' | i18n }}</div>
                <div>{{ item.accountType | i18n }}</div>
                <div class="text-main_gray">{{ 'UserPortal:associationType' | i18n }}</div>
                <div>{{ item.userTrackAccountAssociation.associationType | i18n }}</div>
              </div>
            </div>
            <div class="absolute top-2 end-2">
              <img
                src="assets/images/sms/info.svg"
                (click)="openInfo(item.id); $event.stopPropagation()"
                class="size-6"
                alt=""
              />
            </div>
          </div>
          @if (item.userTrackAccountAssociation.associationType==AssociationType.Owner) {
          <div class="grid grid-cols-3 border-gray-300 divide-x divide-gray-300 border-y">
            @for (action of actions(); track $index) {
            <a
              class="flex flex-col items-center px-2 my-4"
              (click)="exec(action.name, item); $event.stopPropagation()"
            >
              <img class="size-10" [src]="'/assets/images/sms/' + action.icon" alt="" />
              <div class="mt-2 text-xs text-center">
                {{ action.name | i18n }}
              </div>
            </a>
            }
          </div>
          <div class="flex justify-center m-3">
            <button
              mat-flat-button
              class="!bg-main_sky_blue"
              (click)="openOredrs(item.id); $event.stopPropagation()"
            >
              {{ 'UserPortal:Requests' | i18n }}
            </button>
          </div>
          }
        </div>
        }
        <div class="mt-4 w-2/3">
          <button
            class="w-full !bg-main_perple !rounded-lg"
            mat-button
            mat-flat-button
            [routerLink]="['..', 'subscription-requests']"
          >
            {{ 'UserPortal:review subscription status' | i18n }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
