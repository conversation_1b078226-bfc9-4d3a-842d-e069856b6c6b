import { DatePipe, NgClass } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { Router, RouterLink } from '@angular/router';
import { TrackAccountService } from '@proxy/mobile/track-accounts';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';
import { TrackAccountInfoDialogComponent } from './components/track-account-info-dialog/track-account-info-dialog.component';
import { TrackAccountDto } from '@proxy/mobile/track-accounts/dtos';
import { TrackAccountrequestDialogComponent } from './components/track-account-request-dialog/track-account-request-dialog.component';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { SessionStateService } from '@abp/ng.core';
import { AssociationType } from '@shared/constants/enums';
import { logout } from '@shared/functions/logout';

@Component({
  selector: 'app-track-accounts',
  standalone: true,
  templateUrl: `./track-accounts.component.html`,
  imports: [RouterLink, MatButton, LanguagePipe, MatIcon, MatMenuModule, MatIconButton],
})
export class TrackAccountsComponent {
  AssociationType = AssociationType;
  trackAccountService = inject(TrackAccountService);
  router = inject(Router);
  dialog = inject(MatDialog);
  private session = inject(SessionStateService);

  accounts = toSignal(
    this.trackAccountService.getList({ maxResultCount: 1000, skipCount: 0 }, {}).pipe(
      map(val => {
        return val.items;
      })
    )
  );

  actions = signal([
    { name: 'sms pundle', icon: 'sms.svg' },
    { name: 'renew subscription', icon: 'تجديد اشتراك.svg' },
    { name: 'increace vehicle', icon: 'زيادة المركبات.svg' },
  ]);

  setTrackAccount(track: TrackAccountDto) {
    localStorage.setItem('trackAccountId', track.id);
    localStorage.setItem(
      'AssociationType',
      track.userTrackAccountAssociation.associationType == AssociationType.Owner ? 'Ow' : 'O'
    );
    this.router.navigate(['/main']);
  }

  smsPundle(id: string) {
    this.router.navigate(['track-accounts', 'sms-bundle-renewal', id]);
  }
  renewSubscription(id: string) {
    this.router.navigate(['track-accounts', 'renew-subscription', id]);
  }
  increaceVehicle(id: string) {
    this.router.navigate(['track-accounts', 'increase-vehicle', id]);
  }

  openInfo(accountId: string) {
    localStorage.setItem('tempTrackAccountId', accountId);
    this.dialog.open(TrackAccountInfoDialogComponent, {
      data: { id: accountId },
    });
  }

  exec(name: string, item: TrackAccountDto) {
    localStorage.setItem('tempTrackAccountId', item.id);
    switch (name) {
      case 'sms pundle':
        this.smsPundle(item.id);
        break;
      case 'renew subscription':
        this.renewSubscription(item.id);
        break;
      case 'increace vehicle':
        this.increaceVehicle(item.id);
        break;
      default:
        console.warn('Unknown action:', name);
    }
  }

  openOredrs(id: string) {
    localStorage.setItem('tempTrackAccountId', id);
    const dialogRef = this.dialog.open(TrackAccountrequestDialogComponent, {
      data: { id: id },
      height: '80%',
    });
    dialogRef.afterClosed().subscribe(v => {
      if (!v) {
        localStorage.removeItem('tempTrackAccountId');
      }
    });
  }
  changeLang(language: 'ar' | 'en') {
    this.session.setLanguage(language);
  }

  back() {
    this.router.navigate(['/']);
    logout();
  }
}
