import type { GetUserLocaleDto, SetUserLocaleDto } from './dtos/models';
import type { GetMobileUserProfileDto, MobileUserUpdateProfileDto, UpdateMobileUserEmailDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class MobileIdentityUserService {
  apiName = 'Default';
  

  delete = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/mobileIdentityUser',
    },
    { apiName: this.apiName,...config });
  

  getLocale = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, GetUserLocaleDto>({
      method: 'GET',
      url: '/api/app/mobileIdentityUser/locale',
    },
    { apiName: this.apiName,...config });
  

  getProfile = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, GetMobileUserProfileDto>({
      method: 'GET',
      url: '/api/app/mobileIdentityUser/profile',
    },
    { apiName: this.apiName,...config });
  

  getProfilePicture = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, Blob>({
      method: 'GET',
      responseType: 'blob',
      url: '/api/app/mobileIdentityUser/profilePicture',
    },
    { apiName: this.apiName,...config });
  

  setLocale = (input: SetUserLocaleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/mobileIdentityUser/setLocale',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  updateEmail = (input: UpdateMobileUserEmailDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/mobileIdentityUser/email',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  updateProfile = (profileDto: MobileUserUpdateProfileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: '/api/app/mobileIdentityUser/profile',
      body: profileDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
