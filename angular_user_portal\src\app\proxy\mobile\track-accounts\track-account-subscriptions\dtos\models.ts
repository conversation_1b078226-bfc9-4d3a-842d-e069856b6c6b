import type { FullAuditedEntityDto } from '@abp/ng.core';
import type { TrackAccountSubscriptionState } from '../../../../track-accounts/track-account-subscription-state.enum';
import type { SmsBundleDto } from '../../../sms-bundles/dtos/models';

export interface TrackAccountSubscriptionDetailDto extends FullAuditedEntityDto<string> {
  subscriptionPlanKey?: string;
  subscriptionPlanLocalizedName?: string;
  trackAccountId?: string;
  userCount: number;
  currentUserCount: number;
  currentActiveUserCount: number;
  smsBundleCount: number;
  from?: string;
  to?: string;
  state: TrackAccountSubscriptionState;
  lastNotificationAt?: string;
  smsBundle: SmsBundleDto;
  trackerInstallationLocation?: string;
  remainingSubscriptionDurationInMonth: number;
  subscriptionDurationInMonth: number;
}

export interface TrackAccountSubscriptionDto extends FullAuditedEntityDto<string> {
  subscriptionPlanKey?: string;
  subscriptionPlanLocalizedName?: string;
  trackAccountId?: string;
  userCount: number;
  smsBundleCount: number;
  from?: string;
  to?: string;
  state: TrackAccountSubscriptionState;
  lastNotificationAt?: string;
}
