<div class="confirmation-dialog">
  <div mat-dialog-title class="dialog-title">
    <div class="title-content">
      <h2>{{ data.title | i18n }}</h2>
    </div>
  </div>

  @if (data.payConfirm) {
  <div mat-dialog-content class="dialog-content">
    <p class="dialog-message !mb-4">{{ 'UserPortal:payDialogMessage1' | i18n }}</p>

    <p class="dialog-message !mb-4">{{ 'UserPortal:payDialogMessage2' | i18n }} 0949333701</p>

    <div class="flex gap-4 justify-center">
      <button class="!rounded-3xl" mat-mini-fab href="tel:0949333701">
        <mat-icon>phone</mat-icon>
      </button>
      <button class="!rounded-3xl" mat-mini-fab href="//api.whatsapp.com/send?phone=0949333701">
        <img src="/assets/images/svg/whatsapp-about.svg" alt="" />
      </button>
    </div>
  </div>
  } @else {
  <div mat-dialog-content class="dialog-content">
    <p class="dialog-message">{{ data.message | i18n }}</p>
  </div>
  }

  <div mat-dialog-actions class="dialog-actions">
    <button mat-flat-button (click)="onCancel()" class="cancleButton">
      {{ data.cancelText | i18n }}
    </button>
    <button
      mat-flat-button
      [color]="data.confirmButtonColor"
      (click)="onConfirm()"
      class="confirm-button"
    >
      {{ data.confirmText | i18n }}
    </button>
  </div>
</div>
