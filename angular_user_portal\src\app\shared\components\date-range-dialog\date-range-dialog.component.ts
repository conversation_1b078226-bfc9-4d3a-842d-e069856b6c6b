import { Component, inject } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import {
  fields,
  LanguagePipe,
  model,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';

export function last30DValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }

    const selectedDate = new Date(new Date(control.value).setSeconds(0));
    const now = new Date(new Date().setMilliseconds(0));
    const twentyFourHoursAgo = new Date(now.setSeconds(0) - 30 * 24 * 60 * 60 * 1000);
    if (selectedDate < twentyFourHoursAgo) {
      return {
        d30: {
          error: true,
        },
      };
    }

    return null;
  };
}
export function FutureDateValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }

    const selectedDate = new Date(control.value);
    const now = new Date();

    if (+selectedDate > +now) {
      return {
        futureDate: {
          error: true,
        },
      };
    }

    return null;
  };
}

// Custom validator for date range (from date should be before to date)
export function dateRangeValidator(): ValidatorFn {
  const fromDateField = 'fromDate';
  const toDateField = 'toDate';
  return (formGroup: AbstractControl): ValidationErrors | null => {
    let fromDate = formGroup.get(fromDateField)?.value;
    let toDate = formGroup.get(toDateField)?.value;
    if (formGroup && formGroup.parent) {
      fromDate = formGroup.parent.get(fromDateField)?.value;
      toDate = formGroup.parent.get(toDateField)?.value;
    }

    if (!fromDate || !toDate) {
      return null; // Don't validate if either date is missing
    }

    const from = new Date(fromDate);
    const to = new Date(toDate);

    if (from > to) {
      return {
        dateRange: {
          error: true,
        },
      };
    }

    return null;
  };
}
export function h24Validator(): ValidatorFn {
  const fromDateField = 'fromDate';
  const toDateField = 'toDate';
  return (formGroup: AbstractControl): ValidationErrors | null => {
    let fromDate = formGroup.get(fromDateField)?.value;
    let toDate = formGroup.get(toDateField)?.value;
    if (formGroup && formGroup.parent) {
      fromDate = formGroup.parent.get(fromDateField)?.value;
      toDate = formGroup.parent.get(toDateField)?.value;
    }

    if (!fromDate || !toDate) {
      return null; // Don't validate if either date is missing
    }

    const from = new Date(new Date(fromDate).setMilliseconds(0)).setSeconds(0);
    const to = new Date(new Date(toDate).setMilliseconds(0)).setSeconds(0);
    const now = new Date(new Date().setMilliseconds(0)).setSeconds(0);

    if (to - from > 1000 * 60 * 60 * 24) {
      return {
        h24: {
          error: true,
        },
      };
    }

    return null;
  };
}

export interface DateRangeDialogData {
  title?: string;
  fromDate?: Date;
  toDate?: Date;
  fromLabel?: string;
  toLabel?: string;
}

export interface DateRangeResult {
  fromDate: Date;
  toDate: Date;
}
@Component({
  selector: 'app-date-range-dialog',
  standalone: true,
  imports: [TtwrFormComponent, LanguagePipe],
  styles: [
    `
      :host {
        ::ng-deep ttwr-input {
          margin-bottom: 20px;
        }
      }
    `,
  ],
  template: `
    <div class="m-4">
      <div class="m-2 text-main_perple">
        {{ 'Please select a time range of up to 24 hours within the last 30 days.' | i18n }}
      </div>
      <ttwr-form [config]="config" />
    </div>
  `,
})
export class DateRangeDialogComponent {
  dialogRef = inject(MatDialogRef<DateRangeDialogComponent>);
  data = inject<DateRangeDialogData>(MAT_DIALOG_DATA);

  config = model({
    fromDate: fields.datetime(),
    toDate: fields.datetime(),
  })
    .select({
      fromDate: true,
      toDate: true,
    })
    .form({
      fields: {
        fromDate: {
          validators: [
            requiredValidator,
            {
              name: 'futureDate',
              message: 'the date can`t be in the future',
              validator: FutureDateValidator(),
            },
            {
              name: 'h24',
              message: 'Date must be within 24 hours',
              validator: h24Validator(),
            },
            {
              name: 'd30',
              message: 'Date must be within the last 30 days',
              validator: last30DValidator(),
            },
            {
              name: 'dateRange',
              message: 'From date must be before to date',
              validator: dateRangeValidator(),
            },
          ],
        },
        toDate: {
          validators: [
            requiredValidator,
            {
              name: 'futureDate',
              message: 'the date can`t be in the future',
              validator: FutureDateValidator(),
            },
            {
              name: 'h24',
              message: 'Date must be within 24 hours',
              validator: h24Validator(),
            },
            {
              name: 'dateRange',
              message: 'To date must be after from date',
              validator: dateRangeValidator(),
            },
          ],
        },
      },
      submitAction: {
        matButtonType: 'flat',
        onSubmit: (value: any) => {
          const result: DateRangeResult = {
            fromDate: new Date(new Date(value.fromDate).setSeconds(0)),
            toDate: new Date(new Date(value.toDate).setSeconds(0)),
          };
          this.dialogRef.close(result);
        },
      },
      actions: [
        {
          matButtonType: 'raised',
          label: 'Cancel',
          delegateFunc: () => this.dialogRef.close(),
        },
      ],
    });
}

export const openDateRangeDialog = (dialog: MatDialog, dialogData?: DateRangeDialogData) => {
  return dialog
    .open(DateRangeDialogComponent, {
      width: '20rem',
      data: { ...dialogData },
    })
    .afterClosed();
};
