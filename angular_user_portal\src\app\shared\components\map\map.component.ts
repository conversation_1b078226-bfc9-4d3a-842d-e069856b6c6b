import { Component, computed, input, output, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { LeafletModule } from '@bluehalo/ngx-leaflet';
import { LeafletDrawModule } from '@bluehalo/ngx-leaflet-draw';
import {
  divIcon,
  DrawEvents,
  featureGroup,
  FeatureGroup,
  icon,
  LatLng,
  latLng,
  LatLngTuple,
  Layer,
  LayerGroup,
  Map,
  MapOptions,
  marker,
  polyline,
  tileLayer,
} from 'leaflet';

import { map, skip } from 'rxjs';

export interface changeNodeDto {
  event: any;
  layers: FeatureGroup;
}
export const mapCenter = [33.51393731431839, 36.276426315307624];
@Component({
  selector: 'app-map',
  template: `
    <div
      class="h-full"
      leaflet
      [leafletOptions]="options$()"
      [leafletZoom]="zoom$()"
      [leafletCenter]="center$()"
      (leafletMapReady)="setMap($event)"
    >
      @if (showDraw()) {
      <div
        leafletDraw
        [leafletDrawOptions]="drawOptions()"
        (leafletDrawCreated)="onDrawCreated($event)"
      ></div>
      }
      <div [leafletLayer]="drawnItems"></div>
    </div>
  `,
  standalone: true,
  imports: [LeafletModule, LeafletDrawModule],
})
export class MapComponent {
  LayerChange = input<boolean>(true);

  nodesId: number | null = null;
  linesId: number | null = null;
  LayerChange$ = toObservable(this.LayerChange).pipe(
    takeUntilDestroyed(),
    skip(1),
    map(data => {
      this.changeLayer();
    })
  );

  nodes = input<Layer[]>();
  nodes$ = toObservable(this.nodes).pipe(
    takeUntilDestroyed(),
    map(data => {
      if (!data) {
        return;
      }
      if (this.nodesId) {
        this.drawnItems.removeLayer(this.nodesId);
      }
      if (data.length > 0) {
        const group = new LayerGroup(data);
        this.drawnItems.addLayer(group);
        this.nodesId = this.drawnItems.getLayerId(group);
      }
    })
  );

  line = input<Layer[]>();
  line$ = toObservable(this.line).pipe(
    takeUntilDestroyed(),
    map(data => {
      if (!data) {
        return;
      }
      if (this.linesId) {
        this.drawnItems.removeLayer(this.linesId);
      }
      if (data.length > 0) {
        const group = new LayerGroup(data);
        this.drawnItems.addLayer(group);
        this.linesId = this.drawnItems.getLayerId(group);
      }
    })
  );

  draw = input<{
    polyline?: boolean;
    marker?: boolean;
    circlemarker?: boolean;
    rectangle?: boolean;
    polygon?: boolean;
    circle?: boolean;
  }>();
  draw$ = toObservable(this.draw).pipe(
    map(data => {
      if (data) {
        this.drawOptions.update(value => {
          return {
            ...value,
            draw: {
              polyline: data.polyline,
              marker: data.marker,
              circlemarker: data.circlemarker,
              rectangle: data.rectangle,
              polygon: data.polygon,
              circle: data.circle,
            },
          };
        });
      }
    })
  );
  showDraw = input(false);
  change_nodes = output<changeNodeDto>();

  drawnItems: FeatureGroup = featureGroup();

  drawOptions = signal<any>({
    position: 'topright',
    draw: {
      polyline: false,
      marker: false,
      circlemarker: false,
      rectangle: false,
      polygon: false,
      circle: false,
    },
    edit: false,
    remove: false,
  });

  public onDrawCreated(e: DrawEvents.Created) {
    const layer = e.layer;
    this.drawnItems.addLayer(layer);
    this.change_nodes.emit({ event: e, layers: this.drawnItems });
  }

  GoogleSateliteLyr = tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
    maxZoom: 20,
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
  });
  OpenStreetLyr = tileLayer('http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 22,
    attribution: '..',
  });

  baseOptions = signal<MapOptions>({
    layers: [this.OpenStreetLyr],
    zoom: 10,
    center: latLng(mapCenter[0], mapCenter[1]),
    zoomControl: false,
  });
  options = input<MapOptions>({
    layers: [this.OpenStreetLyr],
    zoom: 10,
    center: latLng(mapCenter[0], mapCenter[1]),
  });
  changeOptions = signal<MapOptions>({});
  options$ = computed<MapOptions>(() => {
    const o = {
      ...this.baseOptions(),
      ...this.options(),
      ...this.changeOptions(),
    };
    return o;
  });
  zoom$ = computed(() => {
    return this.options$().zoom;
  });
  center$ = computed<LatLng>(() => {
    return this.options$().center as LatLng;
  });

  map: Map;
  setMap(map: Map) {
    this.map = map;
  }

  changeLayer() {
    if (this.map.hasLayer(this.GoogleSateliteLyr)) {
      this.map.removeLayer(this.GoogleSateliteLyr);
      this.map.addLayer(this.OpenStreetLyr);
    } else {
      this.map.removeLayer(this.OpenStreetLyr);
      this.map.addLayer(this.GoogleSateliteLyr);
    }
  }

  ngOnInit(): void {
    this.nodes$.subscribe();
    this.draw$.subscribe();
    this.line$.subscribe();
    this.LayerChange$.subscribe();
  }
}

export const CustomMarker = (point: {
  latlang: LatLngTuple;
  icon: string;
  popup?: () => HTMLElement;
}) => {
  const r = marker(point.latlang, {
    icon: divIcon({
      iconSize: [36, 36],
      className: 'bg-transparent border-none',
      popupAnchor: [0, 130],
      html: point.icon,
    }),
  });
  if (point.popup) {
    r.bindPopup(point.popup());
  }
  return r;
};

export const CustomLine = (data: { line: any; color?: string }) => {
  let line = data.line;
  const color = data.color ?? 'black';
  const r = polyline(line, { color: color });
  return r;
};

export const CustomDrawMarker = icon({
  iconUrl: '/assets/images/map-marker.svg',
  iconSize: [38, 95],
  iconAnchor: [19, 65],
});
