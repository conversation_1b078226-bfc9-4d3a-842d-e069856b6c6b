import { DatePipe, formatN<PERSON>ber, NgClass } from '@angular/common';
import { Component, computed, inject, LOCALE_ID, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { BillService } from '@proxy/mobile/payments/bills';
import { BillDto, BillLineItemDto } from '@proxy/mobile/payments/bills/dtos';
import { BillStatus } from '@proxy/payments/bills';
import { PricingType, pricingTypeOptions } from '@proxy/payments/pricing-items';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom, Observable } from 'rxjs';

interface BillLineItemDtoWithPrice extends BillLineItemDto {
  price: number;
  PriceWithDiscount?: number;
  unitPriceName?: string;
  quantityName?: string;
}
@Component({
  selector: 'app-price-offer-dialog',
  standalone: true,
  templateUrl: `./price-offer-dialog.component.html`,
  imports: [MatButton, LanguagePipe, NgClass, DatePipe, MatIcon],
})
export class PriceOfferDialogComponent {
  dialogRef = inject(MatDialogRef<PriceOfferDialogComponent>);
  data: BillDto & { id: string; pay: boolean } = inject(MAT_DIALOG_DATA);
  billService = inject(BillService);
  private locale = inject(LOCALE_ID);

  filter$ = signal('all');
  details$ = signal(false);
  filters = signal<string[]>(['all', ...pricingTypeOptions.map(x => x.value)]);

  pricingTypeOptionsCounts = signal<any>({});

  bills = signal<BillDto & { billLineItems: BillLineItemDtoWithPrice[] }>({
    status: BillStatus.Draft,
    appliedDiscounts: [],
    billLineItems: [],
    total: 0,
    totalBeforeDiscounts: 0,
  });

  filteredBillLineItems$ = computed<BillLineItemDtoWithPrice[]>(() => {
    const filter = this.filter$();
    return this.bills().billLineItems.filter(x => {
      if (filter === 'all') {
        return true;
      }
      return x.pricingType === filter;
    }) as BillLineItemDtoWithPrice[];
  });

  formatNumber(value: number | null | undefined, digitsInfo: string = '1.0-2'): string {
    if (value == null || value == undefined) {
      return '0';
    }
    return formatNumber(value, this.locale, digitsInfo);
  }

  ngOnInit(): void {
    this.getbill();
  }

  async getbill() {
    let bill: BillDto = { ...this.data };
    if (this.data.id) {
      bill = await firstValueFrom(this.billService.getBill(this.data.id));
    }
    const r = {};
    const onceItems: {
      'PricingItemKeys:DeviceInstallation': BillLineItemDtoWithPrice | {};
      'PricingItemKeys:Device': BillLineItemDtoWithPrice | {};
    } = {
      'PricingItemKeys:DeviceInstallation': {},
      'PricingItemKeys:Device': {},
    };
    bill.billLineItems.map(v => {
      if (onceItems[v.pricingItemKey]) {
        onceItems[v.pricingItemKey] = v;
      }
    });

    const DeviceInstallation = onceItems[
      'PricingItemKeys:DeviceInstallation'
    ] as BillLineItemDtoWithPrice;
    const Device = onceItems['PricingItemKeys:Device'] as BillLineItemDtoWithPrice;

    const discountDevice = bill.appliedDiscounts.find(v => {
      return v.targetKey == 'PricingItemKeys:Device';
    });
    const discountDeviceInstallation = bill.appliedDiscounts.find(v => {
      return v.targetKey == 'PricingItemKeys:DeviceInstallation';
    });
    const oneTimeItems: BillLineItemDtoWithPrice = {
      pricingItemDisplayName: `${DeviceInstallation.pricingItemDisplayName ?? ''}  ${
        Device.pricingItemDisplayName ? `+ ${Device.pricingItemDisplayName}` : ''
      }`,
      price:
        (Device.unitPrice || 0) * (Device.quantity || 0) +
        (DeviceInstallation.unitPrice || 0) * (DeviceInstallation.quantity || 0),
      unitPrice: 0,
      quantity: 0,
      pricingType: PricingType.OneTime,
    };
    if (discountDevice) {
      oneTimeItems.PriceWithDiscount =
        (Device.unitPrice || 0) *
          (Device.quantity || 0) *
          (discountDevice.isPercentage ? 1 - discountDevice.value : 1) -
        (!discountDevice.isPercentage ? discountDevice.value : 0) +
        (DeviceInstallation.unitPrice || 0) * (DeviceInstallation.quantity || 0);
    }
    if (discountDeviceInstallation) {
      oneTimeItems.PriceWithDiscount =
        (Device.unitPrice || 0) * (Device.quantity || 0) +
        (DeviceInstallation.unitPrice || 0) *
          (DeviceInstallation.quantity || 0) *
          (discountDeviceInstallation.isPercentage ? discountDeviceInstallation.value : 1) -
        (!discountDeviceInstallation.isPercentage ? discountDeviceInstallation.value : 0);
    }
    if (oneTimeItems.PriceWithDiscount < 0) {
      oneTimeItems.PriceWithDiscount = 0;
    }
    if (DeviceInstallation.quantity) {
      oneTimeItems.quantityName = ` ${DeviceInstallation.quantity} `;
      oneTimeItems.unitPriceName = ` ${DeviceInstallation.unitPrice} `;
    }
    if (Device.quantity) {
      oneTimeItems.quantityName =
        (oneTimeItems.quantityName
          ? `${DeviceInstallation.pricingItemDisplayName} ( ${oneTimeItems.quantityName} )` + ' + '
          : '') + `${Device.pricingItemDisplayName} ( ${Device.quantity} )`;
      oneTimeItems.unitPriceName =
        (oneTimeItems.unitPriceName
          ? `${DeviceInstallation.pricingItemDisplayName} ( ${oneTimeItems.unitPriceName} )` + ' + '
          : '') + `${Device.pricingItemDisplayName} ( ${Device.unitPrice} )`;
    }

    bill.billLineItems = bill.billLineItems
      .filter(item => {
        return !['PricingItemKeys:DeviceInstallation', 'PricingItemKeys:Device'].includes(
          item.pricingItemKey
        );
      })
      .map((x: BillLineItemDtoWithPrice) => {
        x.price = x.unitPrice * x.quantity * x.requestedMonths;
        const discount = bill.appliedDiscounts.find(v => {
          return v.targetKey == x.pricingItemKey;
        });
        if (discount) {
          x.PriceWithDiscount = discount.isPercentage
            ? x.price * (1 - discount.value)
            : x.price - discount.value;
        }
        return x;
      });
    if (JSON.stringify(DeviceInstallation) != '{}' && JSON.stringify(DeviceInstallation) != '{}') {
      bill.billLineItems.push(oneTimeItems);
    }
    bill.billLineItems.map((x: BillLineItemDtoWithPrice) => {
      if (r[x.pricingType]) {
        r[x.pricingType]++;
      } else {
        r[x.pricingType] = 1;
      }
    });
    r['all'] = bill.billLineItems.length;
    this.pricingTypeOptionsCounts.set(r);
    bill.appliedDiscounts = bill.appliedDiscounts.filter(d => {
      return !d.targetKey;
    });
    this.bills.set(bill as BillDto & { billLineItems: BillLineItemDtoWithPrice[] });
  }

  closeDialog(data: boolean | undefined = undefined) {
    if (data == undefined) {
      this.dialogRef.close(null);
    } else {
      this.dialogRef.close(true);
    }
  }
}

export const openPriceOfferDialog = (dialog: MatDialog, data: any) => {
  return dialog
    .open(PriceOfferDialogComponent, {
      data: data,
      height: '90%',
    })
    .afterClosed() as Observable<boolean | null>;
};
