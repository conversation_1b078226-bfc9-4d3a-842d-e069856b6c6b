<div class="p-6 bg-white rounded-lg shadow-lg text-sm">
  <h2 class="font-semibold text-indigo-600">
    @if (data.header) {
    {{ data.header | i18n }}
    } @else { @switch (data.type) { @case ("VEHICLE") {
    {{ 'UserPortal:ShowRelatedGroups' | i18n }}
    } @case ("VEHICLE_GROUP") {
    {{ 'UserPortal:ShowRelatedGroups' | i18n }}
    }@case ("Observation") {
    {{ 'UserPortal:ShowRelatedObservation' | i18n }} } }}
  </h2>

  <div class="border-t border-gray-300 my-1 pb-1"></div>

  <div class="space-y-2">
    @for (item of data.items; track item; let i = $index) {
    <div class="flex justify-between items-center p-2 gap-2 border-b">
      @switch (data.type ) { @case ('VEHICLE') {
      <span>
        <mat-icon [ngStyle]="{ color: $any(item).colorHex | hexToColor }">directions_car</mat-icon>
      </span>
      <span class="text-gray-800">{{
        $any(item).licensePlateSerial + ' ' + ($any(item).licensePlateSubClass | i18n)
      }}</span
      >} @case('VEHICLE_GROUP') {
      <span>
        <img [src]="'/assets/images/svg/groups.svg'" class="w-5 h-w-5 fill-red-500" />
      </span>
      <span class="text-gray-800">{{ $any(item).name }}</span>
      } @case ('Observation') {
      <span>
        <img class="size-8" src="/assets/images/sms/مراقب.svg" alt="" />
      </span>
      <div class="flex-grow">
        <div class="text-gray-800">{{ $any(item).name }}</div>
        <div class="text-gray-800">{{ $any(item).phoneNumber }}</div>
      </div>
      }} @if (!!!data.hideAction) {
      <button mat-icon-button (click)="removeVehicle(item)">
        <img src="/assets/images/svg/remove.svg" alt="{{ 'UserPortal:Remove' | i18n }}" />
      </button>
      }
    </div>
    }
  </div>

  <div class="text-right mt-4 text-sm">
    <button mat-button (click)="closeDialog()">
      {{ 'UserPortal:Close' | i18n }}
    </button>
  </div>
</div>
