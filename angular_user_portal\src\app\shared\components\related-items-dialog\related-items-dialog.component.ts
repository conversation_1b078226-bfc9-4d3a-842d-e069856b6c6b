import { Component, inject, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';

import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { VehicleGroupDetailsDto } from '@proxy/mobile/vehicle-groups';
import { NgStyle } from '@angular/common';
import { HexToColorPipe } from '@shared/pipes/hex-to-color.pipe';
import { ObservationViewModelDto } from '@proxy/mobile/observations/dtos';

export interface relatedVihicleData {
  dialog: MatDialog;
  items: VehicleDto[] | VehicleGroupDetailsDto[] | ObservationViewModelDto[];
  type: 'VEHICLE' | 'VEHICLE_GROUP' | 'Observation';
  hideAction?: boolean;
  header?: string;
}
@Component({
  selector: 'app-related-items-dialog',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, LanguagePipe, NgStyle, HexToColorPipe],
  templateUrl: `./related-items-dialog.component.html`,
})
export class RelatedItemsDialogComponent {
  dialogRef = inject(MatDialogRef<RelatedItemsDialogComponent>);
  data = inject<relatedVihicleData>(MAT_DIALOG_DATA);

  removeVehicle(item: VehicleDto | VehicleGroupDetailsDto | ObservationViewModelDto) {
    this.dialogRef.close({
      action: true,
      id:
        this.data.type == 'Observation'
          ? (item as ObservationViewModelDto).vehicleOrGroupId
          : (item as VehicleDto | VehicleGroupDetailsDto).id,
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }
}

export const openRelatedItemsDialog = (data: relatedVihicleData) => {
  const dialogRef = data.dialog.open(RelatedItemsDialogComponent, {
    data: data,
  });
  return dialogRef.afterClosed();
};
