export const tempLocalization = [
  {
    culture: 'en',
    resources: [
      {
        resourceName: 'GoTrack',
        texts: {
          'UserPortal:change account': 'Change account',
          'UserPortal:my requests': 'My requests',
          'UserPortal:virify email': 'Verify email',
          'UserPortal:EmailIsVerifyedpanner':
            'If you want to change your verified email click on change',
          'UserPortal:VerifyEmail': 'Verify Email',
          'UserPortal:EmailIsVerifyed': 'Email Is Verified',
          'UserPortal:EnterEmailToVerify': 'Enter your email address to verify',
          'UserPortal:Email': 'Email',
          'UserPortal:Cancel': 'Cancel',
          'UserPortal:Submit': 'Submit',
          'UserPortal:change': 'Change',
          'UserPortal:Apply': 'Apply',
          'UserPortal:makePaymentSelected': 'Payment Selected',
          'UserPortal:DeleteConfirmation': 'Delete Confirmation',
          'UserPortal:ConfirmMissage': 'Are you sure you want to delete this request?',
          'UserPortal:Delete': 'Delete',
          'UserPortal:Validation_minLength': 'Must have at least one item',
          'UserPortal:promoCode': 'Promo Code',
          'UserPortal:payDialogMessage1':
            'are you sure you want to send a request? click Ok to continue',
          'UserPortal:payDialogMessage2':
            'Upon approval of your request, you can complete the payment electronically through Fatoorah e-payment services, or in cash by contacting the following number:',
          'try again': 'Try Again',
          'price per vehicle': 'Price per vehicle',
          'Delete Confirm': 'Delete Confirm',
          'UserPortal:SelectRoutes': 'Select Routes',
          'UserPortal:ExitingRouteAlert': 'Exiting Route Alert',
          custom: 'Custom (24 hours)',
          fromDate: 'From Date',
          toDate: 'To Date',
          otpIsNotValid: 'the OTP is not valid ',
          Time: 'Time',
          OneTime: 'One Time',
          OnTotal: 'On Total',
          JobTime: 'Job Time',
          active: 'active',
          details: 'Details',
          'Not Active': 'Not Active',
          'UserPortal:DeleteAccount': 'Delete Account',

          'UserPortal:old vehicle': 'Old vehicle',
          'UserPortal:Logout': 'Logout',
          'there is no movement in the selected timeframe':
            'There is no movement in the selected timeframe',
          'Start Drawing': 'Start Drawing',
          'UserPortal:ShowRelatedGroups': 'Related groups',
          'UserPortal:ActivateObserver': 'Activate Observer',

          'UserPortal:newTrackVehiclesCount': 'New Vehicles',
          'UserPortal:removeTrackVehiclesCount': 'Removed Vehicles',
          'UserPortal:lastModificationTime': 'Updated at',
          'UserPortal:ShowRelatedObservation': 'Show Related Observation',
          'UserPortal:newVehicles': 'New Vehicles',
          'UserPortal:RemovedUsers': 'Removed Observers',
          'UserPortal:RemovedVehicles': 'Removed Vehicles',
          'UserPortal:fromDate': 'From Date',
          'UserPortal:toDate': 'To Date',
          'UserPortal:state': 'State',

          'the date can`t be in the future': 'The date can`t be in the future',
          'To date must be after from date': 'To date must be after from date',
          'From date must be before to date': 'From date must be before to date',
          'Date must be within 24 hours': 'Date must be within 24 hours',
          'Please select a time range of up to 24 hours within the last 30 days.':
            'Please select a time range of up to 24 hours within the last 30 days.',
          'Date must be within the last 30 days': 'Date must be within the last 30 days',
          'you Can Only Add One Point': 'you Can Only Add One Point',
          'Please draw a route line with at least 2 points before adding stop points.':
            'Please draw a route line with at least 2 points before adding stop points.',
          'Stop point must be within 30 meters of the route line.':
            'Stop point must be within 30 meters of the route line.',
          'you don`t have access to this part of the application':
            'You don`t have access to this part of the application',
          'Are you sure you want to delete this request':
            'Are you sure you want to delete this request',
          'copy current vehicle location': 'Copy current vehicle location',
          'location failed to copy': 'location failed to copy',
          'location copied to clipboard': 'location copied to clipboard',
          'UserPortal:aboutUs_description1':
            'GoTrack allows you to track your vehicle in real time, wherever you are.The app helps you monitor daily movements, stop locations, and trip details—meeting the needs of both individuals and businesses.',
          'UserPortal:aboutUs_description2':
            'GoTrack offers a complete tracking and management experience with high accuracy, through an easy-to-use interface that enables users to monitor performance, receive alerts, and access detailed reports anytime, anywhere',
        },
      },
    ],
  },
  {
    culture: 'ar',
    resources: [
      {
        resourceName: 'GoTrack',
        texts: {
          'UserPortal:change account': 'تغيير الحساب',
          'UserPortal:my requests': 'طلباتي',
          'UserPortal:virify email': 'تحقق البريد الإلكتروني',
          'UserPortal:EmailIsVerifyedpanner':
            'إذا كنت ترغب في تغيير بريدك الالكتروني المعتمد اضغط على تغيير ',
          'UserPortal:VerifyEmail': 'تحقق من البريد الإلكتروني',
          'UserPortal:EmailIsVerifyed': 'تم التحقق من البريد الإلكتروني',
          'UserPortal:EnterEmailToVerify': 'أدخل عنوان بريدك الإلكتروني للتحقق',
          'UserPortal:Email': 'البريد الإلكتروني',
          'UserPortal:Cancel': 'إلغاء',
          'UserPortal:Submit': 'إرسال',
          'UserPortal:change': 'تغيير',
          'UserPortal:Apply': 'تطبيق',
          'UserPortal:makePaymentSelected': 'دفع المحدد',
          'UserPortal:Validation_minLength': 'يجب أن يحتوي على عنصر واحد على الأقل',
          'UserPortal:DeleteConfirmation': 'تأكيد الحذف',
          'UserPortal:ConfirmMissage': 'هل أنت متأكد أنك تريد حذف هذا الطلب؟',
          'UserPortal:Delete': 'حذف',
          'price per vehicle': 'رسوم الباقة للمركبة',
          'UserPortal:promoCode': 'كود الحسم ',
          'UserPortal:payDialogMessage1':
            'هل أنت متأكد أنك تريد إرسال الطلب؟ انقر فوق موافق للمتابعة',
          'UserPortal:payDialogMessage2':
            'عند الموافقة على طلبك، يمكنك إتمام الدفع إلكترونيًا من خلال خدمات فاتورة للدفع الإلكتروني، أو نقدًا عبر التواصل على الرقم التالي:',
          'try again': 'حاول مرة أخرى',
          'Delete Confirm': 'تأكيد الحذف',
          'UserPortal:SelectRoutes': 'اختر مسار',
          'UserPortal:ShowRelatedObservation': 'عرض المراقبين',
          'UserPortal:newVehicles': 'المركبات الجديدة',
          'UserPortal:RemovedUsers': 'المراقبين المحذوفين',
          'UserPortal:RemovedVehicles': 'المركبات المحذوفة',
          'UserPortal:fromDate': 'تاريخ الانشاء',
          'UserPortal:toDate': 'تاريخ الانتهاء',
          'UserPortal:state': 'الحالة',

          'copy current vehicle location': 'نسخ موقع المركبة الحالية',
          custom: 'مخصص (24 ساعة)',
          otpIsNotValid: 'رمز التأكيد ليس صالح',
          fromDate: 'من تاريخ',
          toDate: 'إلى تاريخ',
          Time: 'الوقت',
          OneTime: 'مرة واحدة',
          JobTime: 'وقت العمل ',
          active: 'فعال',
          'Not Active': 'غير فعال ',
          'UserPortal:DeleteAccount': 'حذف حساب',
          OnTotal: 'على الإجمالي',
          details: 'تفاصيل',
          'UserPortal:lastModificationTime': 'اخر تعديل',
          'UserPortal:newTrackVehiclesCount': 'عدد المركبات الجديدة',
          'UserPortal:removeTrackVehiclesCount': 'عدد المركبات المحذوفة',
          'UserPortal:ActivateObserver': 'تفعيل المراقب',
          'UserPortal:ShowRelatedGroups': 'المجموعات المسندة ',
          'UserPortal:Logout': 'تسجيل خروج ',
          'there is no movement in the selected timeframe':
            'لا يوجد أي حركة ضمن الايطار الزمني الذي اخترته ',
          'UserPortal:old vehicle': 'المركبات المضافة مسبقا',
          'Start Drawing': 'بدء الرسم',
          'the date can`t be in the future': 'لا يمكن أن يكون التاريخ في المستقبل',
          'To date must be after from date': 'يجب أن يكون تاريخ النهاية بعد تاريخ البداية',
          'From date must be before to date': 'يجب أن يكون تاريخ البداية قبل تاريخ النهاية',
          'Date must be within 24 hours': 'يجب أن يكون التاريخ خلال ال 24 ساعة ',
          'Date must be within the last 30 days': 'يجب أن يكون التاريخ خلال ال 30 يوم ',
          'Please select a time range of up to 24 hours within the last 30 days.':
            'الرجاء اختيار فترة لا تزيد عن 24 ساعة خلال آخر 30 يوم.',
          'you Can Only Add One Point': 'لا يمكنك إضافة أكثر من نقطة دفعة واحدة ',
          'Please draw a route line with at least 2 points before adding stop points.':
            'يرجى رسم خط المسار بنقطتين على الأقل قبل إضافة نقاط التوقف',
          'Stop point must be within 30 meters of the route line.':
            'يجب أن تكون نقطة التوقف في حدود 10 أمتار من خط المسار',
          'UserPortal:ExitingRouteAlert': 'انذار خروج عن المسار',
          'you don`t have access to this part of the application':
            'ليس لديك وصول إلى هذا الجزء من التطبيق ',
          'Are you sure you want to delete this request': 'هل أنت متأكد أنك تريد حذف هذا الطلب',
          'location failed to copy': 'فشل نسخ الموقع',
          'location copied to clipboard': 'تم نسخ الموقع',
          'UserPortal:aboutUs_description1':
            'يتيح لك تطبيق GoTrack  تتبع مركبتك لحظة بلحظة، أينما كنت . يساعدك التطبيق على مراقبة الحركة اليومية، ومواقع التوقف، وتفاصيل الرحلات، لتلبية احتياجات الأفراد والشركات.',
          'UserPortal:aboutUs_description2':
            'يُوفر Go Track تجربة متكاملة للإدارة والتتبّع بدقة عالية، عبر واجهة سهلة الاستخدام تتيح للمستخدمين مراقبة الأداء، استلام التنبيهات، واستعراض التقارير التفصيلية في أي وقت ومن أي مكان.',
        },
      },
    ],
  },
];
