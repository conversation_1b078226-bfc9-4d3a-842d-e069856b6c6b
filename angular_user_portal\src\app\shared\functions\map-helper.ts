import { FeatureGroup, latLng, LatLng, LatLngExpression, LatLngTuple, Marker } from 'leaflet';

export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

export function getMinDistanceToPolyline(
  markerLat: number,
  markerLng: number,
  polylinePoints: any[]
): number {
  if (!polylinePoints || polylinePoints.length === 0) {
    return Infinity;
  }

  let minDistance = Infinity;

  // Check distance to each point in the polyline
  for (const point of polylinePoints) {
    const distance = calculateDistance(markerLat, markerLng, point.latitudeY, point.longitudeX);
    minDistance = Math.min(minDistance, distance);
  }

  // Check distance to each line segment
  for (let i = 0; i < polylinePoints.length - 1; i++) {
    const segmentDistance = getDistanceToLineSegment(
      markerLat,
      markerLng,
      polylinePoints[i].latitudeY,
      polylinePoints[i].longitudeX,
      polylinePoints[i + 1].latitudeY,
      polylinePoints[i + 1].longitudeX
    );
    minDistance = Math.min(minDistance, segmentDistance);
  }

  return minDistance;
}

export function getDistanceToLineSegment(
  px: number,
  py: number,
  x1: number,
  y1: number,
  x2: number,
  y2: number
): number {
  const A = px - x1;
  const B = py - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  let param = -1;
  if (lenSq !== 0) {
    param = dot / lenSq;
  }

  let xx: number, yy: number;

  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  return calculateDistance(px, py, xx, yy);
}

export function removeLayer(layers: FeatureGroup, lat: number, lng: number) {
  layers.getLayers().forEach((layer: any) => {
    if (
      layer._latlng &&
      layer._latlng.lat == lat &&
      layer._latlng.lng == lng &&
      layer instanceof Marker
    ) {
      layers.removeLayer((layer as any)._leaflet_id);
    }
  });
}

export function circleToPolygon(
  center: LatLngExpression,
  radius: number,
  sides: number
): LatLngTuple[] {
  const latlng = latLng(center);
  const points: LatLngTuple[] = [];
  for (let i = 0; i < sides; i++) {
    const angle = (i * 2 * Math.PI) / sides; // Calculate the angle for each point
    const point = calculateOffset(latlng.lat, latlng.lng, radius, angle);
    points.push([point.lat, point.lng]);
  }
  return points;
}
export function calculateOffset(lat: number, lng: number, radius: number, angle: number): LatLng {
  const earthRadius = 6378137; // Earth's radius in meters
  const deltaLat = (radius / earthRadius) * Math.cos(angle);
  const deltaLng = (radius / (earthRadius * Math.cos((lat * Math.PI) / 180))) * Math.sin(angle);
  return latLng(lat + (deltaLat * 180) / Math.PI, lng + (deltaLng * 180) / Math.PI);
}
